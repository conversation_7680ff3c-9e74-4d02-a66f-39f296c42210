# Generated by Django 5.2.4 on 2025-07-14 08:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authentication", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="emailverificationtoken",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="email_verification_tokens",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="loginattempt",
            index=models.Index(
                fields=["email", "attempted_at"], name="auth_login__email_c4fbee_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="loginattempt",
            index=models.Index(
                fields=["ip_address", "attempted_at"],
                name="auth_login__ip_addr_0fb781_idx",
            ),
        ),
        migrations.AddField(
            model_name="passwordresettoken",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="password_reset_tokens",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="refreshtoken",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="refresh_tokens",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="emailverificationtoken",
            index=models.Index(fields=["token"], name="auth_email__token_247a2c_idx"),
        ),
        migrations.AddIndex(
            model_name="emailverificationtoken",
            index=models.Index(
                fields=["expires_at"], name="auth_email__expires_8a24ed_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="passwordresettoken",
            index=models.Index(fields=["token"], name="auth_passwo_token_076a22_idx"),
        ),
        migrations.AddIndex(
            model_name="passwordresettoken",
            index=models.Index(
                fields=["expires_at"], name="auth_passwo_expires_ca8703_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="refreshtoken",
            index=models.Index(
                fields=["user", "is_revoked"], name="auth_refres_user_id_6c5f7d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="refreshtoken",
            index=models.Index(
                fields=["expires_at"], name="auth_refres_expires_4ad26b_idx"
            ),
        ),
    ]
