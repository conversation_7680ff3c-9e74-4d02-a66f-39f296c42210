"""
GraphQL 自动生成器
基于 Django 模型自动生成 GraphQL Schema 和 Resolvers
"""
from typing import Type, Dict, Any, List, Optional
from django.db import models
from django.apps import apps
import graphene
from graphene_django import DjangoObjectType
from graphene_django.filter import DjangoFilterConnectionField
from graphene import relay
import inspect


class GraphQLSchemaGenerator:
    """GraphQL Schema 自动生成器"""
    
    def __init__(self):
        self.object_types = {}
        self.mutations = {}
        self.queries = {}
        self.filters = {}
    
    def generate_schema_for_app(self, app_name: str) -> str:
        """为应用生成 GraphQL Schema"""
        try:
            app_config = apps.get_app_config(app_name)
        except LookupError:
            raise ValueError(f"应用 {app_name} 不存在")
        
        models_list = app_config.get_models()
        
        # 生成 ObjectTypes
        for model in models_list:
            self._generate_object_type(model)
        
        # 生成 Queries
        self._generate_queries(models_list)
        
        # 生成 Mutations
        self._generate_mutations(models_list)
        
        # 生成完整的 Schema 代码
        return self._generate_schema_code(app_name)
    
    def _generate_object_type(self, model: Type[models.Model]) -> str:
        """生成 ObjectType"""
        model_name = model.__name__
        
        # 获取模型字段
        fields = self._get_model_fields(model)
        
        object_type_code = f'''
class {model_name}Type(DjangoObjectType):
    """
    {model._meta.verbose_name} GraphQL Type
    """
    
    class Meta:
        model = {model_name}
        fields = {fields}
        interfaces = (relay.Node,)
        filter_fields = {self._get_filter_fields(model)}
        connection_class = graphene.Connection
    
    @classmethod
    def get_queryset(cls, queryset, info):
        """优化查询集"""
        # 自动添加 select_related 和 prefetch_related
        {self._generate_queryset_optimization(model)}
        return queryset
'''
        
        self.object_types[model_name] = object_type_code
        return object_type_code
    
    def _generate_queries(self, models: List[Type[models.Model]]):
        """生成查询"""
        queries = []
        
        for model in models:
            model_name = model.__name__
            model_name_lower = model_name.lower()
            model_name_plural = f"{model_name_lower}s"
            
            # 单个查询
            single_query = f'''
    {model_name_lower} = graphene.Field(
        {model_name}Type,
        id=graphene.ID(required=True),
        description="获取单个{model._meta.verbose_name}"
    )
    
    def resolve_{model_name_lower}(self, info, id):
        """解析单个{model._meta.verbose_name}"""
        try:
            return {model_name}.objects.get(pk=id)
        except {model_name}.DoesNotExist:
            return None'''
            
            # 列表查询
            list_query = f'''
    all_{model_name_plural} = DjangoFilterConnectionField(
        {model_name}Type,
        description="获取{model._meta.verbose_name}列表"
    )'''
            
            queries.extend([single_query, list_query])
        
        self.queries = '\n'.join(queries)
    
    def _generate_mutations(self, models: List[Type[models.Model]]):
        """生成变更"""
        mutations = []
        mutation_classes = []
        
        for model in models:
            model_name = model.__name__
            
            # 创建变更
            create_mutation = self._generate_create_mutation(model)
            mutations.append(create_mutation)
            
            # 更新变更
            update_mutation = self._generate_update_mutation(model)
            mutations.append(update_mutation)
            
            # 删除变更
            delete_mutation = self._generate_delete_mutation(model)
            mutations.append(delete_mutation)
            
            # 添加到变更类列表
            mutation_classes.extend([
                f'create_{model_name.lower()} = Create{model_name}Mutation.Field()',
                f'update_{model_name.lower()} = Update{model_name}Mutation.Field()',
                f'delete_{model_name.lower()} = Delete{model_name}Mutation.Field()',
            ])
        
        self.mutations = {
            'classes': '\n'.join(mutations),
            'fields': '\n    '.join(mutation_classes)
        }
    
    def _generate_create_mutation(self, model: Type[models.Model]) -> str:
        """生成创建变更"""
        model_name = model.__name__
        input_fields = self._get_input_fields(model, exclude_readonly=True)
        
        return f'''
class Create{model_name}Input(graphene.InputObjectType):
    """创建{model._meta.verbose_name}输入"""
    {input_fields}

class Create{model_name}Mutation(graphene.Mutation):
    """创建{model._meta.verbose_name}变更"""
    
    class Arguments:
        input = Create{model_name}Input(required=True)
    
    {model_name.lower()} = graphene.Field({model_name}Type)
    success = graphene.Boolean()
    errors = graphene.List(graphene.String)
    
    @staticmethod
    def mutate(root, info, input=None):
        """执行创建操作"""
        try:
            # 权限检查
            user = info.context.user
            if not user.is_authenticated:
                return Create{model_name}Mutation(
                    success=False,
                    errors=["需要登录"]
                )
            
            # 创建对象
            {model_name.lower()}_data = dict(input)
            {model_name.lower()}_instance = {model_name}.objects.create(**{model_name.lower()}_data)
            
            return Create{model_name}Mutation(
                {model_name.lower()}={model_name.lower()}_instance,
                success=True
            )
        except Exception as e:
            return Create{model_name}Mutation(
                success=False,
                errors=[str(e)]
            )'''
    
    def _generate_update_mutation(self, model: Type[models.Model]) -> str:
        """生成更新变更"""
        model_name = model.__name__
        input_fields = self._get_input_fields(model, exclude_readonly=True, required=False)
        
        return f'''
class Update{model_name}Input(graphene.InputObjectType):
    """更新{model._meta.verbose_name}输入"""
    id = graphene.ID(required=True)
    {input_fields}

class Update{model_name}Mutation(graphene.Mutation):
    """更新{model._meta.verbose_name}变更"""
    
    class Arguments:
        input = Update{model_name}Input(required=True)
    
    {model_name.lower()} = graphene.Field({model_name}Type)
    success = graphene.Boolean()
    errors = graphene.List(graphene.String)
    
    @staticmethod
    def mutate(root, info, input=None):
        """执行更新操作"""
        try:
            # 权限检查
            user = info.context.user
            if not user.is_authenticated:
                return Update{model_name}Mutation(
                    success=False,
                    errors=["需要登录"]
                )
            
            # 获取对象
            {model_name.lower()}_instance = {model_name}.objects.get(pk=input.id)
            
            # 更新字段
            for field, value in input.items():
                if field != 'id' and value is not None:
                    setattr({model_name.lower()}_instance, field, value)
            
            {model_name.lower()}_instance.save()
            
            return Update{model_name}Mutation(
                {model_name.lower()}={model_name.lower()}_instance,
                success=True
            )
        except {model_name}.DoesNotExist:
            return Update{model_name}Mutation(
                success=False,
                errors=["{model._meta.verbose_name}不存在"]
            )
        except Exception as e:
            return Update{model_name}Mutation(
                success=False,
                errors=[str(e)]
            )'''
    
    def _generate_delete_mutation(self, model: Type[models.Model]) -> str:
        """生成删除变更"""
        model_name = model.__name__
        
        return f'''
class Delete{model_name}Mutation(graphene.Mutation):
    """删除{model._meta.verbose_name}变更"""
    
    class Arguments:
        id = graphene.ID(required=True)
    
    success = graphene.Boolean()
    errors = graphene.List(graphene.String)
    
    @staticmethod
    def mutate(root, info, id):
        """执行删除操作"""
        try:
            # 权限检查
            user = info.context.user
            if not user.is_authenticated:
                return Delete{model_name}Mutation(
                    success=False,
                    errors=["需要登录"]
                )
            
            # 删除对象
            {model_name.lower()}_instance = {model_name}.objects.get(pk=id)
            {model_name.lower()}_instance.delete()
            
            return Delete{model_name}Mutation(success=True)
        except {model_name}.DoesNotExist:
            return Delete{model_name}Mutation(
                success=False,
                errors=["{model._meta.verbose_name}不存在"]
            )
        except Exception as e:
            return Delete{model_name}Mutation(
                success=False,
                errors=[str(e)]
            )'''
    
    def _get_model_fields(self, model: Type[models.Model]) -> str:
        """获取模型字段列表"""
        fields = []
        for field in model._meta.fields:
            if field.name not in ['password']:
                fields.append(f"'{field.name}'")
        
        return f"[{', '.join(fields)}]"
    
    def _get_filter_fields(self, model: Type[models.Model]) -> Dict[str, List[str]]:
        """获取过滤字段"""
        filter_fields = {}
        
        for field in model._meta.fields:
            if isinstance(field, models.CharField):
                filter_fields[field.name] = ['exact', 'icontains', 'istartswith']
            elif isinstance(field, models.IntegerField):
                filter_fields[field.name] = ['exact', 'lt', 'gt', 'lte', 'gte']
            elif isinstance(field, models.BooleanField):
                filter_fields[field.name] = ['exact']
            elif isinstance(field, models.DateTimeField):
                filter_fields[field.name] = ['exact', 'lt', 'gt', 'lte', 'gte', 'year', 'month', 'day']
            elif isinstance(field, models.ForeignKey):
                filter_fields[field.name] = ['exact']
        
        return str(filter_fields)
    
    def _get_input_fields(self, model: Type[models.Model], exclude_readonly: bool = True, required: bool = True) -> str:
        """获取输入字段"""
        fields = []
        
        for field in model._meta.fields:
            if exclude_readonly and (isinstance(field, models.AutoField) or 
                                   field.auto_now or field.auto_now_add or
                                   field.name in ['created_at', 'updated_at']):
                continue
            
            graphql_type = self._get_graphql_type(field)
            required_str = '(required=True)' if required and not field.null else ''
            
            fields.append(f"    {field.name} = {graphql_type}{required_str}")
        
        return '\n'.join(fields)
    
    def _get_graphql_type(self, field) -> str:
        """获取 GraphQL 类型"""
        if isinstance(field, models.CharField):
            return 'graphene.String'
        elif isinstance(field, models.TextField):
            return 'graphene.String'
        elif isinstance(field, models.IntegerField):
            return 'graphene.Int'
        elif isinstance(field, models.FloatField):
            return 'graphene.Float'
        elif isinstance(field, models.BooleanField):
            return 'graphene.Boolean'
        elif isinstance(field, models.DateTimeField):
            return 'graphene.DateTime'
        elif isinstance(field, models.DateField):
            return 'graphene.Date'
        elif isinstance(field, models.EmailField):
            return 'graphene.String'
        elif isinstance(field, models.URLField):
            return 'graphene.String'
        elif isinstance(field, models.ForeignKey):
            return 'graphene.ID'
        elif isinstance(field, models.JSONField):
            return 'graphene.JSONString'
        else:
            return 'graphene.String'
    
    def _generate_queryset_optimization(self, model: Type[models.Model]) -> str:
        """生成查询集优化代码"""
        select_related = []
        prefetch_related = []
        
        for field in model._meta.fields:
            if isinstance(field, models.ForeignKey):
                select_related.append(f"'{field.name}'")
        
        for field in model._meta.many_to_many:
            prefetch_related.append(f"'{field.name}'")
        
        optimization_code = []
        
        if select_related:
            optimization_code.append(f"        queryset = queryset.select_related({', '.join(select_related)})")
        
        if prefetch_related:
            optimization_code.append(f"        queryset = queryset.prefetch_related({', '.join(prefetch_related)})")
        
        return '\n'.join(optimization_code) if optimization_code else "        pass"
    
    def _generate_schema_code(self, app_name: str) -> str:
        """生成完整的 Schema 代码"""
        return f'''"""
{app_name.title()} GraphQL Schema
自动生成的 GraphQL Schema 和 Resolvers
"""
import graphene
from graphene_django import DjangoObjectType
from graphene_django.filter import DjangoFilterConnectionField
from graphene import relay
from django.contrib.auth import get_user_model
from .models import *

User = get_user_model()

# ObjectTypes
{''.join(self.object_types.values())}

# Mutations
{self.mutations['classes']}

class Query(graphene.ObjectType):
    """GraphQL 查询"""
    {self.queries}

class Mutation(graphene.ObjectType):
    """GraphQL 变更"""
    {self.mutations['fields']}

# Schema
schema = graphene.Schema(query=Query, mutation=Mutation)
'''


# 便捷函数
def generate_graphql_schema(app_name: str) -> str:
    """生成 GraphQL Schema 的便捷函数"""
    generator = GraphQLSchemaGenerator()
    return generator.generate_schema_for_app(app_name)
