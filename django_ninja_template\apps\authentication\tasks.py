"""
Authentication Celery tasks.
"""
import logging
from datetime import timedelta
from django.utils import timezone
from celery import shared_task
from .models import RefreshToken, PasswordResetToken, EmailVerificationToken, LoginAttempt

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def cleanup_expired_tokens(self):
    """
    Clean up expired tokens and old login attempts.
    """
    try:
        now = timezone.now()
        
        # Clean up expired refresh tokens
        expired_refresh_tokens = RefreshToken.objects.filter(expires_at__lt=now)
        refresh_count = expired_refresh_tokens.count()
        expired_refresh_tokens.delete()
        
        # Clean up expired password reset tokens
        expired_password_tokens = PasswordResetToken.objects.filter(expires_at__lt=now)
        password_count = expired_password_tokens.count()
        expired_password_tokens.delete()
        
        # Clean up expired email verification tokens
        expired_email_tokens = EmailVerificationToken.objects.filter(expires_at__lt=now)
        email_count = expired_email_tokens.count()
        expired_email_tokens.delete()
        
        # Clean up old login attempts (older than 30 days)
        cutoff_date = now - timedelta(days=30)
        old_login_attempts = LoginAttempt.objects.filter(attempted_at__lt=cutoff_date)
        login_count = old_login_attempts.count()
        old_login_attempts.delete()
        
        total_cleaned = refresh_count + password_count + email_count + login_count
        
        logger.info(
            f"Cleaned up expired tokens: {refresh_count} refresh, "
            f"{password_count} password reset, {email_count} email verification, "
            f"{login_count} old login attempts"
        )
        
        return f"Cleaned up {total_cleaned} expired tokens and old records"
        
    except Exception as exc:
        logger.error(f"Error cleaning up expired tokens: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True, max_retries=3)
def send_password_reset_email(self, user_id, reset_token, reset_url):
    """
    Send password reset email to user.
    """
    try:
        from django.contrib.auth import get_user_model
        from django.core.mail import send_mail
        from django.conf import settings
        
        User = get_user_model()
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return "User not found"
        
        subject = "Password Reset Request"
        message = f"""
        Hello {user.get_full_name()},
        
        You have requested a password reset for your account.
        
        Please click the following link to reset your password:
        {reset_url}?token={reset_token}
        
        This link will expire in 1 hour.
        
        If you did not request this password reset, please ignore this email.
        
        Best regards,
        The Team
        """
        
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )
        
        logger.info(f"Sent password reset email to {user.email}")
        return f"Password reset email sent to {user.email}"
        
    except Exception as exc:
        logger.error(f"Error sending password reset email: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True, max_retries=3)
def send_email_verification(self, user_id, verification_token, verification_url):
    """
    Send email verification to user.
    """
    try:
        from django.contrib.auth import get_user_model
        from django.core.mail import send_mail
        from django.conf import settings
        
        User = get_user_model()
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return "User not found"
        
        subject = "Email Verification Required"
        message = f"""
        Hello {user.get_full_name()},
        
        Thank you for registering with us!
        
        Please click the following link to verify your email address:
        {verification_url}?token={verification_token}
        
        This link will expire in 24 hours.
        
        If you did not create this account, please ignore this email.
        
        Best regards,
        The Team
        """
        
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )
        
        logger.info(f"Sent email verification to {user.email}")
        return f"Email verification sent to {user.email}"
        
    except Exception as exc:
        logger.error(f"Error sending email verification: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True)
def log_security_event(self, event_type, user_id=None, ip_address=None, details=None):
    """
    Log security events asynchronously.
    """
    try:
        from apps.core.tasks import create_audit_log
        
        # Create audit log for security event
        create_audit_log.delay(
            user_id=user_id,
            action=f"security_{event_type}",
            resource_type="security",
            details=details or {},
            ip_address=ip_address
        )
        
        logger.info(f"Logged security event: {event_type}")
        return f"Security event logged: {event_type}"
        
    except Exception as exc:
        logger.error(f"Error logging security event: {exc}")
        raise


@shared_task(bind=True, max_retries=3)
def check_suspicious_activity(self):
    """
    Check for suspicious login activity and send alerts.
    """
    try:
        from django.db.models import Count
        from datetime import timedelta
        
        # Check for multiple failed login attempts from same IP
        cutoff_time = timezone.now() - timedelta(hours=1)
        
        suspicious_ips = LoginAttempt.objects.filter(
            attempted_at__gte=cutoff_time,
            success=False
        ).values('ip_address').annotate(
            attempt_count=Count('id')
        ).filter(attempt_count__gte=10)
        
        alerts_sent = 0
        for ip_data in suspicious_ips:
            ip_address = ip_data['ip_address']
            attempt_count = ip_data['attempt_count']
            
            # Log security event
            log_security_event.delay(
                event_type="suspicious_login_attempts",
                ip_address=ip_address,
                details={
                    "attempt_count": attempt_count,
                    "time_window": "1_hour"
                }
            )
            
            alerts_sent += 1
        
        logger.info(f"Checked suspicious activity, sent {alerts_sent} alerts")
        return f"Sent {alerts_sent} security alerts"
        
    except Exception as exc:
        logger.error(f"Error checking suspicious activity: {exc}")
        raise self.retry(exc=exc, countdown=300)  # Retry after 5 minutes
