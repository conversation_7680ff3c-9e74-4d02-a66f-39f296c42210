#!/usr/bin/env python
"""
Django Ninja 脚手架项目数据库设置脚本
负责数据库清理、迁移和基础数据创建
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.development')

class DatabaseSetup:
    """数据库设置器"""
    
    def __init__(self):
        print("🔧 Django Ninja 脚手架项目数据库设置")
        print("=" * 50)
    
    def step_1_clean_database(self):
        """步骤1: 清理数据库"""
        print("\n📝 步骤1: 清理数据库文件")
        
        try:
            # 删除数据库文件
            db_file = project_root / "db.sqlite3"
            if db_file.exists():
                try:
                    db_file.unlink()
                    print("  ✅ 删除数据库文件成功")
                except PermissionError:
                    print("  ⚠️ 数据库文件被占用")
                    print("  💡 请手动删除 db.sqlite3 文件，或者:")
                    print("     1. 停止Django开发服务器")
                    print("     2. 关闭所有数据库连接")
                    print("     3. 重新运行此脚本")
                    return False
            else:
                print("  ✅ 数据库文件不存在，无需删除")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 数据库清理失败: {e}")
            return False
    
    def step_2_setup_django(self):
        """步骤2: 设置Django环境"""
        print("\n🔧 步骤2: 设置Django环境")
        
        try:
            import django
            django.setup()
            
            from django.conf import settings
            from django.apps import apps
            
            print(f"  ✅ Django版本: {django.get_version()}")
            print(f"  ✅ 设置模块: {settings.SETTINGS_MODULE}")
            print(f"  ✅ 自定义用户模型: {settings.AUTH_USER_MODEL}")
            
            # 检查应用
            local_apps = [app for app in settings.INSTALLED_APPS if app.startswith('apps.')]
            print(f"  ✅ 本地应用数量: {len(local_apps)}")
            for app in local_apps:
                print(f"    - {app}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Django环境设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def step_3_create_migrations(self):
        """步骤3: 创建迁移文件"""
        print("\n📝 步骤3: 创建迁移文件")
        
        try:
            from django.core.management import call_command
            
            # 生成迁移文件
            print("  🔄 生成迁移文件...")
            call_command('makemigrations', verbosity=1)
            
            # 检查生成的迁移文件
            apps_dir = project_root / 'apps'
            migration_count = 0
            
            for app_dir in apps_dir.iterdir():
                if app_dir.is_dir() and app_dir.name != '__pycache__':
                    migrations_dir = app_dir / 'migrations'
                    if migrations_dir.exists():
                        migration_files = list(migrations_dir.glob('*.py'))
                        migration_files = [f for f in migration_files if f.name != '__init__.py']
                        if migration_files:
                            print(f"    ✅ {app_dir.name}: {len(migration_files)} 个迁移文件")
                            for mf in migration_files:
                                print(f"      - {mf.name}")
                            migration_count += len(migration_files)
                        else:
                            print(f"    ⚠️ {app_dir.name}: 无迁移文件")
            
            if migration_count > 0:
                print(f"  ✅ 总共生成 {migration_count} 个迁移文件")
                return True
            else:
                print("  ⚠️ 没有生成迁移文件，可能模型没有变化")
                return True  # 这不算失败
            
        except Exception as e:
            print(f"  ❌ 创建迁移文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def step_4_apply_migrations(self):
        """步骤4: 应用迁移"""
        print("\n🔄 步骤4: 应用迁移")
        
        try:
            from django.core.management import call_command
            from django.db import connection
            
            # 应用迁移
            print("  🔄 应用迁移...")
            call_command('migrate', verbosity=1)
            
            # 检查创建的表
            with connection.cursor() as cursor:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
            
            print(f"  ✅ 创建了 {len(tables)} 个数据库表")
            
            # 显示所有表
            print("  📋 数据库表列表:")
            for table in sorted(tables):
                print(f"    - {table}")
            
            # 检查关键表
            expected_tables = [
                'users', 
                'auth_refresh_tokens', 
                'permissions_permission', 
                'storage_storagefile', 
                'i18n_languages',
            ]
            found_tables = []
            missing_tables = []
            
            print("\n  🔍 检查关键表:")
            for table in expected_tables:
                if table in tables:
                    found_tables.append(table)
                    print(f"    ✅ {table}")
                else:
                    missing_tables.append(table)
                    print(f"    ❌ {table} (缺失)")
            
            if len(found_tables) >= 1:  # 至少要有1个关键表
                print(f"  ✅ 关键表创建成功 ({len(found_tables)}/{len(expected_tables)})")
                return True
            else:
                print(f"  ⚠️ 关键表缺失较多，但继续执行")
                return True
                
        except Exception as e:
            print(f"  ❌ 应用迁移失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def step_5_create_test_data(self):
        """步骤5: 创建测试数据"""
        print("\n👤 步骤5: 创建测试数据")
        
        try:
            from django.contrib.auth import get_user_model
            
            User = get_user_model()
            
            # 创建测试用户
            test_user, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'username': 'testuser',
                    'first_name': 'Test',
                    'last_name': 'User',
                    'is_active': True
                }
            )
            
            if created:
                test_user.set_password('testpass123')
                test_user.save()
                print("  ✅ 测试用户创建成功")
            else:
                print("  ✅ 测试用户已存在")
            
            # 创建管理员用户
            admin_user, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'username': 'admin',
                    'first_name': 'Admin',
                    'last_name': 'User',
                    'is_active': True,
                    'is_staff': True,
                    'is_superuser': True
                }
            )
            
            if created:
                admin_user.set_password('admin123')
                admin_user.save()
                print("  ✅ 管理员用户创建成功")
            else:
                print("  ✅ 管理员用户已存在")
            
            # 创建其他测试数据
            self._create_additional_test_data()
            
            print(f"  📊 用户统计: 总共 {User.objects.count()} 个用户")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 创建测试数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _create_additional_test_data(self):
        """创建额外的测试数据"""
        try:
            # 创建系统配置
            try:
                from apps.core.models import SystemConfiguration
                config, created = SystemConfiguration.objects.get_or_create(
                    key='test_config',
                    defaults={
                        'value': 'test_value',
                        'description': 'Test configuration for validation'
                    }
                )
                if created:
                    print("    ✅ 系统配置创建成功")
            except Exception as e:
                print(f"    ⚠️ 系统配置创建失败: {e}")
            
            # 创建语言
            try:
                from apps.i18n.models import Language
                lang_zh, created = Language.objects.get_or_create(
                    code='zh-cn',
                    defaults={
                        'name': 'Chinese (Simplified)',
                        'native_name': '简体中文',
                        'is_active': True
                    }
                )
                if created:
                    print("    ✅ 中文语言创建成功")
                
                lang_en, created = Language.objects.get_or_create(
                    code='en',
                    defaults={
                        'name': 'English',
                        'native_name': 'English',
                        'is_active': True,
                        'is_default': True
                    }
                )
                if created:
                    print("    ✅ 英文语言创建成功")
            except Exception as e:
                print(f"    ⚠️ 语言创建失败: {e}")
            
            # 创建文件分类
            try:
                from apps.storage.models import FileCategory
                category, created = FileCategory.objects.get_or_create(
                    name='测试分类',
                    defaults={
                        'description': '用于测试的文件分类'
                    }
                )
                if created:
                    print("    ✅ 文件分类创建成功")
            except Exception as e:
                print(f"    ⚠️ 文件分类创建失败: {e}")
            
            # 创建基础权限
            try:
                from apps.permissions.models import Permission
                permissions_data = [
                    ('users.view', 'View Users', 'users', 'view'),
                    ('users.add', 'Add Users', 'users', 'add'),
                    ('users.change', 'Change Users', 'users', 'change'),
                    ('users.delete', 'Delete Users', 'users', 'delete'),
                ]
                
                created_count = 0
                for codename, name, resource, action in permissions_data:
                    perm, created = Permission.objects.get_or_create(
                        codename=codename,
                        defaults={
                            'name': name,
                            'resource': resource,
                            'action': action
                        }
                    )
                    if created:
                        created_count += 1
                
                if created_count > 0:
                    print(f"    ✅ 创建了 {created_count} 个基础权限")
            except Exception as e:
                print(f"    ⚠️ 权限创建失败: {e}")
            
        except Exception as e:
            print(f"    ⚠️ 创建额外测试数据时出现问题: {e}")
    
    def step_6_verify_setup(self):
        """步骤6: 验证设置"""
        print("\n✅ 步骤6: 验证数据库设置")
        
        try:
            from django.db import connection
            from django.contrib.auth import get_user_model
            
            # 检查数据库连接
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            print("  ✅ 数据库连接正常")
            
            # 检查用户模型
            User = get_user_model()
            user_count = User.objects.count()
            print(f"  ✅ 用户模型正常，用户数量: {user_count}")
            
            # 检查测试用户
            try:
                test_user = User.objects.get(email='<EMAIL>')
                print("  ✅ 测试用户存在")
            except User.DoesNotExist:
                print("  ❌ 测试用户不存在")
                return False
            
            # 检查管理员用户
            try:
                admin_user = User.objects.get(email='<EMAIL>')
                if admin_user.is_superuser:
                    print("  ✅ 管理员用户存在且权限正确")
                else:
                    print("  ⚠️ 管理员用户存在但权限不正确")
            except User.DoesNotExist:
                print("  ❌ 管理员用户不存在")
                return False
            
            print("  ✅ 数据库设置验证通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 验证设置失败: {e}")
            return False

    
    def run_setup(self):
        """运行数据库设置流程"""
        steps = [
            ("清理数据库文件", self.step_1_clean_database),
            ("设置Django环境", self.step_2_setup_django),
            ("创建迁移文件", self.step_3_create_migrations),
            ("应用迁移", self.step_4_apply_migrations),
            ("创建测试数据", self.step_5_create_test_data),
            ("验证设置", self.step_6_verify_setup),
        ]
        
        for i, (step_name, step_func) in enumerate(steps, 1):
            print(f"\n{'='*50}")
            print(f"执行步骤 {i}/7: {step_name}")
            print(f"{'='*50}")
            
            if not step_func():
                print(f"\n❌ 步骤 {i} 失败: {step_name}")
                print("数据库设置流程中断")
                print("\n💡 建议:")
                print("  1. 检查错误信息")
                print("  2. 确保没有Django服务器在运行")
                print("  3. 检查数据库文件权限")
                print("  4. 重新运行此脚本")
                return False
            
            # 在步骤之间添加短暂延迟
            if i < len(steps):
                time.sleep(0.5)
        
        print(f"\n🎉 数据库设置完成！")
        print(f"\n📋 创建的账户:")
        print(f"  测试用户: <EMAIL> / testpass123")
        print(f"  管理员: <EMAIL> / admin123")
        
        print(f"\n🌐 访问地址:")
        print(f"  - 管理后台: http://localhost:8000/admin/")
        print(f"  - API文档: http://localhost:8000/docs/")
        print(f"  - 健康检查: http://localhost:8000/health/")
        
        print(f"\n🚀 下一步:")
        print(f"  1. 运行API功能验证: uv run python tmp_rovodev_api_test.py")
        print(f"  2. 启动开发服务器: uv run python manage.py runserver")
        
        return True


def main():
    """主函数"""
    setup = DatabaseSetup()
    setup.run_setup()


if __name__ == "__main__":
    main()