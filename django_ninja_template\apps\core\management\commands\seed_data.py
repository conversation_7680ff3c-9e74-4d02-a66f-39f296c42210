"""
种子数据生成管理命令
"""
from django.core.management.base import BaseCommand, CommandError
from django.apps import apps
from django.contrib.auth import get_user_model
from apps.core.factories.data_factory import DataSeeder, UserFactory, create_test_data
import json
from pathlib import Path

User = get_user_model()


class Command(BaseCommand):
    help = '生成种子数据'
    
    def add_arguments(self, parser):
        parser.add_argument('--app', type=str, help='指定应用名称')
        parser.add_argument('--model', type=str, help='指定模型名称')
        parser.add_argument('--count', type=int, default=10, help='生成数据数量')
        parser.add_argument('--config', type=str, help='配置文件路径')
        parser.add_argument('--clear', action='store_true', help='清除现有数据')
        parser.add_argument('--clear-all', action='store_true', help='清除所有生成的数据')
        parser.add_argument('--users', type=int, help='生成指定数量的用户')
        parser.add_argument('--demo', action='store_true', help='生成演示数据')
        parser.add_argument('--list-models', action='store_true', help='列出可用的模型')
        parser.add_argument('--stats', action='store_true', help='显示数据统计')
    
    def handle(self, *args, **options):
        seeder = DataSeeder()
        
        # 列出模型
        if options['list_models']:
            self.list_models(options.get('app'))
            return
        
        # 显示统计
        if options['stats']:
            self.show_stats()
            return
        
        # 清除所有数据
        if options['clear_all']:
            self.clear_all_data(seeder)
            return
        
        # 生成用户
        if options['users']:
            self.create_users(options['users'])
            return
        
        # 生成演示数据
        if options['demo']:
            self.create_demo_data()
            return
        
        # 加载配置
        config = self.load_config(options.get('config'))
        
        # 清除现有数据
        if options['clear']:
            self.clear_existing_data(seeder, options.get('app'), options.get('model'))
        
        # 生成数据
        if options['app']:
            self.seed_app_data(seeder, options['app'], config, options['count'])
        elif options['model']:
            self.seed_model_data(seeder, options['model'], options['count'])
        else:
            self.seed_all_apps(seeder, config)
        
        # 显示结果
        self.show_results(seeder)
    
    def list_models(self, app_name: str = None):
        """列出可用的模型"""
        if app_name:
            try:
                app_config = apps.get_app_config(app_name)
                models = app_config.get_models()
                self.stdout.write(f'\n应用 {app_name} 的模型:')
                for model in models:
                    self.stdout.write(f'  - {model.__name__}')
            except LookupError:
                self.stdout.write(self.style.ERROR(f'应用 {app_name} 不存在'))
        else:
            self.stdout.write('\n所有应用的模型:')
            for app_config in apps.get_app_configs():
                if app_config.name.startswith('apps.'):
                    models = app_config.get_models()
                    if models:
                        self.stdout.write(f'\n{app_config.name}:')
                        for model in models:
                            self.stdout.write(f'  - {model.__name__}')
    
    def show_stats(self):
        """显示数据统计"""
        self.stdout.write('\n数据库统计:')
        
        # 用户统计
        user_count = User.objects.count()
        self.stdout.write(f'用户总数: {user_count}')
        
        # 各应用统计
        for app_config in apps.get_app_configs():
            if app_config.name.startswith('apps.'):
                models = app_config.get_models()
                if models:
                    app_total = 0
                    app_stats = []
                    
                    for model in models:
                        count = model.objects.count()
                        if count > 0:
                            app_stats.append(f'  - {model.__name__}: {count}')
                            app_total += count
                    
                    if app_total > 0:
                        self.stdout.write(f'\n{app_config.name} (总计: {app_total}):')
                        for stat in app_stats:
                            self.stdout.write(stat)
    
    def clear_all_data(self, seeder: DataSeeder):
        """清除所有生成的数据"""
        self.stdout.write('正在清除所有生成的数据...')
        seeder.clear_all_data()
        self.stdout.write(self.style.SUCCESS('所有生成的数据已清除'))
    
    def clear_existing_data(self, seeder: DataSeeder, app_name: str = None, model_name: str = None):
        """清除现有数据"""
        if model_name:
            model = self.get_model_by_name(model_name)
            if model:
                seeder.clear_model_data(model)
        elif app_name:
            try:
                app_config = apps.get_app_config(app_name)
                for model in app_config.get_models():
                    seeder.clear_model_data(model)
                self.stdout.write(f'清除了应用 {app_name} 的数据')
            except LookupError:
                self.stdout.write(self.style.ERROR(f'应用 {app_name} 不存在'))
    
    def create_users(self, count: int):
        """创建用户"""
        self.stdout.write(f'正在创建 {count} 个用户...')
        
        users = []
        for i in range(count):
            user = UserFactory.create()
            users.append(user)
        
        self.stdout.write(self.style.SUCCESS(f'成功创建了 {len(users)} 个用户'))
        
        # 显示部分用户信息
        self.stdout.write('\n创建的用户:')
        for user in users[:5]:  # 只显示前5个
            self.stdout.write(f'  - {user.email} ({user.username})')
        
        if len(users) > 5:
            self.stdout.write(f'  ... 还有 {len(users) - 5} 个用户')
    
    def create_demo_data(self):
        """创建演示数据"""
        self.stdout.write('正在创建演示数据...')
        
        # 创建演示用户
        demo_users = []
        for i in range(5):
            user = UserFactory.create(
                email=f'demo{i+1}@example.com',
                username=f'demo{i+1}',
                first_name=f'Demo{i+1}',
                last_name='User'
            )
            demo_users.append(user)
        
        # 创建管理员用户
        admin_user = User.objects.create_user(
            email='<EMAIL>',
            username='admin',
            password='admin123',
            first_name='Admin',
            last_name='User',
            is_staff=True,
            is_superuser=True
        )
        
        self.stdout.write(self.style.SUCCESS('演示数据创建完成！'))
        self.stdout.write('\n创建的账户:')
        self.stdout.write('管理员账户:')
        self.stdout.write(f'  - <EMAIL> / admin123 (超级管理员)')
        self.stdout.write('\n演示用户账户:')
        for user in demo_users:
            self.stdout.write(f'  - {user.email} / password123')
    
    def seed_app_data(self, seeder: DataSeeder, app_name: str, config: dict, default_count: int):
        """为应用生成数据"""
        app_config = config.get(app_name, {})
        
        # 如果没有配置，使用默认配置
        if not app_config:
            try:
                app_models = apps.get_app_config(app_name).get_models()
                app_config = {
                    model.__name__: {'count': default_count}
                    for model in app_models
                }
            except LookupError:
                self.stdout.write(self.style.ERROR(f'应用 {app_name} 不存在'))
                return
        
        seeder.seed_app(app_name, app_config)
    
    def seed_model_data(self, seeder: DataSeeder, model_name: str, count: int):
        """为模型生成数据"""
        model = self.get_model_by_name(model_name)
        if model:
            seeder.seed_model(model, count)
        else:
            self.stdout.write(self.style.ERROR(f'模型 {model_name} 不存在'))
    
    def seed_all_apps(self, seeder: DataSeeder, config: dict):
        """为所有应用生成数据"""
        for app_config in apps.get_app_configs():
            if app_config.name.startswith('apps.'):
                app_name = app_config.name.split('.')[-1]
                if app_name in config:
                    seeder.seed_app(app_name, config[app_name])
    
    def get_model_by_name(self, model_name: str):
        """根据名称获取模型"""
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                if model.__name__ == model_name:
                    return model
        return None
    
    def load_config(self, config_path: str = None) -> dict:
        """加载配置文件"""
        if not config_path:
            return {}
        
        config_file = Path(config_path)
        if not config_file.exists():
            self.stdout.write(self.style.WARNING(f'配置文件不存在: {config_path}'))
            return {}
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except json.JSONDecodeError as e:
            self.stdout.write(self.style.ERROR(f'配置文件格式错误: {e}'))
            return {}
    
    def show_results(self, seeder: DataSeeder):
        """显示生成结果"""
        stats = seeder.get_stats()
        
        if not stats:
            self.stdout.write('没有生成任何数据')
            return
        
        self.stdout.write('\n生成结果:')
        total = 0
        for model_name, count in stats.items():
            self.stdout.write(f'  ✓ {model_name}: {count} 条记录')
            total += count
        
        self.stdout.write(f'\n总计生成了 {total} 条记录')
        self.stdout.write(self.style.SUCCESS('数据生成完成！'))
        
        # 提供下一步建议
        self.stdout.write('\n下一步:')
        self.stdout.write('  1. 访问 /admin/ 查看生成的数据')
        self.stdout.write('  2. 使用 API 接口测试数据')
        self.stdout.write('  3. 运行 python manage.py seed_data --stats 查看统计')
        self.stdout.write('  4. 运行 python manage.py seed_data --clear-all 清除数据')
