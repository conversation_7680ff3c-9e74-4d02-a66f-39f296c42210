"""
权限管理自动化工具
自动扫描模型和 API，生成权限配置
"""
from django.apps import apps
from django.db import models
from django.contrib.auth import get_user_model
from apps.permissions.models import Permission, Role, UserRole
from typing import List, Dict, Any, Optional, Type
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class PermissionAutoGenerator:
    """权限自动生成器"""
    
    STANDARD_ACTIONS = ['view', 'create', 'edit', 'delete', 'manage']
    
    def __init__(self):
        self.created_permissions = []
        self.created_roles = []
    
    def scan_and_generate_permissions(self, app_name: str, models_list: Optional[List[str]] = None) -> List[Permission]:
        """扫描应用并生成权限"""
        try:
            app_config = apps.get_app_config(app_name)
        except LookupError:
            logger.error(f"应用 {app_name} 不存在")
            return []
        
        permissions = []
        
        for model in app_config.get_models():
            # 如果指定了模型列表，只处理指定的模型
            if models_list and model.__name__ not in models_list:
                continue
                
            model_permissions = self._generate_model_permissions(model)
            permissions.extend(model_permissions)
        
        self.created_permissions = permissions
        return permissions
    
    def _generate_model_permissions(self, model: Type[models.Model]) -> List[Permission]:
        """为模型生成标准权限"""
        app_label = model._meta.app_label
        model_name = model._meta.model_name
        permissions = []
        
        for action in self.STANDARD_ACTIONS:
            codename = f'{app_label}.{model_name}.{action}'
            name = f'{self._get_action_name(action)} {model._meta.verbose_name}'
            
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                defaults={
                    'name': name,
                    'resource': model_name,
                    'action': action,
                    'description': f'{name} - {model._meta.verbose_name}'
                }
            )
            
            if created:
                logger.info(f'创建权限: {permission.name}')
            
            permissions.append(permission)
        
        return permissions
    
    def _get_action_name(self, action: str) -> str:
        """获取动作的中文名称"""
        action_names = {
            'view': '查看',
            'create': '创建',
            'edit': '编辑',
            'delete': '删除',
            'manage': '管理',
        }
        return action_names.get(action, action.title())
    
    def create_standard_roles(self, app_name: str, permissions: List[Permission]) -> List[Role]:
        """创建标准角色"""
        app_display_name = app_name.replace('_', ' ').title()
        
        role_configs = [
            {
                'name': f'{app_display_name} 查看者',
                'description': f'可以查看 {app_display_name} 相关资源',
                'actions': ['view'],
                'is_system_role': True,
            },
            {
                'name': f'{app_display_name} 编辑者',
                'description': f'可以创建和编辑 {app_display_name} 相关资源',
                'actions': ['view', 'create', 'edit'],
                'is_system_role': True,
            },
            {
                'name': f'{app_display_name} 管理员',
                'description': f'拥有 {app_display_name} 的完整权限',
                'actions': ['view', 'create', 'edit', 'delete', 'manage'],
                'is_system_role': True,
            }
        ]
        
        created_roles = []
        
        for role_config in role_configs:
            role, created = Role.objects.get_or_create(
                name=role_config['name'],
                defaults={
                    'description': role_config['description'],
                    'is_system_role': role_config['is_system_role']
                }
            )
            
            if created:
                logger.info(f'创建角色: {role.name}')
            
            # 分配权限
            for permission in permissions:
                if permission.action in role_config['actions']:
                    role.add_permission(permission)
            
            created_roles.append(role)
        
        self.created_roles = created_roles
        return created_roles
    
    def create_custom_permissions(self, custom_permissions: List[Dict[str, str]]) -> List[Permission]:
        """创建自定义权限"""
        permissions = []
        
        for perm_config in custom_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=perm_config['codename'],
                defaults={
                    'name': perm_config['name'],
                    'resource': perm_config.get('resource', 'custom'),
                    'action': perm_config.get('action', 'custom'),
                    'description': perm_config.get('description', perm_config['name'])
                }
            )
            
            if created:
                logger.info(f'创建自定义权限: {permission.name}')
            
            permissions.append(permission)
        
        return permissions
    
    def create_custom_roles(self, custom_roles: List[Dict[str, Any]]) -> List[Role]:
        """创建自定义角色"""
        roles = []
        
        for role_config in custom_roles:
            role, created = Role.objects.get_or_create(
                name=role_config['name'],
                defaults={
                    'description': role_config.get('description', ''),
                    'is_system_role': role_config.get('is_system_role', False)
                }
            )
            
            if created:
                logger.info(f'创建自定义角色: {role.name}')
            
            # 分配权限
            permission_codenames = role_config.get('permissions', [])
            for codename in permission_codenames:
                try:
                    permission = Permission.objects.get(codename=codename)
                    role.add_permission(permission)
                except Permission.DoesNotExist:
                    logger.warning(f'权限不存在: {codename}')
            
            roles.append(role)
        
        return roles
    
    def assign_role_to_user(self, user: User, role_name: str, assigned_by: Optional[User] = None) -> bool:
        """为用户分配角色"""
        try:
            role = Role.objects.get(name=role_name)
            user_role, created = UserRole.objects.get_or_create(
                user=user,
                role=role,
                defaults={'assigned_by': assigned_by}
            )
            
            if created:
                logger.info(f'为用户 {user.email} 分配角色: {role.name}')
                return True
            else:
                logger.info(f'用户 {user.email} 已拥有角色: {role.name}')
                return False
        except Role.DoesNotExist:
            logger.error(f'角色不存在: {role_name}')
            return False
    
    def remove_role_from_user(self, user: User, role_name: str) -> bool:
        """移除用户角色"""
        try:
            role = Role.objects.get(name=role_name)
            user_role = UserRole.objects.get(user=user, role=role)
            user_role.delete()
            
            logger.info(f'移除用户 {user.email} 的角色: {role.name}')
            return True
        except (Role.DoesNotExist, UserRole.DoesNotExist):
            logger.warning(f'用户 {user.email} 没有角色: {role_name}')
            return False
    
    def get_user_permissions(self, user: User) -> List[str]:
        """获取用户的所有权限"""
        from apps.permissions.services import PermissionService
        
        # 获取用户的所有角色
        user_roles = UserRole.objects.filter(user=user, is_active=True)
        permissions = set()
        
        for user_role in user_roles:
            if not user_role.is_expired():
                role_permissions = user_role.role.permissions.all()
                for perm in role_permissions:
                    permissions.add(perm.codename)
        
        return list(permissions)
    
    def cleanup_unused_permissions(self, app_name: str) -> int:
        """清理未使用的权限"""
        # 获取应用的所有权限
        app_permissions = Permission.objects.filter(codename__startswith=f'{app_name}.')
        
        # 检查哪些权限没有被角色使用
        unused_permissions = []
        for permission in app_permissions:
            if not permission.roles.exists():
                unused_permissions.append(permission)
        
        # 删除未使用的权限
        count = len(unused_permissions)
        for permission in unused_permissions:
            logger.info(f'删除未使用的权限: {permission.name}')
            permission.delete()
        
        return count
    
    def export_permissions_config(self, app_name: str) -> Dict[str, Any]:
        """导出权限配置"""
        app_permissions = Permission.objects.filter(codename__startswith=f'{app_name}.')
        app_roles = Role.objects.filter(name__icontains=app_name.replace('_', ' ').title())
        
        config = {
            'permissions': [],
            'roles': []
        }
        
        # 导出权限
        for permission in app_permissions:
            config['permissions'].append({
                'codename': permission.codename,
                'name': permission.name,
                'resource': permission.resource,
                'action': permission.action,
                'description': permission.description,
            })
        
        # 导出角色
        for role in app_roles:
            role_permissions = [perm.codename for perm in role.permissions.all()]
            config['roles'].append({
                'name': role.name,
                'description': role.description,
                'permissions': role_permissions,
                'is_system_role': role.is_system_role,
            })
        
        return config
    
    def import_permissions_config(self, config: Dict[str, Any]) -> Dict[str, int]:
        """导入权限配置"""
        stats = {'permissions': 0, 'roles': 0}
        
        # 导入权限
        for perm_config in config.get('permissions', []):
            permission, created = Permission.objects.get_or_create(
                codename=perm_config['codename'],
                defaults=perm_config
            )
            if created:
                stats['permissions'] += 1
        
        # 导入角色
        for role_config in config.get('roles', []):
            permissions = role_config.pop('permissions', [])
            role, created = Role.objects.get_or_create(
                name=role_config['name'],
                defaults=role_config
            )
            
            if created:
                stats['roles'] += 1
            
            # 分配权限
            for codename in permissions:
                try:
                    permission = Permission.objects.get(codename=codename)
                    role.add_permission(permission)
                except Permission.DoesNotExist:
                    logger.warning(f'权限不存在: {codename}')
        
        return stats


# 便捷函数
def auto_generate_app_permissions(app_name: str, create_roles: bool = True, models_list: Optional[List[str]] = None) -> Dict[str, Any]:
    """自动生成应用权限的便捷函数"""
    generator = PermissionAutoGenerator()
    
    # 生成权限
    permissions = generator.scan_and_generate_permissions(app_name, models_list)
    
    result = {
        'permissions_count': len(permissions),
        'roles_count': 0,
        'permissions': permissions,
        'roles': []
    }
    
    # 创建角色
    if create_roles:
        roles = generator.create_standard_roles(app_name, permissions)
        result['roles_count'] = len(roles)
        result['roles'] = roles
    
    return result


def create_admin_user_with_permissions(email: str, username: str, password: str, app_names: List[str]) -> User:
    """创建具有指定应用管理权限的管理员用户"""
    user = User.objects.create_user(
        email=email,
        username=username,
        password=password,
        is_staff=True,
        is_superuser=False
    )
    
    generator = PermissionAutoGenerator()
    
    # 为每个应用分配管理员角色
    for app_name in app_names:
        app_display_name = app_name.replace('_', ' ').title()
        admin_role_name = f'{app_display_name} 管理员'
        generator.assign_role_to_user(user, admin_role_name)
    
    logger.info(f'创建管理员用户: {user.email}，分配了 {len(app_names)} 个应用的管理权限')
    return user
