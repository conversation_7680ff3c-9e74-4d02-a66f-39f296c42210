"""
Admin configuration for users app.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _

from .models import User, UserProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Admin configuration for User model.
    """
    list_display = ('email', 'username', 'first_name', 'last_name', 'is_verified', 'is_premium', 'is_staff', 'created_at')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'is_verified', 'is_premium', 'created_at')
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('-created_at',)
    
    fieldsets = (
        (None, {'fields': ('email', 'username', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'phone', 'avatar', 'bio', 'birth_date')}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('Account Status'), {'fields': ('is_verified', 'is_premium')}),
        (_('Settings'), {'fields': ('timezone', 'language')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined', 'last_login_ip')}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'first_name', 'last_name', 'password1', 'password2'),
        }),
    )


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    Admin configuration for UserProfile model.
    """
    list_display = ('user', 'company', 'job_title', 'location', 'profile_visibility', 'created_at')
    list_filter = ('profile_visibility', 'email_notifications', 'push_notifications', 'marketing_emails', 'created_at')
    search_fields = ('user__email', 'user__username', 'company', 'job_title', 'location')
    ordering = ('-created_at',)
    
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Social Links'), {'fields': ('website', 'github', 'linkedin', 'twitter')}),
        (_('Professional Info'), {'fields': ('company', 'job_title', 'location')}),
        (_('Preferences'), {'fields': ('email_notifications', 'push_notifications', 'marketing_emails')}),
        (_('Privacy'), {'fields': ('profile_visibility',)}),
    )
