#!/bin/bash

echo "🚀 Django Ninja 模板项目启动脚本 (Linux/macOS)"
echo "=================================================="

# 检查虚拟环境是否存在
if [ ! -d ".venv" ]; then
    echo "⚠️  虚拟环境不存在，正在创建..."
    python3 -m venv .venv
    echo "✅ 虚拟环境创建完成"
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source .venv/bin/activate

# 检查依赖是否安装
echo "📦 检查依赖..."
if ! python -c "import django" 2>/dev/null; then
    echo "⚠️  Django 未安装，正在安装依赖..."
    pip install -r requirements.txt
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已安装"
fi

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在，正在从模板创建..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件"
else
    echo "✅ .env 配置文件存在"
fi

# 执行数据库检查
echo "🔍 检查项目配置..."
if ! python manage.py check --deploy; then
    echo "❌ 项目配置检查失败"
    exit 1
fi

echo "✅ 项目配置检查通过"

# 询问是否执行数据库迁移
read -p "是否执行数据库迁移？(y/n): " migrate
if [[ $migrate =~ ^[Yy]$ ]]; then
    echo "🗄️  执行数据库迁移..."
    python manage.py migrate
    
    read -p "是否创建超级用户？(y/n): " superuser
    if [[ $superuser =~ ^[Yy]$ ]]; then
        python manage.py createsuperuser
    fi
    
    read -p "是否初始化权限和角色？(y/n): " permissions
    if [[ $permissions =~ ^[Yy]$ ]]; then
        python manage.py setup_permissions
    fi
fi

echo ""
echo "🌐 服务地址："
echo "- 应用首页：http://localhost:8000/"
echo "- API 文档：http://localhost:8000/api/docs/"
echo "- 管理后台：http://localhost:8000/admin/"
echo "- 健康检查：http://localhost:8000/health/"
echo ""

# 询问是否启动开发服务器
read -p "是否启动开发服务器？(y/n): " runserver
if [[ $runserver =~ ^[Yy]$ ]]; then
    echo "🚀 启动开发服务器..."
    echo "按 Ctrl+C 停止服务器"
    python manage.py runserver
else
    echo ""
    echo "🎉 项目设置完成！"
    echo "手动启动服务器：python manage.py runserver"
    echo ""
fi
