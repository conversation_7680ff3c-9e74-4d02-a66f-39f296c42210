@echo off
echo 🚀 Django Ninja 模板项目启动脚本 (Windows)
echo ==================================================

REM 检查虚拟环境是否存在
if not exist ".venv" (
    echo ⚠️  虚拟环境不存在，正在创建...
    python -m venv .venv
    echo ✅ 虚拟环境创建完成
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call .venv\Scripts\activate.bat

REM 检查依赖是否安装
echo 📦 检查依赖...
python -c "import django" 2>nul
if errorlevel 1 (
    echo ⚠️  Django 未安装，正在安装依赖...
    pip install -r requirements.txt
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

REM 检查环境配置文件
if not exist ".env" (
    echo ⚠️  .env 文件不存在，正在从模板创建...
    copy .env.example .env
    echo ✅ 已创建 .env 文件
) else (
    echo ✅ .env 配置文件存在
)

REM 执行数据库检查
echo 🔍 检查项目配置...
python manage.py check --deploy
if errorlevel 1 (
    echo ❌ 项目配置检查失败
    pause
    exit /b 1
)

echo ✅ 项目配置检查通过

REM 询问是否执行数据库迁移
set /p migrate="是否执行数据库迁移？(y/n): "
if /i "%migrate%"=="y" (
    echo 🗄️  执行数据库迁移...
    python manage.py migrate
    
    set /p superuser="是否创建超级用户？(y/n): "
    if /i "%superuser%"=="y" (
        python manage.py createsuperuser
    )
    
    set /p permissions="是否初始化权限和角色？(y/n): "
    if /i "%permissions%"=="y" (
        python manage.py setup_permissions
    )
)

echo.
echo 🌐 服务地址：
echo - 应用首页：http://localhost:8000/
echo - API 文档：http://localhost:8000/api/docs/
echo - 管理后台：http://localhost:8000/admin/
echo - 健康检查：http://localhost:8000/health/
echo.

REM 询问是否启动开发服务器
set /p runserver="是否启动开发服务器？(y/n): "
if /i "%runserver%"=="y" (
    echo 🚀 启动开发服务器...
    echo 按 Ctrl+C 停止服务器
    python manage.py runserver
) else (
    echo.
    echo 🎉 项目设置完成！
    echo 手动启动服务器：python manage.py runserver
    echo.
    pause
)
