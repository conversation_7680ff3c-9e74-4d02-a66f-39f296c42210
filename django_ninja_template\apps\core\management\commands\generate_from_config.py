"""
基于Python模型文件的代码生成工具
支持从 Python 模型文件批量生成模块、Admin 和视图组件
"""

import os
import json
import ast
import importlib.util
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.template import Template, Context
from django.conf import settings


class Command(BaseCommand):
    help = "基于Python模型文件的代码生成工具 - 支持批量生成模块、Admin 和视图组件"

    def add_arguments(self, parser):
        parser.add_argument(
            "--models", type=str, required=True, help="Python 模型文件路径"
        )
        parser.add_argument(
            "--full",
            action="store_true",
            help="生成完整应用 (模型、Admin、Views)",
        )
        parser.add_argument(
            "--models-only", action="store_true", help="仅生成模型文件"
        )
        parser.add_argument(
            "--admin-only", action="store_true", help="仅生成Admin文件"
        )
        parser.add_argument(
            "--views-only", action="store_true", help="仅生成Views文件"
        )
        parser.add_argument(
            "--output-dir", type=str, default="apps", help="输出目录 (默认: apps)"
        )
        parser.add_argument(
            "--dry-run", action="store_true", help="预览模式，不实际生成文件"
        )

    def handle(self, *args, **options):
        models_file = options["models"]

        # 检查模型文件是否存在
        if not os.path.exists(models_file):
            raise CommandError(f"模型文件不存在: {models_file}")

        # 解析Python模型文件
        try:
            self.config = self.parse_models_file(models_file)
        except Exception as e:
            raise CommandError(f"模型文件解析错误: {e}")

        self.options = options

        # 验证配置
        self.validate_config()

        # 开始生成
        self.stdout.write(self.style.SUCCESS("🚀 开始基于Python模型文件生成代码..."))
        self.stdout.write(f"📁 模型文件: {models_file}")
        self.stdout.write(f'📦 应用名称: {self.config.get("app_name", "unknown")}')

        if options["dry_run"]:
            self.stdout.write(self.style.WARNING("🔍 预览模式 - 不会实际生成文件"))

        # 根据选项决定生成内容
        if options["full"]:
            # 生成完整应用
            self.generate_app_module()
            self.generate_models()
            self.generate_admin()
            self.generate_views()
        elif options["models_only"]:
            # 仅生成模型
            self.generate_models()
        elif options["admin_only"]:
            # 仅生成Admin文件
            self.generate_admin()
        elif options["views_only"]:
            # 仅生成Views文件
            self.generate_views()
        else:
            # 默认：生成应用模块和模型
            self.generate_app_module()
            self.generate_models()

        # 生成完成提示
        self.show_completion_message()

    def parse_models_file(self, models_file):
        """解析Python模型文件"""
        # 读取文件内容
        with open(models_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析AST
        tree = ast.parse(content)
        
        config = {
            'app_name': 'generated_app',
            'models': {}
        }
        
        # 查找应用名称（从文件名或注释中）
        file_name = Path(models_file).stem
        if file_name != 'models':
            config['app_name'] = file_name
        
        # 遍历AST节点查找模型类
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # 检查是否继承自Model或BaseModel
                if self._is_model_class(node):
                    model_config = self._parse_model_class(node)
                    config['models'][node.name] = model_config
        
        return config
    
    def _is_model_class(self, class_node):
        """检查类是否是Django模型"""
        for base in class_node.bases:
            if isinstance(base, ast.Name):
                if base.id in ['Model', 'BaseModel']:
                    return True
            elif isinstance(base, ast.Attribute):
                if (isinstance(base.value, ast.Name) and 
                    base.value.id == 'models' and 
                    base.attr == 'Model'):
                    return True
        return False
    
    def _parse_model_class(self, class_node):
        """解析模型类的字段"""
        model_config = {
            'fields': {},
            'options': {}
        }
        
        # 解析类的文档字符串作为描述
        if (class_node.body and 
            isinstance(class_node.body[0], ast.Expr) and 
            isinstance(class_node.body[0].value, ast.Constant)):
            model_config['description'] = class_node.body[0].value.value
        
        # 解析字段
        for node in class_node.body:
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        field_name = target.id
                        if not field_name.startswith('_'):  # 忽略私有属性
                            field_config = self._parse_field_assignment(node.value)
                            if field_config:
                                model_config['fields'][field_name] = field_config
        
        return model_config
    
    def _parse_field_assignment(self, value_node):
        """解析字段赋值"""
        if isinstance(value_node, ast.Call):
            if isinstance(value_node.func, ast.Attribute):
                # models.CharField() 格式
                if (isinstance(value_node.func.value, ast.Name) and 
                    value_node.func.value.id == 'models'):
                    field_type = value_node.func.attr
                    return self._django_field_to_config(field_type, value_node.keywords)
            elif isinstance(value_node.func, ast.Name):
                # CharField() 格式
                field_type = value_node.func.id
                return self._django_field_to_config(field_type, value_node.keywords)
        
        return None
    
    def _django_field_to_config(self, field_type, keywords):
        """将Django字段类型转换为配置"""
        # 字段类型映射
        type_mapping = {
            'CharField': 'str',
            'TextField': 'text',
            'IntegerField': 'int',
            'FloatField': 'float',
            'DecimalField': 'decimal',
            'BooleanField': 'bool',
            'DateField': 'date',
            'DateTimeField': 'datetime',
            'TimeField': 'time',
            'EmailField': 'email',
            'URLField': 'url',
            'SlugField': 'slug',
            'UUIDField': 'uuid',
            'JSONField': 'json',
            'FileField': 'file',
            'ImageField': 'image',
            'ForeignKey': 'fk',
            'ManyToManyField': 'm2m',
            'OneToOneField': 'o2o',
        }
        
        config = {
            'type': type_mapping.get(field_type, 'str')
        }
        
        # 解析关键字参数
        for keyword in keywords:
            if keyword.arg == 'max_length' and isinstance(keyword.value, ast.Constant):
                config['max_length'] = keyword.value.value
            elif keyword.arg == 'null' and isinstance(keyword.value, ast.Constant):
                config['null'] = keyword.value.value
            elif keyword.arg == 'blank' and isinstance(keyword.value, ast.Constant):
                config['blank'] = keyword.value.value
            elif keyword.arg == 'unique' and isinstance(keyword.value, ast.Constant):
                config['unique'] = keyword.value.value
            elif keyword.arg == 'default' and isinstance(keyword.value, ast.Constant):
                config['default'] = keyword.value.value
            elif keyword.arg == 'help_text' and isinstance(keyword.value, ast.Constant):
                config['help_text'] = keyword.value.value
            elif keyword.arg == 'verbose_name' and isinstance(keyword.value, ast.Constant):
                config['verbose_name'] = keyword.value.value
        
        return config

    def validate_config(self):
        """验证配置文件格式"""
        if "app_name" not in self.config:
            raise CommandError("配置文件必须包含 app_name 字段")

        if "models" not in self.config:
            raise CommandError("配置文件必须包含 models 字段")

        if not isinstance(self.config["models"], dict):
            raise CommandError("models 字段必须是字典格式")

    def generate_app_module(self):
        """生成应用模块结构"""
        app_name = self.config["app_name"]
        output_dir = self.options["output_dir"]

        self.stdout.write(f"\n📦 生成应用模块: {app_name}")

        # 创建目录结构
        app_path = Path(output_dir)

        if not self.options["dry_run"]:
            app_path.mkdir(parents=True, exist_ok=True)
            (app_path / "migrations").mkdir(exist_ok=True)

        # 生成基础文件
        files_to_generate = [
            ("__init__.py", ""),
            ("apps.py", self.get_apps_content()),
            ("migrations/__init__.py", ""),
        ]

        for file_name, content in files_to_generate:
            file_path = app_path / file_name
            if not self.options["dry_run"]:
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
            self.stdout.write(f"  ✓ {file_name}")

    def generate_models(self):
        """生成模型文件"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        self.stdout.write(f"\n🗃️  生成模型文件: {len(models)} 个模型")

        # 生成 models.py 内容
        models_content = self.get_models_content()

        if not self.options["dry_run"]:
            models_file = Path(self.options["output_dir"]) / "models.py"
            with open(models_file, "w", encoding="utf-8") as f:
                f.write(models_content)

        for model_name in models.keys():
            self.stdout.write(f"  ✓ {model_name}")

    def generate_admin(self):
        """生成 Admin 配置"""
        app_name = self.config["app_name"]

        self.stdout.write(f"\n⚙️  生成 Admin 配置")

        admin_content = self.get_admin_content()

        if not self.options["dry_run"]:
            admin_file = Path(self.options["output_dir"]) / "admin.py"
            with open(admin_file, "w", encoding="utf-8") as f:
                f.write(admin_content)

        self.stdout.write(f"  ✓ admin.py")

    def generate_views(self):
        """生成 Views 文件"""
        app_name = self.config["app_name"]

        self.stdout.write(f"\n📄 生成 Views 文件")

        views_content = self.get_views_content()

        if not self.options["dry_run"]:
            views_file = Path(self.options["output_dir"]) / "views.py"
            with open(views_file, "w", encoding="utf-8") as f:
                f.write(views_content)

        self.stdout.write(f"  ✓ views.py")

    def show_completion_message(self):
        """显示完成消息和后续步骤"""
        app_name = self.config["app_name"]

        self.stdout.write(self.style.SUCCESS(f"\n🎉 代码生成完成！"))
        self.stdout.write("\n📋 后续步骤:")
        self.stdout.write(f"  1. 将应用添加到 INSTALLED_APPS: apps.{app_name}")
        self.stdout.write(
            f"  2. 生成迁移文件: python manage.py makemigrations {app_name}"
        )
        self.stdout.write(f"  3. 执行迁移: python manage.py migrate")
        self.stdout.write(f"  4. 在 core/urls.py 中添加路由")

    def get_apps_content(self):
        """生成 apps.py 内容"""
        app_name = self.config["app_name"]
        app_title = app_name.replace("_", " ").title()

        return f'''"""
{app_title} 应用配置
"""
from django.apps import AppConfig


class {app_name.title()}Config(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.{app_name}'
    verbose_name = '{app_title}'

    def ready(self):
        """应用就绪时的初始化操作"""
        try:
            import apps.{app_name}.signals  # noqa
        except ImportError:
            pass
'''

    def get_models_content(self):
        """生成 models.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} 应用数据模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


'''

        for model_name, model_config in models.items():
            content += self.get_model_class_content(model_name, model_config)
            content += "\n\n"

        return content

    def get_model_class_content(self, model_name, model_config):
        """生成单个模型类内容"""
        fields = model_config.get("fields", {})
        options = model_config.get("options", {})
        description = model_config.get("description", f"{model_name} 模型")

        content = f'''class {model_name}(models.Model):
    """{description}"""

'''

        # 生成字段
        for field_name, field_config in fields.items():
            django_field = self.convert_field_type(field_config, field_name)
            content += f"    {field_name} = {django_field}\n"

        # 添加Meta类
        content += f'''
    class Meta:
        verbose_name = _('{options.get('verbose_name', model_name)}')
        verbose_name_plural = _('{options.get('verbose_name_plural', model_name)}')
        db_table = '{self.config['app_name']}_{model_name.lower()}'
        ordering = {options.get('ordering', ['-id'])}

    def __str__(self):
        return f'{model_name} #{{self.id}}'
'''

        return content

    def convert_field_type(self, field_config, field_name=""):
        """转换字段类型为 Django 字段"""
        if isinstance(field_config, str):
            field_config = {'type': field_config}

        field_type = field_config.get("type", "str")
        
        # 基础字段类型映射
        type_mapping = {
            "str": "models.CharField",
            "text": "models.TextField",
            "int": "models.IntegerField",
            "float": "models.FloatField",
            "bool": "models.BooleanField",
            "date": "models.DateField",
            "datetime": "models.DateTimeField",
            "time": "models.TimeField",
            "email": "models.EmailField",
            "url": "models.URLField",
            "json": "models.JSONField",
            "file": "models.FileField",
            "image": "models.ImageField",
            "slug": "models.SlugField",
            "uuid": "models.UUIDField",
            "decimal": "models.DecimalField",
            "fk": "models.ForeignKey",
            "m2m": "models.ManyToManyField",
            "o2o": "models.OneToOneField",
        }

        django_field_class = type_mapping.get(field_type, "models.CharField")
        field_params = []

        # 处理不同字段类型的参数
        if field_type == "str":
            max_length = field_config.get("max_length", 255)
            field_params.append(f"max_length={max_length}")
        elif field_type == "decimal":
            max_digits = field_config.get("max_digits", 10)
            decimal_places = field_config.get("decimal_places", 2)
            field_params.append(f"max_digits={max_digits}")
            field_params.append(f"decimal_places={decimal_places}")
        elif field_type == "fk":
            related_model = field_config.get("to", "User")
            on_delete = field_config.get("on_delete", "CASCADE")
            field_params.append(f'"{related_model}"')
            field_params.append(f"on_delete=models.{on_delete}")
        elif field_type == "m2m":
            related_model = field_config.get("to", "Tag")
            field_params.append(f'"{related_model}"')

        # 通用参数
        if field_config.get("null", False):
            field_params.append("null=True")
        if field_config.get("blank", False):
            field_params.append("blank=True")
        if field_config.get("unique", False):
            field_params.append("unique=True")
        
        # verbose_name
        verbose_name = field_config.get("verbose_name", field_name.replace("_", " ").title())
        field_params.append(f'verbose_name=_("{verbose_name}")')

        params_str = ", ".join(field_params)
        return f"{django_field_class}({params_str})"

    def get_admin_content(self):
        """生成 admin.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} Admin 配置
"""
from django.contrib import admin

'''

        # 导入模型
        model_imports = ", ".join(models.keys())
        content += f"from .models import {model_imports}\n\n"

        # 为每个模型生成Admin类
        for model_name, model_config in models.items():
            fields = list(model_config.get("fields", {}).keys())
            list_display = fields[:5] if len(fields) > 5 else fields  # 最多显示5个字段
            
            content += f'''@admin.register({model_name})
class {model_name}Admin(admin.ModelAdmin):
    list_display = {list_display + ['id']}
    list_filter = ['created_at'] if 'created_at' in {fields} else []
    search_fields = {[f for f in fields if 'name' in f or 'title' in f][:3]}
    ordering = ['-id']

'''

        return content

    def get_views_content(self):
        """生成 views.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} Views
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q

'''

        # 导入模型
        model_imports = ", ".join(models.keys())
        content += f"from .models import {model_imports}\n\n"

        # 为每个模型生成视图
        for model_name in models.keys():
            content += self.get_model_views_content(model_name)
            content += "\n\n"

        return content

    def get_model_views_content(self, model_name):
        """生成单个模型的视图内容"""
        model_lower = model_name.lower()

        content = f'''# {model_name} Views

@login_required
def {model_lower}_list(request):
    """{model_name} 列表视图"""
    search = request.GET.get('search', '')
    items = {model_name}.objects.all()
    
    if search:
        items = items.filter(
            Q(name__icontains=search) if hasattr({model_name}, 'name') else Q(id__icontains=search)
        )
    
    paginator = Paginator(items, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {{
        'page_obj': page_obj,
        'search': search,
        'title': '{model_name} 列表',
    }}
    return render(request, '{self.config['app_name']}/{model_lower}_list.html', context)


@login_required
def {model_lower}_detail(request, pk):
    """{model_name} 详情视图"""
    item = get_object_or_404({model_name}, pk=pk)
    context = {{
        'item': item,
        'title': f'{model_name} 详情',
    }}
    return render(request, '{self.config['app_name']}/{model_lower}_detail.html', context)


@login_required
def {model_lower}_create(request):
    """{model_name} 创建视图"""
    if request.method == 'POST':
        try:
            # 这里需要根据实际字段调整
            data = {{
                'name': request.POST.get('name', ''),
            }}
            item = {model_name}.objects.create(**data)
            messages.success(request, f'{model_name} 创建成功！')
            return redirect('{self.config['app_name']}:{model_lower}_detail', pk=item.pk)
        except Exception as e:
            messages.error(request, f'创建失败: {{e}}')
    
    context = {{
        'title': f'创建 {model_name}',
    }}
    return render(request, '{self.config['app_name']}/{model_lower}_form.html', context)


@login_required
def {model_lower}_update(request, pk):
    """{model_name} 更新视图"""
    item = get_object_or_404({model_name}, pk=pk)
    
    if request.method == 'POST':
        try:
            # 这里需要根据实际字段调整
            item.name = request.POST.get('name', item.name)
            item.save()
            messages.success(request, f'{model_name} 更新成功！')
            return redirect('{self.config['app_name']}:{model_lower}_detail', pk=item.pk)
        except Exception as e:
            messages.error(request, f'更新失败: {{e}}')
    
    context = {{
        'item': item,
        'title': f'编辑 {model_name}',
    }}
    return render(request, '{self.config['app_name']}/{model_lower}_form.html', context)


@login_required
def {model_lower}_delete(request, pk):
    """{model_name} 删除视图"""
    item = get_object_or_404({model_name}, pk=pk)
    
    if request.method == 'POST':
        try:
            item.delete()
            messages.success(request, f'{model_name} 删除成功！')
            return redirect('{self.config['app_name']}:{model_lower}_list')
        except Exception as e:
            messages.error(request, f'删除失败: {{e}}')
    
    context = {{
        'item': item,
        'title': f'删除 {model_name}',
    }}
    return render(request, '{self.config['app_name']}/{model_lower}_confirm_delete.html', context)'''

        return content