"""
Vue.js 前端代码自动生成器
基于 Django 模型和 API 自动生成 Vue.js 组件和页面
"""
from typing import Type, Dict, Any, List, Optional
from django.db import models
from pathlib import Path
import json


class VueComponentGenerator:
    """Vue 组件生成器"""
    
    def __init__(self, model: Type[models.Model], api_prefix: str = '/api'):
        self.model = model
        self.model_name = model.__name__
        self.model_name_lower = model.__name__.lower()
        self.model_name_plural = f"{model.__name__.lower()}s"
        self.api_prefix = api_prefix
        self.app_name = model._meta.app_label
    
    def generate_all_components(self, output_dir: str = 'frontend/src') -> Dict[str, str]:
        """生成所有 Vue 组件"""
        components = {}
        
        # 生成组件
        components[f'{self.model_name}List.vue'] = self.generate_list_component()
        components[f'{self.model_name}Form.vue'] = self.generate_form_component()
        components[f'{self.model_name}Detail.vue'] = self.generate_detail_component()
        components[f'{self.model_name}Card.vue'] = self.generate_card_component()
        
        # 生成页面
        components[f'{self.model_name}Page.vue'] = self.generate_page_component()
        
        # 生成 API 服务
        components[f'{self.model_name_lower}Api.js'] = self.generate_api_service()
        
        # 生成 Vuex Store
        components[f'{self.model_name_lower}Store.js'] = self.generate_vuex_store()
        
        # 生成路由配置
        components[f'{self.model_name_lower}Routes.js'] = self.generate_routes()
        
        return components
    
    def generate_list_component(self) -> str:
        """生成列表组件"""
        fields = self._get_display_fields()
        search_fields = self._get_search_fields()
        filter_fields = self._get_filter_fields()
        
        return f'''<template>
  <div class="{self.model_name_lower}-list">
    <!-- 搜索和过滤 -->
    <div class="search-filters mb-4">
      <div class="row">
        <div class="col-md-6">
          <input
            v-model="searchQuery"
            type="text"
            class="form-control"
            placeholder="搜索{self.model._meta.verbose_name}..."
            @input="handleSearch"
          />
        </div>
        <div class="col-md-6">
          <button @click="showCreateModal = true" class="btn btn-primary">
            <i class="fas fa-plus"></i> 新建{self.model._meta.verbose_name}
          </button>
        </div>
      </div>
      
      <!-- 过滤器 -->
      <div class="row mt-3" v-if="showFilters">
        {self._generate_filter_controls(filter_fields)}
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-responsive">
      <table class="table table-striped table-hover">
        <thead class="table-dark">
          <tr>
            {self._generate_table_headers(fields)}
            <th width="120">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in items" :key="item.id">
            {self._generate_table_cells(fields)}
            <td>
              <div class="btn-group btn-group-sm">
                <button @click="viewItem(item)" class="btn btn-outline-info">
                  <i class="fas fa-eye"></i>
                </button>
                <button @click="editItem(item)" class="btn btn-outline-warning">
                  <i class="fas fa-edit"></i>
                </button>
                <button @click="deleteItem(item)" class="btn btn-outline-danger">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <nav v-if="totalPages > 1">
      <ul class="pagination justify-content-center">
        <li class="page-item" :class="{{disabled: currentPage === 1}}">
          <button @click="changePage(currentPage - 1)" class="page-link">上一页</button>
        </li>
        <li 
          v-for="page in visiblePages" 
          :key="page"
          class="page-item"
          :class="{{active: page === currentPage}}"
        >
          <button @click="changePage(page)" class="page-link">{{{{ page }}}}</button>
        </li>
        <li class="page-item" :class="{{disabled: currentPage === totalPages}}">
          <button @click="changePage(currentPage + 1)" class="page-link">下一页</button>
        </li>
      </ul>
    </nav>

    <!-- 创建/编辑模态框 -->
    <{self.model_name}Form
      v-if="showCreateModal || showEditModal"
      :visible="showCreateModal || showEditModal"
      :item="editingItem"
      @close="closeModal"
      @saved="handleSaved"
    />

    <!-- 详情模态框 -->
    <{self.model_name}Detail
      v-if="showDetailModal"
      :visible="showDetailModal"
      :item="viewingItem"
      @close="showDetailModal = false"
    />
  </div>
</template>

<script>
import {{ mapState, mapActions }} from 'vuex'
import {self.model_name}Form from './{self.model_name}Form.vue'
import {self.model_name}Detail from './{self.model_name}Detail.vue'

export default {{
  name: '{self.model_name}List',
  components: {{
    {self.model_name}Form,
    {self.model_name}Detail
  }},
  data() {{
    return {{
      searchQuery: '',
      showFilters: false,
      showCreateModal: false,
      showEditModal: false,
      showDetailModal: false,
      editingItem: null,
      viewingItem: null,
      searchTimeout: null
    }}
  }},
  computed: {{
    ...mapState('{self.model_name_lower}', [
      'items',
      'loading',
      'currentPage',
      'totalPages',
      'total'
    ]),
    visiblePages() {{
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {{
        pages.push(i)
      }}
      return pages
    }}
  }},
  mounted() {{
    this.loadItems()
  }},
  methods: {{
    ...mapActions('{self.model_name_lower}', [
      'fetchItems',
      'deleteItem'
    ]),
    async loadItems() {{
      await this.fetchItems({{
        page: this.currentPage,
        search: this.searchQuery
      }})
    }},
    handleSearch() {{
      clearTimeout(this.searchTimeout)
      this.searchTimeout = setTimeout(() => {{
        this.loadItems()
      }}, 500)
    }},
    changePage(page) {{
      if (page >= 1 && page <= this.totalPages) {{
        this.$store.commit('{self.model_name_lower}/SET_CURRENT_PAGE', page)
        this.loadItems()
      }}
    }},
    viewItem(item) {{
      this.viewingItem = item
      this.showDetailModal = true
    }},
    editItem(item) {{
      this.editingItem = {{ ...item }}
      this.showEditModal = true
    }},
    async deleteItem(item) {{
      if (confirm(`确定要删除 "${{item.name || item.title || item.id}}" 吗？`)) {{
        try {{
          await this.deleteItem(item.id)
          this.$message.success('删除成功')
          this.loadItems()
        }} catch (error) {{
          this.$message.error('删除失败: ' + error.message)
        }}
      }}
    }},
    closeModal() {{
      this.showCreateModal = false
      this.showEditModal = false
      this.editingItem = null
    }},
    handleSaved() {{
      this.closeModal()
      this.loadItems()
      this.$message.success('保存成功')
    }}
  }}
}}
</script>

<style scoped>
.{self.model_name_lower}-list {{
  padding: 20px;
}}

.search-filters {{
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}}

.table {{
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}}

.btn-group-sm .btn {{
  padding: 0.25rem 0.5rem;
}}

.pagination {{
  margin-top: 20px;
}}
</style>'''
    
    def generate_form_component(self) -> str:
        """生成表单组件"""
        fields = self._get_form_fields()
        
        return f'''<template>
  <div class="modal" :class="{{show: visible}}" @click.self="$emit('close')">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            {{{{ item ? '编辑' : '新建' }}}}{self.model._meta.verbose_name}
          </h5>
          <button @click="$emit('close')" class="btn-close"></button>
        </div>
        
        <form @submit.prevent="handleSubmit">
          <div class="modal-body">
            {self._generate_form_fields(fields)}
          </div>
          
          <div class="modal-footer">
            <button type="button" @click="$emit('close')" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" :disabled="loading" class="btn btn-primary">
              <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
              {{{{ item ? '更新' : '创建' }}}}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import {{ mapActions }} from 'vuex'

export default {{
  name: '{self.model_name}Form',
  props: {{
    visible: {{
      type: Boolean,
      default: false
    }},
    item: {{
      type: Object,
      default: null
    }}
  }},
  data() {{
    return {{
      form: {self._generate_form_data_structure(fields)},
      loading: false,
      errors: {{}}
    }}
  }},
  watch: {{
    item: {{
      immediate: true,
      handler(newItem) {{
        if (newItem) {{
          this.form = {{ ...newItem }}
        }} else {{
          this.resetForm()
        }}
      }}
    }}
  }},
  methods: {{
    ...mapActions('{self.model_name_lower}', ['createItem', 'updateItem']),
    async handleSubmit() {{
      this.loading = true
      this.errors = {{}}
      
      try {{
        if (this.item) {{
          await this.updateItem({{ id: this.item.id, ...this.form }})
        }} else {{
          await this.createItem(this.form)
        }}
        this.$emit('saved')
      }} catch (error) {{
        if (error.response?.data) {{
          this.errors = error.response.data
        }} else {{
          this.$message.error('保存失败: ' + error.message)
        }}
      }} finally {{
        this.loading = false
      }}
    }},
    resetForm() {{
      this.form = {self._generate_form_data_structure(fields)}
      this.errors = {{}}
    }}
  }}
}}
</script>

<style scoped>
.modal {{
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1050;
}}

.modal.show {{
  display: flex;
  align-items: center;
  justify-content: center;
}}

.modal-dialog {{
  max-width: 600px;
  width: 90%;
}}

.form-group {{
  margin-bottom: 1rem;
}}

.error-text {{
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}}
</style>'''
    
    def generate_detail_component(self) -> str:
        """生成详情组件"""
        fields = self._get_display_fields()
        
        return f'''<template>
  <div class="modal" :class="{{show: visible}}" @click.self="$emit('close')">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{self.model._meta.verbose_name}详情</h5>
          <button @click="$emit('close')" class="btn-close"></button>
        </div>
        
        <div class="modal-body" v-if="item">
          <div class="row">
            {self._generate_detail_fields(fields)}
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="$emit('close')" class="btn btn-secondary">关闭</button>
          <button @click="editItem" class="btn btn-primary">编辑</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {{
  name: '{self.model_name}Detail',
  props: {{
    visible: {{
      type: Boolean,
      default: false
    }},
    item: {{
      type: Object,
      required: true
    }}
  }},
  methods: {{
    editItem() {{
      this.$emit('edit', this.item)
      this.$emit('close')
    }},
    formatDate(date) {{
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    }},
    formatBoolean(value) {{
      return value ? '是' : '否'
    }}
  }}
}}
</script>

<style scoped>
.detail-field {{
  margin-bottom: 1rem;
}}

.field-label {{
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}}

.field-value {{
  color: #212529;
}}

.badge {{
  font-size: 0.875rem;
}}
</style>'''
    
    def _get_display_fields(self) -> List[Dict[str, Any]]:
        """获取显示字段"""
        fields = []
        for field in self.model._meta.fields:
            if not field.name.endswith('_id') and field.name not in ['password', 'created_at', 'updated_at']:
                fields.append({
                    'name': field.name,
                    'verbose_name': field.verbose_name or field.name,
                    'type': field.__class__.__name__,
                    'is_foreign_key': isinstance(field, models.ForeignKey),
                    'is_boolean': isinstance(field, models.BooleanField),
                    'is_datetime': isinstance(field, models.DateTimeField),
                })
        return fields[:6]  # 限制显示字段数量
    
    def _get_form_fields(self) -> List[Dict[str, Any]]:
        """获取表单字段"""
        fields = []
        for field in self.model._meta.fields:
            if (not isinstance(field, models.AutoField) and 
                not field.auto_now and not field.auto_now_add and
                field.name not in ['password', 'created_at', 'updated_at']):
                
                fields.append({
                    'name': field.name,
                    'verbose_name': field.verbose_name or field.name,
                    'type': field.__class__.__name__,
                    'required': not field.null and not field.blank,
                    'max_length': getattr(field, 'max_length', None),
                    'is_foreign_key': isinstance(field, models.ForeignKey),
                    'is_boolean': isinstance(field, models.BooleanField),
                    'is_text': isinstance(field, models.TextField),
                    'choices': getattr(field, 'choices', None),
                })
        return fields
    
    def _get_search_fields(self) -> List[str]:
        """获取搜索字段"""
        search_fields = []
        for field in self.model._meta.fields:
            if isinstance(field, (models.CharField, models.TextField)):
                search_fields.append(field.name)
        return search_fields[:3]
    
    def _get_filter_fields(self) -> List[Dict[str, Any]]:
        """获取过滤字段"""
        filter_fields = []
        for field in self.model._meta.fields:
            if isinstance(field, (models.BooleanField, models.ForeignKey)):
                filter_fields.append({
                    'name': field.name,
                    'verbose_name': field.verbose_name or field.name,
                    'type': field.__class__.__name__,
                })
        return filter_fields
    
    def _generate_table_headers(self, fields: List[Dict]) -> str:
        """生成表格头部"""
        headers = []
        for field in fields:
            headers.append(f'            <th>{field["verbose_name"]}</th>')
        return '\n'.join(headers)
    
    def _generate_table_cells(self, fields: List[Dict]) -> str:
        """生成表格单元格"""
        cells = []
        for field in fields:
            if field['is_boolean']:
                cell = f'''            <td>
              <span class="badge" :class="item.{field['name']} ? 'bg-success' : 'bg-secondary'">
                {{{{ item.{field['name']} ? '是' : '否' }}}}
              </span>
            </td>'''
            elif field['is_datetime']:
                cell = f'''            <td>{{{{ formatDate(item.{field['name']}) }}}}</td>'''
            elif field['is_foreign_key']:
                cell = f'''            <td>{{{{ item.{field['name']}_display || item.{field['name']} }}}}</td>'''
            else:
                cell = f'''            <td>{{{{ item.{field['name']} }}}}</td>'''
            cells.append(cell)
        return '\n'.join(cells)
    
    def _generate_filter_controls(self, filter_fields: List[Dict]) -> str:
        """生成过滤控件"""
        controls = []
        for field in filter_fields:
            if field['type'] == 'BooleanField':
                control = f'''        <div class="col-md-3">
          <select v-model="filters.{field['name']}" class="form-select">
            <option value="">{field['verbose_name']}</option>
            <option value="true">是</option>
            <option value="false">否</option>
          </select>
        </div>'''
            else:
                control = f'''        <div class="col-md-3">
          <select v-model="filters.{field['name']}" class="form-select">
            <option value="">{field['verbose_name']}</option>
            <!-- 动态选项 -->
          </select>
        </div>'''
            controls.append(control)
        return '\n'.join(controls)
    
    def _generate_form_fields(self, fields: List[Dict]) -> str:
        """生成表单字段"""
        form_fields = []
        for field in fields:
            if field['is_boolean']:
                form_field = f'''            <div class="form-group">
              <div class="form-check">
                <input
                  v-model="form.{field['name']}"
                  type="checkbox"
                  class="form-check-input"
                  id="{field['name']}"
                />
                <label class="form-check-label" for="{field['name']}">
                  {field['verbose_name']}
                </label>
              </div>
            </div>'''
            elif field['is_text']:
                form_field = f'''            <div class="form-group">
              <label for="{field['name']}" class="form-label">
                {field['verbose_name']}
                {'<span class="text-danger">*</span>' if field['required'] else ''}
              </label>
              <textarea
                v-model="form.{field['name']}"
                id="{field['name']}"
                class="form-control"
                :class="{{'is-invalid': errors.{field['name']}}}"
                rows="4"
                {'required' if field['required'] else ''}
              ></textarea>
              <div v-if="errors.{field['name']}" class="invalid-feedback">
                {{{{ errors.{field['name']}[0] }}}}
              </div>
            </div>'''
            elif field['choices']:
                options = '\n'.join([f'                <option value="{choice[0]}">{choice[1]}</option>' 
                                   for choice in field['choices']])
                form_field = f'''            <div class="form-group">
              <label for="{field['name']}" class="form-label">
                {field['verbose_name']}
                {'<span class="text-danger">*</span>' if field['required'] else ''}
              </label>
              <select
                v-model="form.{field['name']}"
                id="{field['name']}"
                class="form-select"
                :class="{{'is-invalid': errors.{field['name']}}}"
                {'required' if field['required'] else ''}
              >
                <option value="">请选择</option>
{options}
              </select>
              <div v-if="errors.{field['name']}" class="invalid-feedback">
                {{{{ errors.{field['name']}[0] }}}}
              </div>
            </div>'''
            else:
                input_type = 'email' if 'email' in field['name'].lower() else 'text'
                form_field = f'''            <div class="form-group">
              <label for="{field['name']}" class="form-label">
                {field['verbose_name']}
                {'<span class="text-danger">*</span>' if field['required'] else ''}
              </label>
              <input
                v-model="form.{field['name']}"
                type="{input_type}"
                id="{field['name']}"
                class="form-control"
                :class="{{'is-invalid': errors.{field['name']}}}"
                {'maxlength="' + str(field['max_length']) + '"' if field['max_length'] else ''}
                {'required' if field['required'] else ''}
              />
              <div v-if="errors.{field['name']}" class="invalid-feedback">
                {{{{ errors.{field['name']}[0] }}}}
              </div>
            </div>'''
            form_fields.append(form_field)
        return '\n'.join(form_fields)
    
    def _generate_detail_fields(self, fields: List[Dict]) -> str:
        """生成详情字段"""
        detail_fields = []
        for field in fields:
            if field['is_boolean']:
                detail_field = f'''            <div class="col-md-6 detail-field">
              <div class="field-label">{field['verbose_name']}</div>
              <div class="field-value">
                <span class="badge" :class="item.{field['name']} ? 'bg-success' : 'bg-secondary'">
                  {{{{ formatBoolean(item.{field['name']}) }}}}
                </span>
              </div>
            </div>'''
            elif field['is_datetime']:
                detail_field = f'''            <div class="col-md-6 detail-field">
              <div class="field-label">{field['verbose_name']}</div>
              <div class="field-value">{{{{ formatDate(item.{field['name']}) }}}}</div>
            </div>'''
            else:
                detail_field = f'''            <div class="col-md-6 detail-field">
              <div class="field-label">{field['verbose_name']}</div>
              <div class="field-value">{{{{ item.{field['name']} || '-' }}}}</div>
            </div>'''
            detail_fields.append(detail_field)
        return '\n'.join(detail_fields)
    
    def _generate_form_data_structure(self, fields: List[Dict]) -> str:
        """生成表单数据结构"""
        form_data = {}
        for field in fields:
            if field['is_boolean']:
                form_data[field['name']] = False
            elif field['type'] in ['IntegerField', 'FloatField']:
                form_data[field['name']] = 0
            else:
                form_data[field['name']] = ''
        return json.dumps(form_data, indent=8).replace('"', "'")
    
    def generate_card_component(self) -> str:
        """生成卡片组件"""
        return f'''<template>
  <div class="card {self.model_name_lower}-card">
    <div class="card-body">
      <h5 class="card-title">{{{{ item.name || item.title || `{self.model._meta.verbose_name} #${{item.id}}` }}}}</h5>
      <p class="card-text" v-if="item.description || item.summary">
        {{{{ item.description || item.summary }}}}
      </p>
      <div class="card-meta">
        <small class="text-muted">
          创建时间: {{{{ formatDate(item.created_at) }}}}
        </small>
      </div>
    </div>
    <div class="card-footer">
      <div class="btn-group btn-group-sm">
        <button @click="$emit('view', item)" class="btn btn-outline-info">查看</button>
        <button @click="$emit('edit', item)" class="btn btn-outline-warning">编辑</button>
        <button @click="$emit('delete', item)" class="btn btn-outline-danger">删除</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {{
  name: '{self.model_name}Card',
  props: {{
    item: {{
      type: Object,
      required: true
    }}
  }},
  methods: {{
    formatDate(date) {{
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    }}
  }}
}}
</script>

<style scoped>
.{self.model_name_lower}-card {{
  margin-bottom: 1rem;
  transition: transform 0.2s;
}}

.{self.model_name_lower}-card:hover {{
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}}

.card-title {{
  margin-bottom: 0.5rem;
}}

.card-meta {{
  margin-top: 0.5rem;
}}
</style>'''

    def generate_page_component(self) -> str:
        """生成页面组件"""
        return f'''<template>
  <div class="{self.model_name_lower}-page">
    <div class="page-header">
      <h1>{self.model._meta.verbose_name}管理</h1>
      <p class="text-muted">管理和维护{self.model._meta.verbose_name}信息</p>
    </div>

    <div class="page-content">
      <{self.model_name}List />
    </div>
  </div>
</template>

<script>
import {self.model_name}List from '../components/{self.model_name}List.vue'

export default {{
  name: '{self.model_name}Page',
  components: {{
    {self.model_name}List
  }}
}}
</script>

<style scoped>
.{self.model_name_lower}-page {{
  padding: 20px;
}}

.page-header {{
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #dee2e6;
}}

.page-header h1 {{
  margin-bottom: 0.5rem;
  color: #212529;
}}
</style>'''

    def generate_api_service(self) -> str:
        """生成 API 服务"""
        return f'''/**
 * {self.model._meta.verbose_name} API 服务
 * 自动生成的 API 客户端
 */
import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://localhost:8000'
const API_PREFIX = '{self.api_prefix}'

class {self.model_name}Api {{
  constructor() {{
    this.baseURL = `${{API_BASE_URL}}${{API_PREFIX}}/{self.app_name}/`
    this.client = axios.create({{
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {{
        'Content-Type': 'application/json'
      }}
    }})

    // 请求拦截器 - 添加认证token
    this.client.interceptors.request.use(
      config => {{
        const token = localStorage.getItem('access_token')
        if (token) {{
          config.headers.Authorization = `Bearer ${{token}}`
        }}
        return config
      }},
      error => Promise.reject(error)
    )

    // 响应拦截器 - 处理错误
    this.client.interceptors.response.use(
      response => response,
      error => {{
        if (error.response?.status === 401) {{
          // 处理认证失败
          localStorage.removeItem('access_token')
          window.location.href = '/login'
        }}
        return Promise.reject(error)
      }}
    )
  }}

  /**
   * 获取{self.model._meta.verbose_name}列表
   * @param {{Object}} params - 查询参数
   * @param {{number}} params.page - 页码
   * @param {{number}} params.per_page - 每页数量
   * @param {{string}} params.search - 搜索关键词
   * @param {{string}} params.ordering - 排序字段
   * @returns {{Promise<Object>}} 分页数据
   */
  async getList(params = {{}}) {{
    const response = await this.client.get('', {{ params }})
    return response.data
  }}

  /**
   * 获取{self.model._meta.verbose_name}详情
   * @param {{number}} id - {self.model._meta.verbose_name}ID
   * @returns {{Promise<Object>}} {self.model._meta.verbose_name}详情
   */
  async getDetail(id) {{
    const response = await this.client.get(`${{id}}/`)
    return response.data
  }}

  /**
   * 创建{self.model._meta.verbose_name}
   * @param {{Object}} data - {self.model._meta.verbose_name}数据
   * @returns {{Promise<Object>}} 创建的{self.model._meta.verbose_name}
   */
  async create(data) {{
    const response = await this.client.post('', data)
    return response.data
  }}

  /**
   * 更新{self.model._meta.verbose_name}
   * @param {{number}} id - {self.model._meta.verbose_name}ID
   * @param {{Object}} data - 更新数据
   * @returns {{Promise<Object>}} 更新后的{self.model._meta.verbose_name}
   */
  async update(id, data) {{
    const response = await this.client.put(`${{id}}/`, data)
    return response.data
  }}

  /**
   * 删除{self.model._meta.verbose_name}
   * @param {{number}} id - {self.model._meta.verbose_name}ID
   * @returns {{Promise<void>}}
   */
  async delete(id) {{
    await this.client.delete(`${{id}}/`)
  }}

  /**
   * 批量删除{self.model._meta.verbose_name}
   * @param {{Array<number>}} ids - {self.model._meta.verbose_name}ID列表
   * @returns {{Promise<void>}}
   */
  async bulkDelete(ids) {{
    await this.client.post('bulk-delete/', {{ ids }})
  }}

  /**
   * 搜索{self.model._meta.verbose_name}
   * @param {{string}} query - 搜索关键词
   * @param {{Object}} filters - 过滤条件
   * @returns {{Promise<Object>}} 搜索结果
   */
  async search(query, filters = {{}}) {{
    const params = {{ search: query, ...filters }}
    return this.getList(params)
  }}
}}

// 导出单例实例
export default new {self.model_name}Api()

// 也可以导出类，用于创建多个实例
export {{ {self.model_name}Api }}'''

    def generate_vuex_store(self) -> str:
        """生成 Vuex Store"""
        return f'''/**
 * {self.model._meta.verbose_name} Vuex Store
 * 自动生成的状态管理
 */
import {self.model_name_lower}Api from '../api/{self.model_name_lower}Api'

const state = () => ({{
  // 列表数据
  items: [],
  total: 0,
  currentPage: 1,
  perPage: 20,
  totalPages: 0,

  // 当前项目
  currentItem: null,

  // 加载状态
  loading: false,
  listLoading: false,
  itemLoading: false,

  // 错误信息
  error: null,

  // 搜索和过滤
  searchQuery: '',
  filters: {{}},
  ordering: '-created_at'
}})

const getters = {{
  // 获取指定ID的项目
  getItemById: (state) => (id) => {{
    return state.items.find(item => item.id === id)
  }},

  // 是否有数据
  hasItems: (state) => {{
    return state.items.length > 0
  }},

  // 是否为空状态
  isEmpty: (state) => {{
    return !state.loading && state.items.length === 0
  }},

  // 分页信息
  pagination: (state) => ({{
    current: state.currentPage,
    total: state.total,
    pageSize: state.perPage,
    totalPages: state.totalPages
  }})
}}

const mutations = {{
  // 设置加载状态
  SET_LOADING(state, loading) {{
    state.loading = loading
  }},

  SET_LIST_LOADING(state, loading) {{
    state.listLoading = loading
  }},

  SET_ITEM_LOADING(state, loading) {{
    state.itemLoading = loading
  }},

  // 设置错误
  SET_ERROR(state, error) {{
    state.error = error
  }},

  // 设置列表数据
  SET_ITEMS(state, {{ items, total, page, pages }}) {{
    state.items = items
    state.total = total
    state.currentPage = page
    state.totalPages = pages
  }},

  // 设置当前项目
  SET_CURRENT_ITEM(state, item) {{
    state.currentItem = item
  }},

  // 添加项目
  ADD_ITEM(state, item) {{
    state.items.unshift(item)
    state.total += 1
  }},

  // 更新项目
  UPDATE_ITEM(state, updatedItem) {{
    const index = state.items.findIndex(item => item.id === updatedItem.id)
    if (index !== -1) {{
      state.items.splice(index, 1, updatedItem)
    }}

    // 更新当前项目
    if (state.currentItem && state.currentItem.id === updatedItem.id) {{
      state.currentItem = updatedItem
    }}
  }},

  // 删除项目
  REMOVE_ITEM(state, id) {{
    const index = state.items.findIndex(item => item.id === id)
    if (index !== -1) {{
      state.items.splice(index, 1)
      state.total -= 1
    }}

    // 清除当前项目
    if (state.currentItem && state.currentItem.id === id) {{
      state.currentItem = null
    }}
  }},

  // 设置搜索查询
  SET_SEARCH_QUERY(state, query) {{
    state.searchQuery = query
  }},

  // 设置过滤器
  SET_FILTERS(state, filters) {{
    state.filters = filters
  }},

  // 设置排序
  SET_ORDERING(state, ordering) {{
    state.ordering = ordering
  }},

  // 设置当前页
  SET_CURRENT_PAGE(state, page) {{
    state.currentPage = page
  }},

  // 重置状态
  RESET_STATE(state) {{
    Object.assign(state, state())
  }}
}}

const actions = {{
  /**
   * 获取{self.model._meta.verbose_name}列表
   */
  async fetchItems({{ commit, state }}, params = {{}}) {{
    commit('SET_LIST_LOADING', true)
    commit('SET_ERROR', null)

    try {{
      const queryParams = {{
        page: state.currentPage,
        per_page: state.perPage,
        search: state.searchQuery,
        ordering: state.ordering,
        ...state.filters,
        ...params
      }}

      const response = await {self.model_name_lower}Api.getList(queryParams)

      commit('SET_ITEMS', {{
        items: response.items || response.results || [],
        total: response.total || response.count || 0,
        page: response.page || 1,
        pages: response.pages || Math.ceil((response.total || 0) / state.perPage)
      }})

      return response
    }} catch (error) {{
      commit('SET_ERROR', error.message)
      throw error
    }} finally {{
      commit('SET_LIST_LOADING', false)
    }}
  }},

  /**
   * 获取{self.model._meta.verbose_name}详情
   */
  async fetchItem({{ commit }}, id) {{
    commit('SET_ITEM_LOADING', true)
    commit('SET_ERROR', null)

    try {{
      const item = await {self.model_name_lower}Api.getDetail(id)
      commit('SET_CURRENT_ITEM', item)
      return item
    }} catch (error) {{
      commit('SET_ERROR', error.message)
      throw error
    }} finally {{
      commit('SET_ITEM_LOADING', false)
    }}
  }},

  /**
   * 创建{self.model._meta.verbose_name}
   */
  async createItem({{ commit }}, data) {{
    commit('SET_LOADING', true)
    commit('SET_ERROR', null)

    try {{
      const item = await {self.model_name_lower}Api.create(data)
      commit('ADD_ITEM', item)
      return item
    }} catch (error) {{
      commit('SET_ERROR', error.message)
      throw error
    }} finally {{
      commit('SET_LOADING', false)
    }}
  }},

  /**
   * 更新{self.model._meta.verbose_name}
   */
  async updateItem({{ commit }}, {{ id, ...data }}) {{
    commit('SET_LOADING', true)
    commit('SET_ERROR', null)

    try {{
      const item = await {self.model_name_lower}Api.update(id, data)
      commit('UPDATE_ITEM', item)
      return item
    }} catch (error) {{
      commit('SET_ERROR', error.message)
      throw error
    }} finally {{
      commit('SET_LOADING', false)
    }}
  }},

  /**
   * 删除{self.model._meta.verbose_name}
   */
  async deleteItem({{ commit }}, id) {{
    commit('SET_LOADING', true)
    commit('SET_ERROR', null)

    try {{
      await {self.model_name_lower}Api.delete(id)
      commit('REMOVE_ITEM', id)
    }} catch (error) {{
      commit('SET_ERROR', error.message)
      throw error
    }} finally {{
      commit('SET_LOADING', false)
    }}
  }},

  /**
   * 搜索{self.model._meta.verbose_name}
   */
  async searchItems({{ commit, dispatch }}, query) {{
    commit('SET_SEARCH_QUERY', query)
    commit('SET_CURRENT_PAGE', 1)
    return dispatch('fetchItems')
  }},

  /**
   * 设置过滤器
   */
  async setFilters({{ commit, dispatch }}, filters) {{
    commit('SET_FILTERS', filters)
    commit('SET_CURRENT_PAGE', 1)
    return dispatch('fetchItems')
  }},

  /**
   * 设置排序
   */
  async setOrdering({{ commit, dispatch }}, ordering) {{
    commit('SET_ORDERING', ordering)
    commit('SET_CURRENT_PAGE', 1)
    return dispatch('fetchItems')
  }},

  /**
   * 重置状态
   */
  resetState({{ commit }}) {{
    commit('RESET_STATE')
  }}
}}

export default {{
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}}'''

    def generate_routes(self) -> str:
        """生成路由配置"""
        return f'''/**
 * {self.model._meta.verbose_name} 路由配置
 * 自动生成的路由定义
 */

const {self.model_name}Page = () => import('../pages/{self.model_name}Page.vue')

export default [
  {{
    path: '/{self.model_name_lower}',
    name: '{self.model_name}List',
    component: {self.model_name}Page,
    meta: {{
      title: '{self.model._meta.verbose_name}管理',
      requiresAuth: true,
      permissions: ['{self.app_name}.{self.model_name_lower}.view']
    }}
  }},
  {{
    path: '/{self.model_name_lower}/:id',
    name: '{self.model_name}Detail',
    component: {self.model_name}Page,
    props: true,
    meta: {{
      title: '{self.model._meta.verbose_name}详情',
      requiresAuth: true,
      permissions: ['{self.app_name}.{self.model_name_lower}.view']
    }}
  }}
]

// 导出路由配置用于注册
export const {self.model_name_lower}Routes = {{
  path: '/{self.model_name_lower}',
  name: '{self.model_name}',
  component: () => import('../layouts/DefaultLayout.vue'),
  children: [
    {{
      path: '',
      name: '{self.model_name}List',
      component: {self.model_name}Page,
      meta: {{
        title: '{self.model._meta.verbose_name}管理',
        icon: 'fas fa-list',
        requiresAuth: true,
        permissions: ['{self.app_name}.{self.model_name_lower}.view']
      }}
    }}
  ]
}}'''
