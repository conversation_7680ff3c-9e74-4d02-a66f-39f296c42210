INFO 2025-07-14 14:57:26,561 autoreload 40800 35408 Watching for file changes with StatReloader
INFO 2025-07-14 14:57:54,447 autoreload 39312 6364 Watching for file changes with StatReloader
INFO 2025-07-14 14:58:27,082 logging 39312 24168 Request started - ID: ce0e9741-bb94-48e8-8a8d-a066b85280f4 | Method: GET | Path: / | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:27,161 logging 39312 24168 Request completed - ID: ce0e9741-bb94-48e8-8a8d-a066b85280f4 | Status: 404 | Duration: 0.078s
WARNING 2025-07-14 14:58:27,163 log 39312 24168 Not Found: /
WARNING 2025-07-14 14:58:27,165 basehttp 39312 24168 "GET / HTTP/1.1" 404 16214
INFO 2025-07-14 14:58:27,248 basehttp 39312 24168 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-07-14 14:58:27,250 basehttp 39312 29276 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-07-14 14:58:28,169 basehttp 39312 29276 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-07-14 14:58:29,021 logging 39312 29276 Request started - ID: 85d81e0b-eda9-4a56-a779-00252d466b46 | Method: GET | Path: /favicon.ico | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:29,074 logging 39312 29276 Request completed - ID: 85d81e0b-eda9-4a56-a779-00252d466b46 | Status: 404 | Duration: 0.053s
WARNING 2025-07-14 14:58:29,077 log 39312 29276 Not Found: /favicon.ico
WARNING 2025-07-14 14:58:29,078 basehttp 39312 29276 "GET /favicon.ico HTTP/1.1" 404 16276
INFO 2025-07-14 14:58:32,493 logging 39312 29276 Request started - ID: 55c2fa78-4140-4754-b922-61594062862b | Method: GET | Path: /admin/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:32,532 logging 39312 29276 Request completed - ID: 55c2fa78-4140-4754-b922-61594062862b | Status: 302 | Duration: 0.038s
INFO 2025-07-14 14:58:32,533 basehttp 39312 29276 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-14 14:58:32,540 logging 39312 29276 Request started - ID: 16ef820a-35db-459b-954c-a66be320b3ca | Method: GET | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:32,612 logging 39312 29276 Request completed - ID: 16ef820a-35db-459b-954c-a66be320b3ca | Status: 200 | Duration: 0.072s
INFO 2025-07-14 14:58:32,613 basehttp 39312 29276 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 17360
INFO 2025-07-14 14:58:32,665 basehttp 39312 29276 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-07-14 14:58:32,669 basehttp 39312 24168 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-07-14 14:58:32,671 basehttp 39312 29276 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-07-14 14:58:32,676 basehttp 39312 24168 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-07-14 14:58:32,678 basehttp 39312 16412 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-07-14 14:58:32,679 basehttp 39312 28456 "GET /static/admin/css/login.css HTTP/1.1" 304 0
INFO 2025-07-14 14:58:32,860 basehttp 39312 28456 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-07-14 14:58:49,299 logging 39312 28456 Request started - ID: 9d93d683-1cb7-429f-a42e-47ab1722e749 | Method: GET | Path: /api/doc | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:49,354 logging 39312 28456 Request completed - ID: 9d93d683-1cb7-429f-a42e-47ab1722e749 | Status: 404 | Duration: 0.055s
WARNING 2025-07-14 14:58:49,355 log 39312 28456 Not Found: /api/doc
WARNING 2025-07-14 14:58:49,357 basehttp 39312 28456 "GET /api/doc HTTP/1.1" 404 38217
INFO 2025-07-14 14:58:52,850 logging 39312 28456 Request started - ID: 8698daa1-3cf8-4017-9456-4ee9e7f23276 | Method: GET | Path: /api/docs | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:52,895 logging 39312 28456 Request completed - ID: 8698daa1-3cf8-4017-9456-4ee9e7f23276 | Status: 404 | Duration: 0.045s
INFO 2025-07-14 14:58:52,896 basehttp 39312 28456 "GET /api/docs HTTP/1.1" 301 0
INFO 2025-07-14 14:58:52,904 logging 39312 16412 Request started - ID: 46e86fcf-15ff-4df4-81e3-451ec266a670 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:52,931 logging 39312 16412 Request completed - ID: 46e86fcf-15ff-4df4-81e3-451ec266a670 | Status: 200 | Duration: 0.027s
INFO 2025-07-14 14:58:52,932 basehttp 39312 16412 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-14 14:58:52,990 basehttp 39312 29276 "GET /static/ninja/swagger-ui-init.js HTTP/1.1" 200 1308
INFO 2025-07-14 14:58:52,993 basehttp 39312 16412 "GET /static/ninja/swagger-ui.css HTTP/1.1" 200 151211
INFO 2025-07-14 14:58:53,017 basehttp 39312 24168 "GET /static/ninja/swagger-ui-bundle.js HTTP/1.1" 200 1385226
INFO 2025-07-14 14:58:53,351 logging 39312 24168 Request started - ID: 1440492e-5fba-4c35-a9f6-1534c30cb6ff | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-14 14:58:53,571 basehttp 39312 16412 "GET /static/ninja/favicon.png HTTP/1.1" 200 6234
INFO 2025-07-14 14:58:53,589 logging 39312 24168 Request completed - ID: 1440492e-5fba-4c35-a9f6-1534c30cb6ff | Status: 200 | Duration: 0.238s
INFO 2025-07-14 14:58:53,590 basehttp 39312 24168 "GET /api/api/openapi.json HTTP/1.1" 200 77458
INFO 2025-07-14 15:48:10,042 autoreload 35592 19560 Watching for file changes with StatReloader
INFO 2025-07-14 15:53:48,378 autoreload 35592 19560 F:\PycharmProjects\django_ninja_template\apps\model_designer\migrations\0001_initial.py changed, reloading.
INFO 2025-07-14 15:53:51,112 autoreload 28788 12416 Watching for file changes with StatReloader
ERROR 2025-07-14 16:06:20,309 log 20620 5932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:06:20,350 log 20620 5932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:06:20,385 log 20620 5932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:06:20,417 log 20620 5932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:06:20,450 log 20620 5932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
INFO 2025-07-14 16:12:25,044 autoreload 40052 34328 Watching for file changes with StatReloader
INFO 2025-07-14 16:12:33,369 logging 40052 39556 Request started - ID: 955211ea-b6ba-4963-9042-889c1efc73de | Method: GET | Path: /api/core/health | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:12:33,619 logging 40052 39556 Request completed - ID: 955211ea-b6ba-4963-9042-889c1efc73de | Status: 200 | Duration: 0.251s
INFO 2025-07-14 16:12:33,621 basehttp 40052 39556 "GET /api/core/health HTTP/1.1" 200 138
INFO 2025-07-14 16:12:35,618 logging 40052 39556 Request started - ID: 7af806e7-9671-473e-bb7b-2675ec19ea68 | Method: GET | Path: /favicon.ico | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:12:35,686 logging 40052 39556 Request completed - ID: 7af806e7-9671-473e-bb7b-2675ec19ea68 | Status: 404 | Duration: 0.069s
WARNING 2025-07-14 16:12:35,688 log 40052 39556 Not Found: /favicon.ico
WARNING 2025-07-14 16:12:35,689 basehttp 40052 39556 "GET /favicon.ico HTTP/1.1" 404 16276
INFO 2025-07-14 16:12:47,658 logging 40052 39556 Request started - ID: f61012f6-9a96-4414-b5ed-895c8e75f18f | Method: GET | Path: /api/core/info | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:12:47,785 logging 40052 39556 Request completed - ID: f61012f6-9a96-4414-b5ed-895c8e75f18f | Status: 200 | Duration: 0.127s
INFO 2025-07-14 16:12:47,786 basehttp 40052 39556 "GET /api/core/info HTTP/1.1" 200 486
INFO 2025-07-14 16:12:56,229 logging 40052 39556 Request started - ID: c070d018-604e-45fe-ac49-9aff370c0b1d | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:12:56,251 logging 40052 39556 Request completed - ID: c070d018-604e-45fe-ac49-9aff370c0b1d | Status: 200 | Duration: 0.022s
INFO 2025-07-14 16:12:56,253 basehttp 40052 39556 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 16:13:16,394 logging 40052 39556 Request started - ID: 6255132f-7877-4750-a75b-d992163aed33 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:13:16,449 logging 40052 39556 Request completed - ID: 6255132f-7877-4750-a75b-d992163aed33 | Status: 200 | Duration: 0.055s
INFO 2025-07-14 16:13:16,451 basehttp 40052 39556 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-14 16:13:16,526 basehttp 40052 39556 "GET /static/ninja/swagger-ui.css HTTP/1.1" 304 0
INFO 2025-07-14 16:13:16,531 basehttp 40052 39556 "GET /static/ninja/swagger-ui-init.js HTTP/1.1" 304 0
INFO 2025-07-14 16:13:16,532 basehttp 40052 39172 "GET /static/ninja/swagger-ui-bundle.js HTTP/1.1" 304 0
INFO 2025-07-14 16:13:16,532 basehttp 40052 31992 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-07-14 16:13:16,536 basehttp 40052 39556 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-07-14 16:13:16,945 logging 40052 39556 Request started - ID: 500ebd87-21a9-4639-bb56-0875d65f0f2a | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:13:17,016 basehttp 40052 39172 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-07-14 16:13:17,152 logging 40052 39556 Request completed - ID: 500ebd87-21a9-4639-bb56-0875d65f0f2a | Status: 200 | Duration: 0.207s
INFO 2025-07-14 16:13:17,153 basehttp 40052 39556 "GET /api/api/openapi.json HTTP/1.1" 200 77458
INFO 2025-07-14 16:14:28,949 logging 40052 39556 Request started - ID: ab3feb5d-e983-40e7-a516-de3d903c511d | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:14:30,174 logging 40052 39556 Request completed - ID: ab3feb5d-e983-40e7-a516-de3d903c511d | Status: 200 | Duration: 1.225s
INFO 2025-07-14 16:14:30,176 basehttp 40052 39556 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-14 16:15:03,015 logging 40052 39556 Request started - ID: 22354849-a253-4588-8a74-c7aab73d23a7 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:15:04,465 logging 40052 39556 Request completed - ID: 22354849-a253-4588-8a74-c7aab73d23a7 | Status: 200 | Duration: 1.450s
INFO 2025-07-14 16:15:04,466 basehttp 40052 39556 "POST /api/auth/login HTTP/1.1" 200 697
INFO 2025-07-14 16:17:20,802 logging 40052 37760 Request started - ID: eabe9dfd-c915-4ea1-8e75-f230507a4047 | Method: GET | Path: /admin/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:17:20,877 logging 40052 37760 Request completed - ID: eabe9dfd-c915-4ea1-8e75-f230507a4047 | Status: 302 | Duration: 0.076s
INFO 2025-07-14 16:17:20,879 basehttp 40052 37760 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-14 16:17:20,886 logging 40052 37760 Request started - ID: ae9658a7-e70e-4ff8-a59a-6253f7a30e3e | Method: GET | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:17:20,932 logging 40052 37760 Request completed - ID: ae9658a7-e70e-4ff8-a59a-6253f7a30e3e | Status: 200 | Duration: 0.047s
INFO 2025-07-14 16:17:20,934 basehttp 40052 37760 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 17360
INFO 2025-07-14 16:17:27,500 logging 40052 37760 Request started - ID: 5cd54241-a8f4-47c5-bc88-0cef33e56947 | Method: POST | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:17:28,536 logging 40052 37760 Request completed - ID: 5cd54241-a8f4-47c5-bc88-0cef33e56947 | Status: 302 | Duration: 1.036s
INFO 2025-07-14 16:17:28,537 basehttp 40052 37760 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-14 16:17:28,547 logging 40052 37760 Request started - ID: 2c627e83-8bd2-496e-8591-8e927463889a | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:28,661 logging 40052 37760 Request completed - ID: 2c627e83-8bd2-496e-8591-8e927463889a | Status: 200 | Duration: 0.115s
INFO 2025-07-14 16:17:28,663 basehttp 40052 37760 "GET /admin/ HTTP/1.1" 200 34863
INFO 2025-07-14 16:17:28,719 basehttp 40052 37760 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-14 16:17:28,914 basehttp 40052 37760 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
INFO 2025-07-14 16:17:28,914 basehttp 40052 19308 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
INFO 2025-07-14 16:17:28,916 basehttp 40052 24936 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
INFO 2025-07-14 16:17:34,642 logging 40052 24936 Request started - ID: 7cda2266-cec5-436e-a05b-4c3f88c28a7b | Method: GET | Path: /admin/authentication/emailverificationtoken/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:34,754 logging 40052 24936 Request completed - ID: 7cda2266-cec5-436e-a05b-4c3f88c28a7b | Status: 200 | Duration: 0.120s
INFO 2025-07-14 16:17:34,756 basehttp 40052 24936 "GET /admin/authentication/emailverificationtoken/ HTTP/1.1" 200 35071
INFO 2025-07-14 16:17:34,846 basehttp 40052 24936 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
INFO 2025-07-14 16:17:34,857 basehttp 40052 36056 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-14 16:17:34,858 basehttp 40052 22340 "GET /static/admin/js/core.js HTTP/1.1" 200 6208
INFO 2025-07-14 16:17:34,860 basehttp 40052 19496 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
INFO 2025-07-14 16:17:34,862 logging 40052 19308 Request started - ID: 19012a79-d830-486d-8d4a-ea9f26e59867 | Method: GET | Path: /admin/jsi18n/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:34,863 basehttp 40052 24936 "GET /static/admin/js/actions.js HTTP/1.1" 200 8076
INFO 2025-07-14 16:17:34,872 basehttp 40052 22340 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-14 16:17:34,873 basehttp 40052 36056 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-07-14 16:17:34,882 basehttp 40052 19496 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-07-14 16:17:34,891 basehttp 40052 37760 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
INFO 2025-07-14 16:17:34,902 logging 40052 19308 Request completed - ID: 19012a79-d830-486d-8d4a-ea9f26e59867 | Status: 200 | Duration: 0.064s
INFO 2025-07-14 16:17:34,903 basehttp 40052 19308 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
INFO 2025-07-14 16:17:34,908 basehttp 40052 24936 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
INFO 2025-07-14 16:17:35,043 basehttp 40052 24936 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-07-14 16:17:35,096 basehttp 40052 24936 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-07-14 16:17:37,175 logging 40052 24936 Request started - ID: fc99332f-0325-4d9d-b481-50dfeece3171 | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:37,330 logging 40052 24936 Request completed - ID: fc99332f-0325-4d9d-b481-50dfeece3171 | Status: 200 | Duration: 0.168s
INFO 2025-07-14 16:17:37,330 basehttp 40052 24936 "GET /admin/ HTTP/1.1" 200 34863
INFO 2025-07-14 16:17:43,619 logging 40052 24936 Request started - ID: f55e0201-e9d4-4039-be29-bc902cb15ba3 | Method: GET | Path: /admin/users/user/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:43,755 logging 40052 24936 Request completed - ID: f55e0201-e9d4-4039-be29-bc902cb15ba3 | Status: 200 | Duration: 0.150s
INFO 2025-07-14 16:17:43,756 basehttp 40052 24936 "GET /admin/users/user/ HTTP/1.1" 200 39816
INFO 2025-07-14 16:17:43,838 logging 40052 24936 Request started - ID: 935c75db-048b-40e4-bd01-20321cd22ea5 | Method: GET | Path: /admin/jsi18n/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:43,839 basehttp 40052 37760 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-07-14 16:17:43,839 basehttp 40052 19308 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
INFO 2025-07-14 16:17:43,866 logging 40052 24936 Request completed - ID: 935c75db-048b-40e4-bd01-20321cd22ea5 | Status: 200 | Duration: 0.038s
INFO 2025-07-14 16:17:43,867 basehttp 40052 24936 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
INFO 2025-07-14 16:17:44,089 basehttp 40052 24936 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-07-14 16:17:46,897 logging 40052 24936 Request started - ID: fa092f94-e881-4e58-990b-def6cef77d9d | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:46,955 logging 40052 24936 Request completed - ID: fa092f94-e881-4e58-990b-def6cef77d9d | Status: 200 | Duration: 0.060s
INFO 2025-07-14 16:17:46,956 basehttp 40052 24936 "GET /admin/ HTTP/1.1" 200 34862
INFO 2025-07-14 16:17:49,910 logging 40052 24936 Request started - ID: a6723129-6435-47cf-adaa-e2593f059f02 | Method: GET | Path: /admin/authentication/loginattempt/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:50,006 logging 40052 24936 Request completed - ID: a6723129-6435-47cf-adaa-e2593f059f02 | Status: 200 | Duration: 0.112s
INFO 2025-07-14 16:17:50,007 basehttp 40052 24936 "GET /admin/authentication/loginattempt/ HTTP/1.1" 200 37047
INFO 2025-07-14 16:17:50,060 logging 40052 24936 Request started - ID: e229e946-72d4-4acd-a4a5-97bf5826d4b5 | Method: GET | Path: /admin/jsi18n/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:50,083 logging 40052 24936 Request completed - ID: e229e946-72d4-4acd-a4a5-97bf5826d4b5 | Status: 200 | Duration: 0.027s
INFO 2025-07-14 16:17:50,085 basehttp 40052 24936 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
INFO 2025-07-14 16:17:52,430 logging 40052 24936 Request started - ID: d554e448-7088-44e5-a10b-dd111dab5b68 | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:52,499 logging 40052 24936 Request completed - ID: d554e448-7088-44e5-a10b-dd111dab5b68 | Status: 200 | Duration: 0.073s
INFO 2025-07-14 16:17:52,500 basehttp 40052 24936 "GET /admin/ HTTP/1.1" 200 34862
INFO 2025-07-14 16:17:53,816 logging 40052 24936 Request started - ID: 96e587a1-3329-48c4-9225-1fff04a15386 | Method: GET | Path: /admin/permissions/userpermission/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:53,907 logging 40052 24936 Request completed - ID: 96e587a1-3329-48c4-9225-1fff04a15386 | Status: 200 | Duration: 0.096s
INFO 2025-07-14 16:17:53,908 basehttp 40052 24936 "GET /admin/permissions/userpermission/ HTTP/1.1" 200 35311
INFO 2025-07-14 16:17:53,968 logging 40052 24936 Request started - ID: ea0561ff-a94f-4883-92ef-f7ca9455bb90 | Method: GET | Path: /admin/jsi18n/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:53,988 logging 40052 24936 Request completed - ID: ea0561ff-a94f-4883-92ef-f7ca9455bb90 | Status: 200 | Duration: 0.025s
INFO 2025-07-14 16:17:53,989 basehttp 40052 24936 "GET /admin/jsi18n/ HTTP/1.1" 200 9327
INFO 2025-07-14 16:17:55,101 logging 40052 24936 Request started - ID: 61caa83c-7ae9-4c17-b2c1-fce3e240e8b6 | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-14 16:17:55,187 logging 40052 24936 Request completed - ID: 61caa83c-7ae9-4c17-b2c1-fce3e240e8b6 | Status: 200 | Duration: 0.089s
INFO 2025-07-14 16:17:55,188 basehttp 40052 24936 "GET /admin/ HTTP/1.1" 200 34862
INFO 2025-07-14 16:26:30,599 autoreload 40356 16952 Watching for file changes with StatReloader
ERROR 2025-07-14 16:26:59,597 log 24984 10624 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:26:59,631 log 24984 10624 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:26:59,668 log 24984 10624 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:33:24,101 log 32060 15932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:33:24,134 log 32060 15932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-14 16:33:24,168 log 32060 15932 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
           ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
INFO 2025-07-14 16:45:29,875 autoreload 32000 17464 Watching for file changes with StatReloader
INFO 2025-07-14 16:45:38,311 logging 32000 34888 Request started - ID: 837c0c1f-e9d0-4261-902f-7291ec3a0482 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:45:38,337 logging 32000 34888 Request completed - ID: 837c0c1f-e9d0-4261-902f-7291ec3a0482 | Status: 200 | Duration: 0.026s
INFO 2025-07-14 16:45:38,337 basehttp 32000 34888 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 16:45:38,342 logging 32000 18668 Request started - ID: ae2c9182-3cd4-494b-997a-b996a8767f51 | Method: GET | Path: /api/core/health | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:45:38,541 logging 32000 18668 Request completed - ID: ae2c9182-3cd4-494b-997a-b996a8767f51 | Status: 200 | Duration: 0.199s
INFO 2025-07-14 16:45:38,542 basehttp 32000 18668 "GET /api/core/health HTTP/1.1" 200 138
INFO 2025-07-14 16:45:38,546 logging 32000 12712 Request started - ID: 8282f533-108e-4557-abfe-e5b6995875e6 | Method: GET | Path: /api/core/info | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:45:38,642 logging 32000 12712 Request completed - ID: 8282f533-108e-4557-abfe-e5b6995875e6 | Status: 200 | Duration: 0.096s
INFO 2025-07-14 16:45:38,643 basehttp 32000 12712 "GET /api/core/info HTTP/1.1" 200 486
INFO 2025-07-14 16:45:38,648 logging 32000 28724 Request started - ID: 3ab02abb-87c1-4124-a429-117c36c0b429 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:45:38,663 logging 32000 28724 Request completed - ID: 3ab02abb-87c1-4124-a429-117c36c0b429 | Status: 200 | Duration: 0.015s
INFO 2025-07-14 16:45:38,664 basehttp 32000 28724 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 16:45:38,668 logging 32000 11160 Request started - ID: 01ba05cb-3ed1-4f81-8e14-18be091d5456 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:45:38,693 logging 32000 11160 Request completed - ID: 01ba05cb-3ed1-4f81-8e14-18be091d5456 | Status: 400 | Duration: 0.025s
WARNING 2025-07-14 16:45:38,693 log 32000 11160 Bad Request: /api/auth/register
WARNING 2025-07-14 16:45:38,694 basehttp 32000 11160 "POST /api/auth/register HTTP/1.1" 400 49
INFO 2025-07-14 16:45:38,698 logging 32000 33876 Request started - ID: 94a337ca-806f-4ed7-aafe-94a56e82092a | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:45:39,996 logging 32000 33876 Request completed - ID: 94a337ca-806f-4ed7-aafe-94a56e82092a | Status: 401 | Duration: 1.299s
WARNING 2025-07-14 16:45:39,996 log 32000 33876 Unauthorized: /api/auth/login
WARNING 2025-07-14 16:45:39,997 basehttp 32000 33876 "POST /api/auth/login HTTP/1.1" 401 51
INFO 2025-07-14 16:51:35,496 logging 32000 22136 Request started - ID: ed89f5c8-f271-4daa-9334-5e2bbe6c0bb9 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:35,510 logging 32000 22136 Request completed - ID: ed89f5c8-f271-4daa-9334-5e2bbe6c0bb9 | Status: 200 | Duration: 0.014s
INFO 2025-07-14 16:51:35,512 basehttp 32000 22136 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 16:51:35,516 logging 32000 3888 Request started - ID: 567ba188-ce65-482a-8592-4837e657ccf4 | Method: GET | Path: /api/core/health | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:35,533 logging 32000 3888 Request completed - ID: 567ba188-ce65-482a-8592-4837e657ccf4 | Status: 200 | Duration: 0.017s
INFO 2025-07-14 16:51:35,534 basehttp 32000 3888 "GET /api/core/health HTTP/1.1" 200 138
INFO 2025-07-14 16:51:35,536 logging 32000 36636 Request started - ID: abfbf503-913d-49df-8fac-e2a84c525f7f | Method: GET | Path: /api/core/info | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:35,548 logging 32000 36636 Request completed - ID: abfbf503-913d-49df-8fac-e2a84c525f7f | Status: 200 | Duration: 0.013s
INFO 2025-07-14 16:51:35,550 basehttp 32000 36636 "GET /api/core/info HTTP/1.1" 200 486
INFO 2025-07-14 16:51:35,554 logging 32000 40196 Request started - ID: c93dafa7-b3f7-435f-b8a4-7a8a0af35863 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:35,568 logging 32000 40196 Request completed - ID: c93dafa7-b3f7-435f-b8a4-7a8a0af35863 | Status: 200 | Duration: 0.013s
INFO 2025-07-14 16:51:35,569 basehttp 32000 40196 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 16:51:35,572 logging 32000 24816 Request started - ID: f5c093a2-0583-4f40-a2e9-5c4a1f0842fd | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:36,542 logging 32000 24816 Request completed - ID: f5c093a2-0583-4f40-a2e9-5c4a1f0842fd | Status: 200 | Duration: 0.969s
INFO 2025-07-14 16:51:36,543 basehttp 32000 24816 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-14 16:51:36,546 logging 32000 12132 Request started - ID: b692340a-c564-471d-b261-6842b8e55ed8 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,658 logging 32000 12132 Request completed - ID: b692340a-c564-471d-b261-6842b8e55ed8 | Status: 200 | Duration: 1.112s
INFO 2025-07-14 16:51:37,660 basehttp 32000 12132 "POST /api/auth/login HTTP/1.1" 200 717
INFO 2025-07-14 16:51:37,664 logging 32000 31328 Request started - ID: 79c586ae-be32-477e-a2c8-5ab5a3d3f65e | Method: GET | Path: /api/auth/me | User:  | IP: 127.0.0.1
ERROR 2025-07-14 16:51:37,671 errors 32000 31328 1 validation error for UserProfileResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 7...o=datetime.timezone.utc), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\authentication\api.py", line 133, in get_current_user
    return UserProfileResponse.from_orm(request.auth)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 1440, in from_orm
    return cls.model_validate(obj)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for UserProfileResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 7...o=datetime.timezone.utc), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
INFO 2025-07-14 16:51:37,702 logging 32000 31328 Request completed - ID: 79c586ae-be32-477e-a2c8-5ab5a3d3f65e | Status: 500 | Duration: 0.039s
ERROR 2025-07-14 16:51:37,703 log 32000 31328 Internal Server Error: /api/auth/me
ERROR 2025-07-14 16:51:37,703 basehttp 32000 31328 "GET /api/auth/me HTTP/1.1" 500 1191
INFO 2025-07-14 16:51:37,708 logging 32000 9580 Request started - ID: a2fdea4a-fa1c-4b01-87e6-f6a6cdac512c | Method: GET | Path: /api/auth/verify-token | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,739 logging 32000 9580 Request completed - ID: a2fdea4a-fa1c-4b01-87e6-f6a6cdac512c | Status: 200 | Duration: 0.031s
INFO 2025-07-14 16:51:37,741 basehttp 32000 9580 "GET /api/auth/verify-token?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUyNDgzOTk3LCJpYXQiOjE3NTI0ODMwOTcsImp0aSI6Ijc2NDBhZDAzOGJhZTQ3ZjY5MzczNjM5MDExZDU4YTYwIiwidXNlcl9pZCI6M30.Q7tvMvUAwg_gPZ7td19nV13RtLdH69tqx1YvMk9UVzw HTTP/1.1" 200 67
INFO 2025-07-14 16:51:37,744 logging 32000 4936 Request started - ID: 157faf65-968f-44fe-a93a-b2b3d0f2bb6a | Method: POST | Path: /api/auth/refresh | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,766 logging 32000 4936 Request completed - ID: 157faf65-968f-44fe-a93a-b2b3d0f2bb6a | Status: 200 | Duration: 0.022s
INFO 2025-07-14 16:51:37,767 basehttp 32000 4936 "POST /api/auth/refresh HTTP/1.1" 200 291
INFO 2025-07-14 16:51:37,770 logging 32000 16716 Request started - ID: 0cc3c36e-54d0-4511-9ebd-e5f1976fc1b9 | Method: GET | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,789 logging 32000 16716 Request completed - ID: 0cc3c36e-54d0-4511-9ebd-e5f1976fc1b9 | Status: 200 | Duration: 0.019s
INFO 2025-07-14 16:51:37,790 basehttp 32000 16716 "GET /api/users/me HTTP/1.1" 200 331
INFO 2025-07-14 16:51:37,794 logging 32000 35504 Request started - ID: 0461eb93-77e0-4e24-8d77-38cdd19e4969 | Method: PUT | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,824 logging 32000 35504 Request completed - ID: 0461eb93-77e0-4e24-8d77-38cdd19e4969 | Status: 200 | Duration: 0.030s
INFO 2025-07-14 16:51:37,825 basehttp 32000 35504 "PUT /api/users/me HTTP/1.1" 200 357
INFO 2025-07-14 16:51:37,829 logging 32000 11256 Request started - ID: 9e62646d-dc2a-4553-bd1b-80c49f5a8eaf | Method: GET | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,869 logging 32000 11256 Request completed - ID: 9e62646d-dc2a-4553-bd1b-80c49f5a8eaf | Status: 200 | Duration: 0.042s
INFO 2025-07-14 16:51:37,870 basehttp 32000 11256 "GET /api/users/me/profile HTTP/1.1" 200 604
INFO 2025-07-14 16:51:37,873 logging 32000 33628 Request started - ID: 48580720-3595-4f50-9fcb-bc1604b1ccf0 | Method: PUT | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,900 logging 32000 33628 Request completed - ID: 48580720-3595-4f50-9fcb-bc1604b1ccf0 | Status: 200 | Duration: 0.027s
INFO 2025-07-14 16:51:37,901 basehttp 32000 33628 "PUT /api/users/me/profile HTTP/1.1" 200 639
INFO 2025-07-14 16:51:37,904 logging 32000 28572 Request started - ID: 5d76bd22-8669-4672-9383-fb40df50d246 | Method: GET | Path: /api/users/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,935 logging 32000 28572 Request completed - ID: 5d76bd22-8669-4672-9383-fb40df50d246 | Status: 200 | Duration: 0.031s
INFO 2025-07-14 16:51:37,936 basehttp 32000 28572 "GET /api/users/?page=1&per_page=10 HTTP/1.1" 200 1050
INFO 2025-07-14 16:51:37,939 logging 32000 38416 Request started - ID: 76e1ac36-808e-48f9-8ac2-eff0dc8951b1 | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:37,978 logging 32000 38416 Request completed - ID: 76e1ac36-808e-48f9-8ac2-eff0dc8951b1 | Status: 403 | Duration: 0.039s
WARNING 2025-07-14 16:51:37,978 log 32000 38416 Forbidden: /api/permissions/permissions
WARNING 2025-07-14 16:51:37,979 basehttp 32000 38416 "GET /api/permissions/permissions HTTP/1.1" 403 49
INFO 2025-07-14 16:51:37,982 logging 32000 37200 Request started - ID: dd1ca96b-bb86-47de-aaa0-63762e5d2958 | Method: GET | Path: /api/permissions/roles | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,013 logging 32000 37200 Request completed - ID: dd1ca96b-bb86-47de-aaa0-63762e5d2958 | Status: 403 | Duration: 0.031s
WARNING 2025-07-14 16:51:38,014 log 32000 37200 Forbidden: /api/permissions/roles
WARNING 2025-07-14 16:51:38,014 basehttp 32000 37200 "GET /api/permissions/roles HTTP/1.1" 403 43
INFO 2025-07-14 16:51:38,019 logging 32000 28576 Request started - ID: 90d3cce7-2d78-4eda-8328-c4e82c072de0 | Method: GET | Path: /api/permissions/my-permissions | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,084 logging 32000 28576 Request completed - ID: 90d3cce7-2d78-4eda-8328-c4e82c072de0 | Status: 404 | Duration: 0.065s
WARNING 2025-07-14 16:51:38,085 log 32000 28576 Not Found: /api/permissions/my-permissions
WARNING 2025-07-14 16:51:38,086 basehttp 32000 28576 "GET /api/permissions/my-permissions HTTP/1.1" 404 38309
INFO 2025-07-14 16:51:38,089 logging 32000 14528 Request started - ID: 47adc215-b5db-418e-8546-866384d4644d | Method: GET | Path: /api/storage/files/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,154 logging 32000 14528 Request completed - ID: 47adc215-b5db-418e-8546-866384d4644d | Status: 404 | Duration: 0.065s
WARNING 2025-07-14 16:51:38,155 log 32000 14528 Not Found: /api/storage/files/
WARNING 2025-07-14 16:51:38,156 basehttp 32000 14528 "GET /api/storage/files/?page=1&per_page=10 HTTP/1.1" 404 38307
INFO 2025-07-14 16:51:38,160 logging 32000 32860 Request started - ID: e52d2f4e-e127-4218-9257-08cfb4ebd051 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,193 logging 32000 32860 Request completed - ID: e52d2f4e-e127-4218-9257-08cfb4ebd051 | Status: 403 | Duration: 0.033s
WARNING 2025-07-14 16:51:38,193 log 32000 32860 Forbidden: /api/storage/stats
WARNING 2025-07-14 16:51:38,194 basehttp 32000 32860 "GET /api/storage/stats HTTP/1.1" 403 51
INFO 2025-07-14 16:51:38,198 logging 32000 27372 Request started - ID: fea10f83-9841-4221-a920-299e749502fb | Method: GET | Path: /api/storage/categories | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,243 logging 32000 27372 Request completed - ID: fea10f83-9841-4221-a920-299e749502fb | Status: 404 | Duration: 0.046s
WARNING 2025-07-14 16:51:38,244 log 32000 27372 Not Found: /api/storage/categories
WARNING 2025-07-14 16:51:38,245 basehttp 32000 27372 "GET /api/storage/categories HTTP/1.1" 404 38277
INFO 2025-07-14 16:51:38,249 logging 32000 36236 Request started - ID: ee1050eb-7fda-4409-8ae0-dcbf30cd044f | Method: GET | Path: /api/i18n/languages | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,274 logging 32000 36236 Request completed - ID: ee1050eb-7fda-4409-8ae0-dcbf30cd044f | Status: 200 | Duration: 0.025s
INFO 2025-07-14 16:51:38,279 basehttp 32000 36236 "GET /api/i18n/languages HTTP/1.1" 200 246
INFO 2025-07-14 16:51:38,283 logging 32000 19072 Request started - ID: 433a741a-866e-4962-995b-c9a7f45cf4c3 | Method: GET | Path: /api/i18n/translations/en | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,316 logging 32000 19072 Request completed - ID: 433a741a-866e-4962-995b-c9a7f45cf4c3 | Status: 200 | Duration: 0.034s
INFO 2025-07-14 16:51:38,317 basehttp 32000 19072 "GET /api/i18n/translations/en HTTP/1.1" 200 2
INFO 2025-07-14 16:51:38,321 logging 32000 20996 Request started - ID: edde3779-60da-4bb6-b80e-cfbc273677d1 | Method: GET | Path: /api/i18n/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:51:38,336 logging 32000 20996 Request completed - ID: edde3779-60da-4bb6-b80e-cfbc273677d1 | Status: 401 | Duration: 0.015s
WARNING 2025-07-14 16:51:38,337 log 32000 20996 Unauthorized: /api/i18n/stats
WARNING 2025-07-14 16:51:38,338 basehttp 32000 20996 "GET /api/i18n/stats HTTP/1.1" 401 37
INFO 2025-07-14 16:56:38,128 autoreload 28052 16784 Watching for file changes with StatReloader
INFO 2025-07-14 16:56:45,710 logging 28052 23176 Request started - ID: f9b695bc-c9bb-47ad-9896-06273c55a055 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:45,765 logging 28052 23176 Request completed - ID: f9b695bc-c9bb-47ad-9896-06273c55a055 | Status: 200 | Duration: 0.056s
INFO 2025-07-14 16:56:45,772 basehttp 28052 23176 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 16:56:45,781 logging 28052 24080 Request started - ID: 0cb32e77-6d1e-4698-bdb6-4ad1453ae751 | Method: GET | Path: /api/core/health | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:45,942 logging 28052 24080 Request completed - ID: 0cb32e77-6d1e-4698-bdb6-4ad1453ae751 | Status: 200 | Duration: 0.160s
INFO 2025-07-14 16:56:45,943 basehttp 28052 24080 "GET /api/core/health HTTP/1.1" 200 138
INFO 2025-07-14 16:56:45,947 logging 28052 34832 Request started - ID: 817ba025-337f-4788-8ef0-7fec8cfee8db | Method: GET | Path: /api/core/info | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:46,026 logging 28052 34832 Request completed - ID: 817ba025-337f-4788-8ef0-7fec8cfee8db | Status: 200 | Duration: 0.079s
INFO 2025-07-14 16:56:46,026 basehttp 28052 34832 "GET /api/core/info HTTP/1.1" 200 486
INFO 2025-07-14 16:56:46,030 logging 28052 23672 Request started - ID: 136d2656-aaf6-4264-aaa8-89de40742783 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:46,044 logging 28052 23672 Request completed - ID: 136d2656-aaf6-4264-aaa8-89de40742783 | Status: 200 | Duration: 0.014s
INFO 2025-07-14 16:56:46,045 basehttp 28052 23672 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 16:56:46,048 logging 28052 12104 Request started - ID: 5a8ed696-fc6b-4bfe-9d64-8a881ab31b36 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:47,042 logging 28052 12104 Request completed - ID: 5a8ed696-fc6b-4bfe-9d64-8a881ab31b36 | Status: 200 | Duration: 0.994s
INFO 2025-07-14 16:56:47,042 basehttp 28052 12104 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-14 16:56:47,046 logging 28052 27820 Request started - ID: f835d395-9275-40e7-8a73-31608e190133 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,075 logging 28052 27820 Request completed - ID: f835d395-9275-40e7-8a73-31608e190133 | Status: 200 | Duration: 1.029s
INFO 2025-07-14 16:56:48,076 basehttp 28052 27820 "POST /api/auth/login HTTP/1.1" 200 717
INFO 2025-07-14 16:56:48,080 logging 28052 40788 Request started - ID: 87135221-c442-426d-bf10-33435085ab90 | Method: GET | Path: /api/auth/me | User:  | IP: 127.0.0.1
ERROR 2025-07-14 16:56:48,086 errors 28052 40788 1 validation error for UserProfileResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 7...o=datetime.timezone.utc), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\authentication\api.py", line 133, in get_current_user
    return UserProfileResponse.from_orm(request.auth)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 1440, in from_orm
    return cls.model_validate(obj)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for UserProfileResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 7...o=datetime.timezone.utc), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
INFO 2025-07-14 16:56:48,126 logging 28052 40788 Request completed - ID: 87135221-c442-426d-bf10-33435085ab90 | Status: 500 | Duration: 0.046s
ERROR 2025-07-14 16:56:48,128 log 28052 40788 Internal Server Error: /api/auth/me
ERROR 2025-07-14 16:56:48,129 basehttp 28052 40788 "GET /api/auth/me HTTP/1.1" 500 1191
INFO 2025-07-14 16:56:48,133 logging 28052 28536 Request started - ID: 5e46852e-8854-4384-b56b-f0fbb7736faf | Method: GET | Path: /api/auth/verify-token | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,168 logging 28052 28536 Request completed - ID: 5e46852e-8854-4384-b56b-f0fbb7736faf | Status: 200 | Duration: 0.035s
INFO 2025-07-14 16:56:48,169 basehttp 28052 28536 "GET /api/auth/verify-token?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUyNDg0MzA3LCJpYXQiOjE3NTI0ODM0MDcsImp0aSI6ImE4NWI5ZDhjYzY5ZjQwOWFhNjc3Zjg1OGNmZmNjZWE0IiwidXNlcl9pZCI6M30.TrZbhGBFvXP8nUS4bsFv7CE-rNL_nDGMnAN6gr-Ijw0 HTTP/1.1" 200 67
INFO 2025-07-14 16:56:48,174 logging 28052 40852 Request started - ID: cb7cd078-f21a-43b6-b598-ab057fe59dd6 | Method: POST | Path: /api/auth/refresh | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,202 logging 28052 40852 Request completed - ID: cb7cd078-f21a-43b6-b598-ab057fe59dd6 | Status: 200 | Duration: 0.028s
INFO 2025-07-14 16:56:48,203 basehttp 28052 40852 "POST /api/auth/refresh HTTP/1.1" 200 291
INFO 2025-07-14 16:56:48,206 logging 28052 3064 Request started - ID: fa911c8c-f39d-42a3-b359-7c9c666d655e | Method: GET | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,222 logging 28052 3064 Request completed - ID: fa911c8c-f39d-42a3-b359-7c9c666d655e | Status: 200 | Duration: 0.017s
INFO 2025-07-14 16:56:48,223 basehttp 28052 3064 "GET /api/users/me HTTP/1.1" 200 331
INFO 2025-07-14 16:56:48,226 logging 28052 12616 Request started - ID: 63e935db-aefc-4c10-bf8c-223c9014f998 | Method: PUT | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,249 logging 28052 12616 Request completed - ID: 63e935db-aefc-4c10-bf8c-223c9014f998 | Status: 200 | Duration: 0.023s
INFO 2025-07-14 16:56:48,250 basehttp 28052 12616 "PUT /api/users/me HTTP/1.1" 200 357
INFO 2025-07-14 16:56:48,253 logging 28052 40772 Request started - ID: ef9d46d6-1e5e-43d7-bc81-e7e0f70d231a | Method: GET | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,287 logging 28052 40772 Request completed - ID: ef9d46d6-1e5e-43d7-bc81-e7e0f70d231a | Status: 200 | Duration: 0.034s
INFO 2025-07-14 16:56:48,288 basehttp 28052 40772 "GET /api/users/me/profile HTTP/1.1" 200 604
INFO 2025-07-14 16:56:48,291 logging 28052 30216 Request started - ID: 6e0f1473-c13f-4114-9556-64b70fb94d91 | Method: PUT | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,318 logging 28052 30216 Request completed - ID: 6e0f1473-c13f-4114-9556-64b70fb94d91 | Status: 200 | Duration: 0.027s
INFO 2025-07-14 16:56:48,319 basehttp 28052 30216 "PUT /api/users/me/profile HTTP/1.1" 200 639
INFO 2025-07-14 16:56:48,322 logging 28052 38548 Request started - ID: 897fc653-11e6-476e-8b06-716e5e5633d5 | Method: GET | Path: /api/users/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,350 logging 28052 38548 Request completed - ID: 897fc653-11e6-476e-8b06-716e5e5633d5 | Status: 200 | Duration: 0.028s
INFO 2025-07-14 16:56:48,352 basehttp 28052 38548 "GET /api/users/?page=1&per_page=10 HTTP/1.1" 200 1050
INFO 2025-07-14 16:56:48,355 logging 28052 6568 Request started - ID: 3343902e-874b-4a50-85a1-cf585ef372a1 | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,392 logging 28052 6568 Request completed - ID: 3343902e-874b-4a50-85a1-cf585ef372a1 | Status: 403 | Duration: 0.036s
WARNING 2025-07-14 16:56:48,392 log 28052 6568 Forbidden: /api/permissions/permissions
WARNING 2025-07-14 16:56:48,393 basehttp 28052 6568 "GET /api/permissions/permissions HTTP/1.1" 403 49
INFO 2025-07-14 16:56:48,396 logging 28052 15788 Request started - ID: 0eb8f507-4d55-45d5-9480-afd5ef5d44a6 | Method: GET | Path: /api/permissions/roles | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,424 logging 28052 15788 Request completed - ID: 0eb8f507-4d55-45d5-9480-afd5ef5d44a6 | Status: 403 | Duration: 0.028s
WARNING 2025-07-14 16:56:48,425 log 28052 15788 Forbidden: /api/permissions/roles
WARNING 2025-07-14 16:56:48,425 basehttp 28052 15788 "GET /api/permissions/roles HTTP/1.1" 403 43
INFO 2025-07-14 16:56:48,428 logging 28052 24092 Request started - ID: bacb50fb-e5f5-4c97-9038-743d3fb235a7 | Method: GET | Path: /api/permissions/my-permissions | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,471 logging 28052 24092 Request completed - ID: bacb50fb-e5f5-4c97-9038-743d3fb235a7 | Status: 404 | Duration: 0.044s
WARNING 2025-07-14 16:56:48,471 log 28052 24092 Not Found: /api/permissions/my-permissions
WARNING 2025-07-14 16:56:48,472 basehttp 28052 24092 "GET /api/permissions/my-permissions HTTP/1.1" 404 38309
INFO 2025-07-14 16:56:48,476 logging 28052 31752 Request started - ID: a8b09921-1fec-4fab-ae08-60b265bcef5f | Method: GET | Path: /api/storage/files/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,520 logging 28052 31752 Request completed - ID: a8b09921-1fec-4fab-ae08-60b265bcef5f | Status: 404 | Duration: 0.044s
WARNING 2025-07-14 16:56:48,520 log 28052 31752 Not Found: /api/storage/files/
WARNING 2025-07-14 16:56:48,521 basehttp 28052 31752 "GET /api/storage/files/?page=1&per_page=10 HTTP/1.1" 404 38307
INFO 2025-07-14 16:56:48,525 logging 28052 25028 Request started - ID: df990d40-5498-443e-b1d7-1477fff774e0 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,553 logging 28052 25028 Request completed - ID: df990d40-5498-443e-b1d7-1477fff774e0 | Status: 403 | Duration: 0.028s
WARNING 2025-07-14 16:56:48,554 log 28052 25028 Forbidden: /api/storage/stats
WARNING 2025-07-14 16:56:48,555 basehttp 28052 25028 "GET /api/storage/stats HTTP/1.1" 403 51
INFO 2025-07-14 16:56:48,558 logging 28052 13256 Request started - ID: 60ae9d89-d38a-4c48-a7d2-921af0de3e5c | Method: GET | Path: /api/storage/categories | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,641 logging 28052 13256 Request completed - ID: 60ae9d89-d38a-4c48-a7d2-921af0de3e5c | Status: 404 | Duration: 0.083s
WARNING 2025-07-14 16:56:48,641 log 28052 13256 Not Found: /api/storage/categories
WARNING 2025-07-14 16:56:48,642 basehttp 28052 13256 "GET /api/storage/categories HTTP/1.1" 404 38277
INFO 2025-07-14 16:56:48,648 logging 28052 15768 Request started - ID: a1e510c1-b311-42df-a477-b45e0fa607ef | Method: GET | Path: /api/i18n/languages | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,674 logging 28052 15768 Request completed - ID: a1e510c1-b311-42df-a477-b45e0fa607ef | Status: 200 | Duration: 0.027s
INFO 2025-07-14 16:56:48,675 basehttp 28052 15768 "GET /api/i18n/languages HTTP/1.1" 200 246
INFO 2025-07-14 16:56:48,679 logging 28052 30396 Request started - ID: d486043d-cafb-4b4d-8206-6b8fdc873bd3 | Method: GET | Path: /api/i18n/translations/en | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,708 logging 28052 30396 Request completed - ID: d486043d-cafb-4b4d-8206-6b8fdc873bd3 | Status: 200 | Duration: 0.029s
INFO 2025-07-14 16:56:48,709 basehttp 28052 30396 "GET /api/i18n/translations/en HTTP/1.1" 200 2
INFO 2025-07-14 16:56:48,712 logging 28052 13396 Request started - ID: 3c7411c8-a0a5-492d-a5ba-7d73778c7b30 | Method: GET | Path: /api/i18n/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 16:56:48,723 logging 28052 13396 Request completed - ID: 3c7411c8-a0a5-492d-a5ba-7d73778c7b30 | Status: 401 | Duration: 0.011s
WARNING 2025-07-14 16:56:48,723 log 28052 13396 Unauthorized: /api/i18n/stats
WARNING 2025-07-14 16:56:48,724 basehttp 28052 13396 "GET /api/i18n/stats HTTP/1.1" 401 37
INFO 2025-07-14 17:07:38,869 autoreload 28052 16784 F:\PycharmProjects\django_ninja_template\apps\authentication\api.py changed, reloading.
INFO 2025-07-14 17:07:41,586 autoreload 40320 12632 Watching for file changes with StatReloader
INFO 2025-07-14 17:08:06,523 autoreload 40320 12632 F:\PycharmProjects\django_ninja_template\apps\permissions\api.py changed, reloading.
INFO 2025-07-14 17:08:08,492 autoreload 34204 23512 Watching for file changes with StatReloader
INFO 2025-07-14 17:08:19,111 autoreload 34204 23512 F:\PycharmProjects\django_ninja_template\apps\storage\api.py changed, reloading.
INFO 2025-07-14 17:08:21,330 autoreload 37664 12408 Watching for file changes with StatReloader
INFO 2025-07-14 17:09:54,130 logging 37664 7460 Request started - ID: 683ad801-90bb-4be2-b324-d3912db90423 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:54,153 logging 37664 7460 Request completed - ID: 683ad801-90bb-4be2-b324-d3912db90423 | Status: 200 | Duration: 0.023s
INFO 2025-07-14 17:09:54,154 basehttp 37664 7460 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 17:09:54,158 logging 37664 34248 Request started - ID: 2c602890-7306-4696-babd-7144e13ad1fa | Method: GET | Path: /api/core/health | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:54,357 logging 37664 34248 Request completed - ID: 2c602890-7306-4696-babd-7144e13ad1fa | Status: 200 | Duration: 0.199s
INFO 2025-07-14 17:09:54,358 basehttp 37664 34248 "GET /api/core/health HTTP/1.1" 200 138
INFO 2025-07-14 17:09:54,361 logging 37664 25396 Request started - ID: 3778039e-649b-4b9f-8475-0e8b3820e720 | Method: GET | Path: /api/core/info | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:54,445 logging 37664 25396 Request completed - ID: 3778039e-649b-4b9f-8475-0e8b3820e720 | Status: 200 | Duration: 0.084s
INFO 2025-07-14 17:09:54,446 basehttp 37664 25396 "GET /api/core/info HTTP/1.1" 200 486
INFO 2025-07-14 17:09:54,450 logging 37664 13376 Request started - ID: 9cd6d611-c3f8-4432-82c1-0ae869985cda | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:54,465 logging 37664 13376 Request completed - ID: 9cd6d611-c3f8-4432-82c1-0ae869985cda | Status: 200 | Duration: 0.015s
INFO 2025-07-14 17:09:54,466 basehttp 37664 13376 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 17:09:54,470 logging 37664 26328 Request started - ID: 7ec9cec9-100e-442c-932d-3d6a85e3ff3f | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:55,545 logging 37664 26328 Request completed - ID: 7ec9cec9-100e-442c-932d-3d6a85e3ff3f | Status: 200 | Duration: 1.076s
INFO 2025-07-14 17:09:55,546 basehttp 37664 26328 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-14 17:09:55,550 logging 37664 17500 Request started - ID: 142c9a1e-5be0-4a2b-9869-6899f7c00f20 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,519 logging 37664 17500 Request completed - ID: 142c9a1e-5be0-4a2b-9869-6899f7c00f20 | Status: 200 | Duration: 0.970s
INFO 2025-07-14 17:09:56,520 basehttp 37664 17500 "POST /api/auth/login HTTP/1.1" 200 717
INFO 2025-07-14 17:09:56,523 logging 37664 25612 Request started - ID: 78c2e0e3-3c0b-489f-b7a0-f4c1eae16470 | Method: GET | Path: /api/auth/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,550 logging 37664 25612 Request completed - ID: 78c2e0e3-3c0b-489f-b7a0-f4c1eae16470 | Status: 200 | Duration: 0.027s
INFO 2025-07-14 17:09:56,551 basehttp 37664 25612 "GET /api/auth/me HTTP/1.1" 200 216
INFO 2025-07-14 17:09:56,554 logging 37664 37964 Request started - ID: ec6830b2-142d-4404-a0da-4de61d3c7b6e | Method: GET | Path: /api/auth/verify-token | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,596 logging 37664 37964 Request completed - ID: ec6830b2-142d-4404-a0da-4de61d3c7b6e | Status: 200 | Duration: 0.042s
INFO 2025-07-14 17:09:56,597 basehttp 37664 37964 "GET /api/auth/verify-token?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUyNDg1MDk2LCJpYXQiOjE3NTI0ODQxOTYsImp0aSI6ImZmNTBlMzlkNmQ0ZDQ5MDY5YmVjZDg2MWFhZjkzMTg1IiwidXNlcl9pZCI6NH0.p_umkA-mP2A9Dy8hZvKxWx75Zcte0BkYnnO0xxjIK-Q HTTP/1.1" 200 67
INFO 2025-07-14 17:09:56,601 logging 37664 23320 Request started - ID: 810fa1b8-fd9a-48e6-a3d5-c7ac6109f63a | Method: POST | Path: /api/auth/refresh | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,641 logging 37664 23320 Request completed - ID: 810fa1b8-fd9a-48e6-a3d5-c7ac6109f63a | Status: 200 | Duration: 0.040s
INFO 2025-07-14 17:09:56,641 basehttp 37664 23320 "POST /api/auth/refresh HTTP/1.1" 200 291
INFO 2025-07-14 17:09:56,646 logging 37664 13848 Request started - ID: 8227e7ae-05bd-4f19-ab40-9561a7d6b073 | Method: GET | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,671 logging 37664 13848 Request completed - ID: 8227e7ae-05bd-4f19-ab40-9561a7d6b073 | Status: 200 | Duration: 0.025s
INFO 2025-07-14 17:09:56,672 basehttp 37664 13848 "GET /api/users/me HTTP/1.1" 200 331
INFO 2025-07-14 17:09:56,676 logging 37664 20432 Request started - ID: 86b2f841-2bd8-4498-9430-9255f3852ec1 | Method: PUT | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,708 logging 37664 20432 Request completed - ID: 86b2f841-2bd8-4498-9430-9255f3852ec1 | Status: 200 | Duration: 0.033s
INFO 2025-07-14 17:09:56,709 basehttp 37664 20432 "PUT /api/users/me HTTP/1.1" 200 357
INFO 2025-07-14 17:09:56,713 logging 37664 15348 Request started - ID: 343917f5-6ae3-4a21-baf6-fca7d1eac683 | Method: GET | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,747 logging 37664 15348 Request completed - ID: 343917f5-6ae3-4a21-baf6-fca7d1eac683 | Status: 200 | Duration: 0.033s
INFO 2025-07-14 17:09:56,748 basehttp 37664 15348 "GET /api/users/me/profile HTTP/1.1" 200 604
INFO 2025-07-14 17:09:56,752 logging 37664 20928 Request started - ID: 253ff591-b295-49eb-9b3d-4d311c9119a6 | Method: PUT | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,777 logging 37664 20928 Request completed - ID: 253ff591-b295-49eb-9b3d-4d311c9119a6 | Status: 200 | Duration: 0.025s
INFO 2025-07-14 17:09:56,779 basehttp 37664 20928 "PUT /api/users/me/profile HTTP/1.1" 200 639
INFO 2025-07-14 17:09:56,782 logging 37664 13112 Request started - ID: ab6ee974-1c72-429d-93e4-aa47ee5de182 | Method: GET | Path: /api/users/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,811 logging 37664 13112 Request completed - ID: ab6ee974-1c72-429d-93e4-aa47ee5de182 | Status: 200 | Duration: 0.030s
INFO 2025-07-14 17:09:56,816 basehttp 37664 13112 "GET /api/users/?page=1&per_page=10 HTTP/1.1" 200 1050
INFO 2025-07-14 17:09:56,822 logging 37664 36544 Request started - ID: ac1a251c-0387-4345-8946-5b39dc8c6707 | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,862 logging 37664 36544 Request completed - ID: ac1a251c-0387-4345-8946-5b39dc8c6707 | Status: 403 | Duration: 0.040s
WARNING 2025-07-14 17:09:56,863 log 37664 36544 Forbidden: /api/permissions/permissions
WARNING 2025-07-14 17:09:56,864 basehttp 37664 36544 "GET /api/permissions/permissions HTTP/1.1" 403 49
INFO 2025-07-14 17:09:56,868 logging 37664 17336 Request started - ID: 7b53fb4e-9e47-4932-bf97-10f6f11eaa56 | Method: GET | Path: /api/permissions/roles | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,901 logging 37664 17336 Request completed - ID: 7b53fb4e-9e47-4932-bf97-10f6f11eaa56 | Status: 403 | Duration: 0.033s
WARNING 2025-07-14 17:09:56,901 log 37664 17336 Forbidden: /api/permissions/roles
WARNING 2025-07-14 17:09:56,902 basehttp 37664 17336 "GET /api/permissions/roles HTTP/1.1" 403 43
INFO 2025-07-14 17:09:56,905 logging 37664 24680 Request started - ID: 1d8bef05-f48e-4d68-8d5f-71b6c68cc626 | Method: GET | Path: /api/permissions/my-permissions | User:  | IP: 127.0.0.1
ERROR 2025-07-14 17:09:56,914 errors 37664 24680 type object 'PermissionService' has no attribute 'get_user_roles'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\permissions\api.py", line 38, in get_my_permissions
    roles = PermissionService.get_user_roles(user)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'PermissionService' has no attribute 'get_user_roles'
INFO 2025-07-14 17:09:56,938 logging 37664 24680 Request completed - ID: 1d8bef05-f48e-4d68-8d5f-71b6c68cc626 | Status: 500 | Duration: 0.033s
ERROR 2025-07-14 17:09:56,939 log 37664 24680 Internal Server Error: /api/permissions/my-permissions
ERROR 2025-07-14 17:09:56,940 basehttp 37664 24680 "GET /api/permissions/my-permissions HTTP/1.1" 500 524
INFO 2025-07-14 17:09:56,943 logging 37664 38832 Request started - ID: 2ffcfe8b-f45a-4076-b83f-192e74193107 | Method: GET | Path: /api/storage/files/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:56,990 logging 37664 38832 Request completed - ID: 2ffcfe8b-f45a-4076-b83f-192e74193107 | Status: 404 | Duration: 0.048s
WARNING 2025-07-14 17:09:56,991 log 37664 38832 Not Found: /api/storage/files/
WARNING 2025-07-14 17:09:56,992 basehttp 37664 38832 "GET /api/storage/files/?page=1&per_page=10 HTTP/1.1" 404 38881
INFO 2025-07-14 17:09:56,996 logging 37664 27944 Request started - ID: e511f3dc-326d-4884-a0e8-e20d785512f2 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:57,027 logging 37664 27944 Request completed - ID: e511f3dc-326d-4884-a0e8-e20d785512f2 | Status: 403 | Duration: 0.031s
WARNING 2025-07-14 17:09:57,027 log 37664 27944 Forbidden: /api/storage/stats
WARNING 2025-07-14 17:09:57,028 basehttp 37664 27944 "GET /api/storage/stats HTTP/1.1" 403 51
INFO 2025-07-14 17:09:57,031 logging 37664 26632 Request started - ID: 4bb925b7-c45d-46e5-8ca0-1d38a03384f5 | Method: GET | Path: /api/storage/categories | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:57,059 logging 37664 26632 Request completed - ID: 4bb925b7-c45d-46e5-8ca0-1d38a03384f5 | Status: 200 | Duration: 0.028s
INFO 2025-07-14 17:09:57,060 basehttp 37664 26632 "GET /api/storage/categories HTTP/1.1" 200 173
INFO 2025-07-14 17:09:57,065 logging 37664 32104 Request started - ID: a48f4dc5-4d78-4241-934e-53ac2cb5c7cc | Method: GET | Path: /api/i18n/languages | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:57,105 logging 37664 32104 Request completed - ID: a48f4dc5-4d78-4241-934e-53ac2cb5c7cc | Status: 200 | Duration: 0.041s
INFO 2025-07-14 17:09:57,106 basehttp 37664 32104 "GET /api/i18n/languages HTTP/1.1" 200 246
INFO 2025-07-14 17:09:57,110 logging 37664 8660 Request started - ID: 628a3324-1778-4e01-b48a-f8ad04924ca7 | Method: GET | Path: /api/i18n/translations/en | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:57,154 logging 37664 8660 Request completed - ID: 628a3324-1778-4e01-b48a-f8ad04924ca7 | Status: 200 | Duration: 0.044s
INFO 2025-07-14 17:09:57,155 basehttp 37664 8660 "GET /api/i18n/translations/en HTTP/1.1" 200 2
INFO 2025-07-14 17:09:57,159 logging 37664 27496 Request started - ID: 4b29f960-e02a-4554-b7f0-6386c7c22348 | Method: GET | Path: /api/i18n/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:09:57,174 logging 37664 27496 Request completed - ID: 4b29f960-e02a-4554-b7f0-6386c7c22348 | Status: 401 | Duration: 0.014s
WARNING 2025-07-14 17:09:57,175 log 37664 27496 Unauthorized: /api/i18n/stats
WARNING 2025-07-14 17:09:57,176 basehttp 37664 27496 "GET /api/i18n/stats HTTP/1.1" 401 37
INFO 2025-07-14 17:11:49,678 autoreload 13596 30868 Watching for file changes with StatReloader
INFO 2025-07-14 17:24:13,623 autoreload 13596 30868 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 17:24:16,053 autoreload 21328 14640 Watching for file changes with StatReloader
INFO 2025-07-14 17:27:56,276 autoreload 21328 14640 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 17:27:58,414 autoreload 19444 19372 Watching for file changes with StatReloader
INFO 2025-07-14 17:29:02,553 autoreload 19444 19372 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 17:29:04,580 autoreload 25124 35836 Watching for file changes with StatReloader
INFO 2025-07-14 17:50:31,825 autoreload 35864 12492 Watching for file changes with StatReloader
INFO 2025-07-14 17:50:41,074 logging 35864 23000 Request started - ID: e3ed0eb3-297b-4596-a324-01e4385d28d8 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:41,096 logging 35864 23000 Request completed - ID: e3ed0eb3-297b-4596-a324-01e4385d28d8 | Status: 200 | Duration: 0.023s
INFO 2025-07-14 17:50:41,097 basehttp 35864 23000 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 17:50:41,101 logging 35864 30480 Request started - ID: b78d6359-de36-456c-bb89-832d0e35aefc | Method: GET | Path: /api/core/health | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:41,273 logging 35864 30480 Request completed - ID: b78d6359-de36-456c-bb89-832d0e35aefc | Status: 200 | Duration: 0.171s
INFO 2025-07-14 17:50:41,274 basehttp 35864 30480 "GET /api/core/health HTTP/1.1" 200 138
INFO 2025-07-14 17:50:41,276 logging 35864 6108 Request started - ID: 31501a39-dfe8-4a46-b5ec-fd6e9d9b9d84 | Method: GET | Path: /api/core/info | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:41,350 logging 35864 6108 Request completed - ID: 31501a39-dfe8-4a46-b5ec-fd6e9d9b9d84 | Status: 200 | Duration: 0.073s
INFO 2025-07-14 17:50:41,351 basehttp 35864 6108 "GET /api/core/info HTTP/1.1" 200 486
INFO 2025-07-14 17:50:41,354 logging 35864 25612 Request started - ID: 87de0822-a37f-4793-9585-a4371917a3dc | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:41,371 logging 35864 25612 Request completed - ID: 87de0822-a37f-4793-9585-a4371917a3dc | Status: 200 | Duration: 0.017s
INFO 2025-07-14 17:50:41,372 basehttp 35864 25612 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 17:50:41,376 logging 35864 31564 Request started - ID: 78f8b403-173b-4662-a734-c27ba795993c | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:42,393 logging 35864 31564 Request completed - ID: 78f8b403-173b-4662-a734-c27ba795993c | Status: 200 | Duration: 1.018s
INFO 2025-07-14 17:50:42,394 basehttp 35864 31564 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-14 17:50:42,397 logging 35864 21804 Request started - ID: ca62359a-0ab1-441e-bcde-8a77f648f286 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,418 logging 35864 21804 Request completed - ID: ca62359a-0ab1-441e-bcde-8a77f648f286 | Status: 200 | Duration: 1.020s
INFO 2025-07-14 17:50:43,419 basehttp 35864 21804 "POST /api/auth/login HTTP/1.1" 200 717
INFO 2025-07-14 17:50:43,423 logging 35864 16760 Request started - ID: 04d44d83-553a-4c1a-afe2-35ecd966daa5 | Method: GET | Path: /api/auth/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,458 logging 35864 16760 Request completed - ID: 04d44d83-553a-4c1a-afe2-35ecd966daa5 | Status: 200 | Duration: 0.035s
INFO 2025-07-14 17:50:43,459 basehttp 35864 16760 "GET /api/auth/me HTTP/1.1" 200 216
INFO 2025-07-14 17:50:43,463 logging 35864 40252 Request started - ID: e1a37539-c579-4404-a655-44abd0ad0ab0 | Method: GET | Path: /api/auth/verify-token | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,499 logging 35864 40252 Request completed - ID: e1a37539-c579-4404-a655-44abd0ad0ab0 | Status: 200 | Duration: 0.036s
INFO 2025-07-14 17:50:43,501 basehttp 35864 40252 "GET /api/auth/verify-token?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUyNDg3NTQzLCJpYXQiOjE3NTI0ODY2NDMsImp0aSI6ImYxNjY4NmRjYjk2OTQyZDI4NmE2MmQ1ZmNlNjczZmQwIiwidXNlcl9pZCI6M30.8ZEibUS8z2lQqtfkuh4oOln9Nm9FNtxftyTuCNkvhGg HTTP/1.1" 200 67
INFO 2025-07-14 17:50:43,504 logging 35864 7992 Request started - ID: 9db2d7aa-82c0-47db-a210-42a4aa0b5f96 | Method: POST | Path: /api/auth/refresh | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,532 logging 35864 7992 Request completed - ID: 9db2d7aa-82c0-47db-a210-42a4aa0b5f96 | Status: 200 | Duration: 0.028s
INFO 2025-07-14 17:50:43,533 basehttp 35864 7992 "POST /api/auth/refresh HTTP/1.1" 200 291
INFO 2025-07-14 17:50:43,538 logging 35864 18792 Request started - ID: 3e8cc8d2-7e1d-4e61-b3c0-774306861408 | Method: GET | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,559 logging 35864 18792 Request completed - ID: 3e8cc8d2-7e1d-4e61-b3c0-774306861408 | Status: 200 | Duration: 0.021s
INFO 2025-07-14 17:50:43,559 basehttp 35864 18792 "GET /api/users/me HTTP/1.1" 200 331
INFO 2025-07-14 17:50:43,563 logging 35864 33332 Request started - ID: 2c4859d1-9a0a-4913-ba97-1dfc1e894577 | Method: PUT | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,591 logging 35864 33332 Request completed - ID: 2c4859d1-9a0a-4913-ba97-1dfc1e894577 | Status: 200 | Duration: 0.028s
INFO 2025-07-14 17:50:43,592 basehttp 35864 33332 "PUT /api/users/me HTTP/1.1" 200 357
INFO 2025-07-14 17:50:43,596 logging 35864 17340 Request started - ID: 3174a19a-9e41-4d67-9fd8-167d195a3be3 | Method: GET | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,633 logging 35864 17340 Request completed - ID: 3174a19a-9e41-4d67-9fd8-167d195a3be3 | Status: 200 | Duration: 0.037s
INFO 2025-07-14 17:50:43,634 basehttp 35864 17340 "GET /api/users/me/profile HTTP/1.1" 200 604
INFO 2025-07-14 17:50:43,637 logging 35864 25540 Request started - ID: 1f8cb67b-d214-4140-a44b-4b481adb9c49 | Method: PUT | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,664 logging 35864 25540 Request completed - ID: 1f8cb67b-d214-4140-a44b-4b481adb9c49 | Status: 200 | Duration: 0.027s
INFO 2025-07-14 17:50:43,665 basehttp 35864 25540 "PUT /api/users/me/profile HTTP/1.1" 200 639
INFO 2025-07-14 17:50:43,669 logging 35864 7380 Request started - ID: 7d72a215-059c-437d-8a50-803ef1efbc20 | Method: GET | Path: /api/users/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,701 logging 35864 7380 Request completed - ID: 7d72a215-059c-437d-8a50-803ef1efbc20 | Status: 200 | Duration: 0.033s
INFO 2025-07-14 17:50:43,703 basehttp 35864 7380 "GET /api/users/?page=1&per_page=10 HTTP/1.1" 200 1050
INFO 2025-07-14 17:50:43,707 logging 35864 16772 Request started - ID: 40f72788-03ab-4942-9ba6-d10a2c578ccb | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,744 logging 35864 16772 Request completed - ID: 40f72788-03ab-4942-9ba6-d10a2c578ccb | Status: 403 | Duration: 0.037s
WARNING 2025-07-14 17:50:43,747 log 35864 16772 Forbidden: /api/permissions/permissions
WARNING 2025-07-14 17:50:43,747 basehttp 35864 16772 "GET /api/permissions/permissions HTTP/1.1" 403 49
INFO 2025-07-14 17:50:43,752 logging 35864 39396 Request started - ID: 9657cac3-84a0-490c-8b8f-e8ff3bfe79a8 | Method: GET | Path: /api/permissions/roles | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,783 logging 35864 39396 Request completed - ID: 9657cac3-84a0-490c-8b8f-e8ff3bfe79a8 | Status: 403 | Duration: 0.032s
WARNING 2025-07-14 17:50:43,784 log 35864 39396 Forbidden: /api/permissions/roles
WARNING 2025-07-14 17:50:43,785 basehttp 35864 39396 "GET /api/permissions/roles HTTP/1.1" 403 43
INFO 2025-07-14 17:50:43,789 logging 35864 36356 Request started - ID: 1011f2d3-2806-4ca8-9788-f31a3f4d77d9 | Method: GET | Path: /api/permissions/my-permissions | User:  | IP: 127.0.0.1
ERROR 2025-07-14 17:50:43,798 errors 35864 36356 type object 'PermissionService' has no attribute 'get_user_roles'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\permissions\api.py", line 38, in get_my_permissions
    roles = PermissionService.get_user_roles(user)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'PermissionService' has no attribute 'get_user_roles'
INFO 2025-07-14 17:50:43,822 logging 35864 36356 Request completed - ID: 1011f2d3-2806-4ca8-9788-f31a3f4d77d9 | Status: 500 | Duration: 0.033s
ERROR 2025-07-14 17:50:43,822 log 35864 36356 Internal Server Error: /api/permissions/my-permissions
ERROR 2025-07-14 17:50:43,823 basehttp 35864 36356 "GET /api/permissions/my-permissions HTTP/1.1" 500 524
INFO 2025-07-14 17:50:43,827 logging 35864 12168 Request started - ID: 3548313f-6568-4f69-a8f2-e4e22aea8f2e | Method: GET | Path: /api/storage/files/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,875 logging 35864 12168 Request completed - ID: 3548313f-6568-4f69-a8f2-e4e22aea8f2e | Status: 404 | Duration: 0.048s
WARNING 2025-07-14 17:50:43,876 log 35864 12168 Not Found: /api/storage/files/
WARNING 2025-07-14 17:50:43,877 basehttp 35864 12168 "GET /api/storage/files/?page=1&per_page=10 HTTP/1.1" 404 38881
INFO 2025-07-14 17:50:43,880 logging 35864 29884 Request started - ID: faf65511-2121-4351-868a-7109fe8ed339 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,916 logging 35864 29884 Request completed - ID: faf65511-2121-4351-868a-7109fe8ed339 | Status: 403 | Duration: 0.037s
WARNING 2025-07-14 17:50:43,917 log 35864 29884 Forbidden: /api/storage/stats
WARNING 2025-07-14 17:50:43,918 basehttp 35864 29884 "GET /api/storage/stats HTTP/1.1" 403 51
INFO 2025-07-14 17:50:43,922 logging 35864 20892 Request started - ID: 48cff52e-bf7a-44d6-b54c-c76879d0a652 | Method: GET | Path: /api/storage/categories | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:43,957 logging 35864 20892 Request completed - ID: 48cff52e-bf7a-44d6-b54c-c76879d0a652 | Status: 200 | Duration: 0.035s
INFO 2025-07-14 17:50:43,958 basehttp 35864 20892 "GET /api/storage/categories HTTP/1.1" 200 173
INFO 2025-07-14 17:50:43,963 logging 35864 40184 Request started - ID: 0b2d74e4-71e8-4886-8891-ccf4a20e351a | Method: GET | Path: /api/i18n/languages | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:44,002 logging 35864 40184 Request completed - ID: 0b2d74e4-71e8-4886-8891-ccf4a20e351a | Status: 200 | Duration: 0.039s
INFO 2025-07-14 17:50:44,004 basehttp 35864 40184 "GET /api/i18n/languages HTTP/1.1" 200 246
INFO 2025-07-14 17:50:44,008 logging 35864 23040 Request started - ID: 501a5391-2397-4d85-9907-ed920a9df0e3 | Method: GET | Path: /api/i18n/translations/en | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:44,045 logging 35864 23040 Request completed - ID: 501a5391-2397-4d85-9907-ed920a9df0e3 | Status: 200 | Duration: 0.037s
INFO 2025-07-14 17:50:44,046 basehttp 35864 23040 "GET /api/i18n/translations/en HTTP/1.1" 200 2
INFO 2025-07-14 17:50:44,051 logging 35864 36132 Request started - ID: be06fe9e-4ca6-47fd-a681-ea3d631482de | Method: GET | Path: /api/i18n/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:44,069 logging 35864 36132 Request completed - ID: be06fe9e-4ca6-47fd-a681-ea3d631482de | Status: 401 | Duration: 0.019s
WARNING 2025-07-14 17:50:44,070 log 35864 36132 Unauthorized: /api/i18n/stats
WARNING 2025-07-14 17:50:44,071 basehttp 35864 36132 "GET /api/i18n/stats HTTP/1.1" 401 37
INFO 2025-07-14 17:50:56,705 logging 35864 11688 Request started - ID: 89c5174c-4c5f-4f25-b408-998c15943e0f | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:56,728 logging 35864 11688 Request completed - ID: 89c5174c-4c5f-4f25-b408-998c15943e0f | Status: 200 | Duration: 0.022s
INFO 2025-07-14 17:50:56,729 basehttp 35864 11688 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 17:50:58,241 logging 35864 11688 Request started - ID: e5b8d6d3-2547-4e02-8409-767c6850c927 | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:58,274 logging 35864 11688 Request completed - ID: e5b8d6d3-2547-4e02-8409-767c6850c927 | Status: 200 | Duration: 0.034s
INFO 2025-07-14 17:50:58,276 basehttp 35864 11688 "GET /api/model-designer/projects HTTP/1.1" 200 575
INFO 2025-07-14 17:50:58,295 logging 35864 11688 Request started - ID: e19d7159-21be-4499-a49d-a8546d8b24af | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:58,317 logging 35864 11688 Request completed - ID: e19d7159-21be-4499-a49d-a8546d8b24af | Status: 200 | Duration: 0.022s
INFO 2025-07-14 17:50:58,318 basehttp 35864 11688 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 17:50:58,726 logging 35864 11688 Request started - ID: b0533a6a-cb83-4839-ac83-f30de55d0160 | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 17:50:58,732 errors 35864 11688 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 17:50:58,755 logging 35864 11688 Request completed - ID: b0533a6a-cb83-4839-ac83-f30de55d0160 | Status: 500 | Duration: 0.029s
ERROR 2025-07-14 17:50:58,755 log 35864 11688 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 17:50:58,756 basehttp 35864 11688 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 17:50:58,933 logging 35864 11688 Request started - ID: 2972d477-85ef-47fe-9c5b-236083231bba | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:50:58,955 logging 35864 11688 Request completed - ID: 2972d477-85ef-47fe-9c5b-236083231bba | Status: 200 | Duration: 0.022s
INFO 2025-07-14 17:50:58,956 basehttp 35864 11688 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 17:51:06,015 logging 35864 11688 Request started - ID: f5353034-614d-4acb-ad20-4163d6d81d71 | Method: POST | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:51:06,130 logging 35864 11688 Request completed - ID: f5353034-614d-4acb-ad20-4163d6d81d71 | Status: 200 | Duration: 0.115s
INFO 2025-07-14 17:51:06,132 basehttp 35864 11688 "POST /api/model-designer/projects HTTP/1.1" 200 237
INFO 2025-07-14 17:51:07,481 logging 35864 11688 Request started - ID: 19bc86e5-7efa-442e-8c19-564e0af40cd2 | Method: GET | Path: /api/model-designer/projects/3/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:51:07,540 logging 35864 11688 Request completed - ID: 19bc86e5-7efa-442e-8c19-564e0af40cd2 | Status: 200 | Duration: 0.059s
INFO 2025-07-14 17:51:07,540 basehttp 35864 11688 "GET /api/model-designer/projects/3/models HTTP/1.1" 200 2
INFO 2025-07-14 17:51:09,010 logging 35864 11688 Request started - ID: aee380e6-eb6e-43d6-9111-f7e04d458a13 | Method: GET | Path: /api/model-designer/projects/3/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:51:09,112 logging 35864 11688 Request completed - ID: aee380e6-eb6e-43d6-9111-f7e04d458a13 | Status: 200 | Duration: 0.103s
INFO 2025-07-14 17:51:09,117 basehttp 35864 11688 "GET /api/model-designer/projects/3/models HTTP/1.1" 200 2
INFO 2025-07-14 17:51:15,003 logging 35864 11688 Request started - ID: 8a94eef6-b2a3-4455-8b3a-2a290a4533d7 | Method: POST | Path: /api/model-designer/projects/3/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:51:15,039 logging 35864 11688 Request completed - ID: 8a94eef6-b2a3-4455-8b3a-2a290a4533d7 | Status: 200 | Duration: 0.037s
INFO 2025-07-14 17:51:15,041 basehttp 35864 11688 "POST /api/model-designer/projects/3/models HTTP/1.1" 200 332
INFO 2025-07-14 17:51:29,274 logging 35864 11688 Request started - ID: 151e5769-2be3-49c4-ba7b-24b2694f732d | Method: POST | Path: /api/model-designer/models/4/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:51:29,353 logging 35864 11688 Request completed - ID: 151e5769-2be3-49c4-ba7b-24b2694f732d | Status: 200 | Duration: 0.079s
INFO 2025-07-14 17:51:29,354 basehttp 35864 11688 "POST /api/model-designer/models/4/fields HTTP/1.1" 200 491
INFO 2025-07-14 17:51:40,955 logging 35864 11688 Request started - ID: 5b47f674-db72-4ecd-92da-adfc96389919 | Method: POST | Path: /api/model-designer/models/4/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:51:41,061 logging 35864 11688 Request completed - ID: 5b47f674-db72-4ecd-92da-adfc96389919 | Status: 200 | Duration: 0.106s
INFO 2025-07-14 17:51:41,062 basehttp 35864 11688 "POST /api/model-designer/models/4/fields HTTP/1.1" 200 491
INFO 2025-07-14 17:51:50,367 logging 35864 11688 Request started - ID: 04a59ac6-bbdf-4874-b0e7-96c4cac10f62 | Method: POST | Path: /api/model-designer/projects/3/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:51:50,467 logging 35864 11688 Request completed - ID: 04a59ac6-bbdf-4874-b0e7-96c4cac10f62 | Status: 200 | Duration: 0.100s
INFO 2025-07-14 17:51:50,469 basehttp 35864 11688 "POST /api/model-designer/projects/3/models HTTP/1.1" 200 329
INFO 2025-07-14 17:52:01,943 logging 35864 11688 Request started - ID: 1b06364e-2b8f-4d23-ab08-838f82a235fc | Method: POST | Path: /api/model-designer/models/5/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:52:02,042 logging 35864 11688 Request completed - ID: 1b06364e-2b8f-4d23-ab08-838f82a235fc | Status: 200 | Duration: 0.099s
INFO 2025-07-14 17:52:02,044 basehttp 35864 11688 "POST /api/model-designer/models/5/fields HTTP/1.1" 200 497
INFO 2025-07-14 17:52:18,488 logging 35864 11688 Request started - ID: 35cf4d43-a338-4847-bb95-983b468f69c9 | Method: POST | Path: /api/model-designer/projects/3/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:52:18,732 logging 35864 11688 Request completed - ID: 35cf4d43-a338-4847-bb95-983b468f69c9 | Status: 200 | Duration: 0.244s
INFO 2025-07-14 17:52:18,733 basehttp 35864 11688 "POST /api/model-designer/projects/3/generate HTTP/1.1" 200 1118
INFO 2025-07-14 17:52:21,328 logging 35864 11688 Request started - ID: acc5602e-daf2-46c6-8cae-f200714d8102 | Method: GET | Path: /api/model-designer/projects/3/export-yaml | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:52:21,441 logging 35864 11688 Request completed - ID: acc5602e-daf2-46c6-8cae-f200714d8102 | Status: 200 | Duration: 0.115s
INFO 2025-07-14 17:52:21,444 basehttp 35864 11688 "GET /api/model-designer/projects/3/export-yaml HTTP/1.1" 200 688
INFO 2025-07-14 17:52:38,243 logging 35864 11688 Request started - ID: 7463afcd-a0fe-4d90-b3d1-8954ff71d67b | Method: POST | Path: /api/model-designer/projects/3/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 17:52:38,511 logging 35864 11688 Request completed - ID: 7463afcd-a0fe-4d90-b3d1-8954ff71d67b | Status: 200 | Duration: 0.268s
INFO 2025-07-14 17:52:38,511 basehttp 35864 11688 "POST /api/model-designer/projects/3/generate HTTP/1.1" 200 1118
INFO 2025-07-14 21:24:33,943 autoreload 18992 17764 Watching for file changes with StatReloader
INFO 2025-07-14 21:24:50,014 logging 18992 21084 Request started - ID: e2024f5d-43d9-4dbd-b878-7de27c2a9752 | Method: GET | Path: /api/ | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:24:50,016 logging 18992 21084 Request failed - ID: e2024f5d-43d9-4dbd-b878-7de27c2a9752 | Exception: Http404: docs_url = /api/docs/
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\openapi\views.py", line 16, in default_home
    raise Http404(f"docs_url = {docs_url}")
django.http.response.Http404: docs_url = /api/docs/
INFO 2025-07-14 21:24:50,085 logging 18992 21084 Request completed - ID: e2024f5d-43d9-4dbd-b878-7de27c2a9752 | Status: 404 | Duration: 0.071s
WARNING 2025-07-14 21:24:50,088 log 18992 21084 Not Found: /api/
WARNING 2025-07-14 21:24:50,089 basehttp 18992 21084 "GET /api/ HTTP/1.1" 404 38278
INFO 2025-07-14 21:24:52,242 logging 18992 21084 Request started - ID: bf712fa0-4411-4dd2-94d4-b76bd96b3e4e | Method: GET | Path: /favicon.ico | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:24:52,354 logging 18992 21084 Request completed - ID: bf712fa0-4411-4dd2-94d4-b76bd96b3e4e | Status: 404 | Duration: 0.112s
WARNING 2025-07-14 21:24:52,355 log 18992 21084 Not Found: /favicon.ico
WARNING 2025-07-14 21:24:52,356 basehttp 18992 21084 "GET /favicon.ico HTTP/1.1" 404 16277
INFO 2025-07-14 21:24:54,852 logging 18992 21084 Request started - ID: 508b46a5-d7b8-477e-9f47-3958f81733d7 | Method: GET | Path: / | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:24:54,902 logging 18992 21084 Request completed - ID: 508b46a5-d7b8-477e-9f47-3958f81733d7 | Status: 404 | Duration: 0.049s
WARNING 2025-07-14 21:24:54,902 log 18992 21084 Not Found: /
WARNING 2025-07-14 21:24:54,904 basehttp 18992 21084 "GET / HTTP/1.1" 404 16214
INFO 2025-07-14 21:25:02,404 logging 18992 21084 Request started - ID: 493792c8-7d80-4986-8878-cbcb3b718be7 | Method: GET | Path: /model-designer | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:02,447 logging 18992 21084 Request completed - ID: 493792c8-7d80-4986-8878-cbcb3b718be7 | Status: 404 | Duration: 0.044s
INFO 2025-07-14 21:25:02,448 basehttp 18992 21084 "GET /model-designer HTTP/1.1" 301 0
INFO 2025-07-14 21:25:02,459 logging 18992 19068 Request started - ID: 6107df22-892e-49ef-af46-37429aed5c64 | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:02,483 logging 18992 19068 Request completed - ID: 6107df22-892e-49ef-af46-37429aed5c64 | Status: 200 | Duration: 0.024s
INFO 2025-07-14 21:25:02,484 basehttp 18992 19068 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 21:25:05,880 logging 18992 19068 Request started - ID: 4d0b8060-a80b-49f6-9b1f-cef8e2eb4f2a | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:06,228 logging 18992 19068 Request completed - ID: 4d0b8060-a80b-49f6-9b1f-cef8e2eb4f2a | Status: 200 | Duration: 0.348s
INFO 2025-07-14 21:25:06,230 basehttp 18992 19068 "GET /api/model-designer/projects HTTP/1.1" 200 785
INFO 2025-07-14 21:25:06,461 logging 18992 19068 Request started - ID: 000b8fb8-c318-4eff-a917-5e0507097d00 | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:06,489 logging 18992 19068 Request completed - ID: 000b8fb8-c318-4eff-a917-5e0507097d00 | Status: 200 | Duration: 0.028s
INFO 2025-07-14 21:25:06,491 basehttp 18992 19068 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:25:06,598 logging 18992 19068 Request started - ID: 8eed865d-4940-41e3-9105-175c10a30fde | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:25:06,609 errors 18992 19068 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:25:06,662 logging 18992 19068 Request completed - ID: 8eed865d-4940-41e3-9105-175c10a30fde | Status: 500 | Duration: 0.065s
ERROR 2025-07-14 21:25:06,663 log 18992 19068 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:25:06,666 basehttp 18992 19068 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:25:06,734 logging 18992 19068 Request started - ID: 80def36f-dbf3-498e-8613-ff6e7eeaaf00 | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:06,767 logging 18992 19068 Request completed - ID: 80def36f-dbf3-498e-8613-ff6e7eeaaf00 | Status: 200 | Duration: 0.034s
INFO 2025-07-14 21:25:06,768 basehttp 18992 19068 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:25:07,653 logging 18992 19068 Request started - ID: 4742064e-1f4e-4a20-b110-f4fbe9859f26 | Method: GET | Path: /api/model-designer/projects/3/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:07,708 logging 18992 19068 Request completed - ID: 4742064e-1f4e-4a20-b110-f4fbe9859f26 | Status: 200 | Duration: 0.055s
INFO 2025-07-14 21:25:07,709 basehttp 18992 19068 "GET /api/model-designer/projects/3/models HTTP/1.1" 200 665
INFO 2025-07-14 21:25:07,718 logging 18992 19068 Request started - ID: f61b02e7-5113-492c-8f2c-b0d2f466cceb | Method: GET | Path: /api/model-designer/models/4/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:07,765 logging 18992 19068 Request completed - ID: f61b02e7-5113-492c-8f2c-b0d2f466cceb | Status: 200 | Duration: 0.048s
INFO 2025-07-14 21:25:07,766 basehttp 18992 19068 "GET /api/model-designer/models/4/fields HTTP/1.1" 200 986
INFO 2025-07-14 21:25:07,773 logging 18992 19068 Request started - ID: 9c7698e6-28d8-45c7-94f4-446d6b16c4b6 | Method: GET | Path: /api/model-designer/models/5/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:07,836 logging 18992 19068 Request completed - ID: 9c7698e6-28d8-45c7-94f4-446d6b16c4b6 | Status: 200 | Duration: 0.063s
INFO 2025-07-14 21:25:07,837 basehttp 18992 19068 "GET /api/model-designer/models/5/fields HTTP/1.1" 200 499
INFO 2025-07-14 21:25:10,097 logging 18992 19068 Request started - ID: b1173cb6-3112-4fcb-974d-4da38be4cb49 | Method: POST | Path: /api/model-designer/projects/3/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:25:10,340 logging 18992 19068 Request completed - ID: b1173cb6-3112-4fcb-974d-4da38be4cb49 | Status: 200 | Duration: 0.244s
INFO 2025-07-14 21:25:10,341 basehttp 18992 19068 "POST /api/model-designer/projects/3/generate HTTP/1.1" 200 1119
INFO 2025-07-14 21:28:23,989 logging 18992 22132 Request started - ID: cb5d5376-af36-474a-ad1d-cf8ca622bd4e | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:24,018 logging 18992 22132 Request completed - ID: cb5d5376-af36-474a-ad1d-cf8ca622bd4e | Status: 200 | Duration: 0.030s
INFO 2025-07-14 21:28:24,020 basehttp 18992 22132 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 21:28:24,026 logging 18992 32112 Request started - ID: c4626474-196d-49a8-8ab1-2b3d63889d72 | Method: GET | Path: /api/core/health | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:24,049 logging 18992 32112 Request completed - ID: c4626474-196d-49a8-8ab1-2b3d63889d72 | Status: 200 | Duration: 0.024s
INFO 2025-07-14 21:28:24,050 basehttp 18992 32112 "GET /api/core/health HTTP/1.1" 200 138
INFO 2025-07-14 21:28:24,072 logging 18992 32064 Request started - ID: 49588c84-2154-48e4-91b9-54c298369b97 | Method: GET | Path: /api/core/info | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:24,173 logging 18992 32064 Request completed - ID: 49588c84-2154-48e4-91b9-54c298369b97 | Status: 200 | Duration: 0.101s
INFO 2025-07-14 21:28:24,174 basehttp 18992 32064 "GET /api/core/info HTTP/1.1" 200 486
INFO 2025-07-14 21:28:24,178 logging 18992 26016 Request started - ID: b204b6d1-5e9e-4ad6-b7b7-a98b9731ead4 | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:24,192 logging 18992 26016 Request completed - ID: b204b6d1-5e9e-4ad6-b7b7-a98b9731ead4 | Status: 200 | Duration: 0.014s
INFO 2025-07-14 21:28:24,194 basehttp 18992 26016 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-14 21:28:24,198 logging 18992 1940 Request started - ID: 17bc2ae5-c855-4e87-ad47-4b236dd4fa27 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:25,304 logging 18992 1940 Request completed - ID: 17bc2ae5-c855-4e87-ad47-4b236dd4fa27 | Status: 200 | Duration: 1.106s
INFO 2025-07-14 21:28:25,306 basehttp 18992 1940 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-14 21:28:25,325 logging 18992 21136 Request started - ID: 368bfa70-ee57-4a36-882f-6131ebfdaf2e | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,489 logging 18992 21136 Request completed - ID: 368bfa70-ee57-4a36-882f-6131ebfdaf2e | Status: 200 | Duration: 1.163s
INFO 2025-07-14 21:28:26,490 basehttp 18992 21136 "POST /api/auth/login HTTP/1.1" 200 717
INFO 2025-07-14 21:28:26,515 logging 18992 20768 Request started - ID: 6d9c2487-6411-437f-afcf-34f326b482e4 | Method: GET | Path: /api/auth/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,540 logging 18992 20768 Request completed - ID: 6d9c2487-6411-437f-afcf-34f326b482e4 | Status: 200 | Duration: 0.026s
INFO 2025-07-14 21:28:26,541 basehttp 18992 20768 "GET /api/auth/me HTTP/1.1" 200 216
INFO 2025-07-14 21:28:26,562 logging 18992 18312 Request started - ID: d9822c90-7139-4228-98f4-46105610c0f8 | Method: GET | Path: /api/auth/verify-token | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,590 logging 18992 18312 Request completed - ID: d9822c90-7139-4228-98f4-46105610c0f8 | Status: 200 | Duration: 0.028s
INFO 2025-07-14 21:28:26,591 basehttp 18992 18312 "GET /api/auth/verify-token?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUyNTAwNjA2LCJpYXQiOjE3NTI0OTk3MDYsImp0aSI6IjE1YzUzNmJlNzJkNDQ1MTY4N2UxMmFkYmE2ZTE2ZTBhIiwidXNlcl9pZCI6NX0._wEvfDydjZ4PmOfQxAZEVkIvvZRZEFZ04chprpLZYSU HTTP/1.1" 200 67
INFO 2025-07-14 21:28:26,610 logging 18992 22612 Request started - ID: 9cc3ae43-475f-4dca-82ac-491817b669a1 | Method: GET | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,640 logging 18992 22612 Request completed - ID: 9cc3ae43-475f-4dca-82ac-491817b669a1 | Status: 200 | Duration: 0.031s
INFO 2025-07-14 21:28:26,642 basehttp 18992 22612 "GET /api/users/me HTTP/1.1" 200 331
INFO 2025-07-14 21:28:26,657 logging 18992 22048 Request started - ID: 7a8e48cf-f0fa-4b94-b26b-53f32fe38210 | Method: PUT | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,692 logging 18992 22048 Request completed - ID: 7a8e48cf-f0fa-4b94-b26b-53f32fe38210 | Status: 200 | Duration: 0.033s
INFO 2025-07-14 21:28:26,693 basehttp 18992 22048 "PUT /api/users/me HTTP/1.1" 200 357
INFO 2025-07-14 21:28:26,697 logging 18992 11840 Request started - ID: 062ed9c0-88a2-4bf7-a52d-5ce6e69c8611 | Method: GET | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,754 logging 18992 11840 Request completed - ID: 062ed9c0-88a2-4bf7-a52d-5ce6e69c8611 | Status: 200 | Duration: 0.057s
INFO 2025-07-14 21:28:26,756 basehttp 18992 11840 "GET /api/users/me/profile HTTP/1.1" 200 604
INFO 2025-07-14 21:28:26,761 logging 18992 40616 Request started - ID: 1ac826be-9ba6-4246-a284-46e482c8379c | Method: PUT | Path: /api/users/me/profile | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,804 logging 18992 40616 Request completed - ID: 1ac826be-9ba6-4246-a284-46e482c8379c | Status: 200 | Duration: 0.043s
INFO 2025-07-14 21:28:26,807 basehttp 18992 40616 "PUT /api/users/me/profile HTTP/1.1" 200 639
INFO 2025-07-14 21:28:26,810 logging 18992 38864 Request started - ID: 86bd4a5d-5041-419a-8020-541476107d88 | Method: GET | Path: /api/users/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,852 logging 18992 38864 Request completed - ID: 86bd4a5d-5041-419a-8020-541476107d88 | Status: 200 | Duration: 0.042s
INFO 2025-07-14 21:28:26,853 basehttp 18992 38864 "GET /api/users/?page=1&per_page=10 HTTP/1.1" 200 1375
INFO 2025-07-14 21:28:26,856 logging 18992 6936 Request started - ID: 5d873682-5620-4cba-aaf8-454384ec9a0a | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,898 logging 18992 6936 Request completed - ID: 5d873682-5620-4cba-aaf8-454384ec9a0a | Status: 403 | Duration: 0.042s
WARNING 2025-07-14 21:28:26,899 log 18992 6936 Forbidden: /api/permissions/permissions
WARNING 2025-07-14 21:28:26,900 basehttp 18992 6936 "GET /api/permissions/permissions HTTP/1.1" 403 49
INFO 2025-07-14 21:28:26,904 logging 18992 10448 Request started - ID: a2503b13-41c3-42ab-9bda-e977eac9bf91 | Method: GET | Path: /api/permissions/roles | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:26,936 logging 18992 10448 Request completed - ID: a2503b13-41c3-42ab-9bda-e977eac9bf91 | Status: 403 | Duration: 0.032s
WARNING 2025-07-14 21:28:26,937 log 18992 10448 Forbidden: /api/permissions/roles
WARNING 2025-07-14 21:28:26,938 basehttp 18992 10448 "GET /api/permissions/roles HTTP/1.1" 403 43
INFO 2025-07-14 21:28:26,941 logging 18992 12020 Request started - ID: 5a413d54-1cc0-4771-b119-aa2770a60c4c | Method: GET | Path: /api/permissions/my-permissions | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:28:26,950 errors 18992 12020 type object 'PermissionService' has no attribute 'get_user_roles'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\permissions\api.py", line 38, in get_my_permissions
    roles = PermissionService.get_user_roles(user)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'PermissionService' has no attribute 'get_user_roles'
INFO 2025-07-14 21:28:26,973 logging 18992 12020 Request completed - ID: 5a413d54-1cc0-4771-b119-aa2770a60c4c | Status: 500 | Duration: 0.032s
ERROR 2025-07-14 21:28:26,974 log 18992 12020 Internal Server Error: /api/permissions/my-permissions
ERROR 2025-07-14 21:28:26,974 basehttp 18992 12020 "GET /api/permissions/my-permissions HTTP/1.1" 500 524
INFO 2025-07-14 21:28:26,978 logging 18992 28348 Request started - ID: f4bb1781-9d43-4867-8d0a-34c99dc6f500 | Method: GET | Path: /api/storage/files/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:27,068 logging 18992 28348 Request completed - ID: f4bb1781-9d43-4867-8d0a-34c99dc6f500 | Status: 404 | Duration: 0.090s
WARNING 2025-07-14 21:28:27,068 log 18992 28348 Not Found: /api/storage/files/
WARNING 2025-07-14 21:28:27,070 basehttp 18992 28348 "GET /api/storage/files/?page=1&per_page=10 HTTP/1.1" 404 38881
INFO 2025-07-14 21:28:27,077 logging 18992 14656 Request started - ID: 84247c4c-eb89-40d8-abf5-047e099cafdf | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:27,111 logging 18992 14656 Request completed - ID: 84247c4c-eb89-40d8-abf5-047e099cafdf | Status: 403 | Duration: 0.034s
WARNING 2025-07-14 21:28:27,111 log 18992 14656 Forbidden: /api/storage/stats
WARNING 2025-07-14 21:28:27,112 basehttp 18992 14656 "GET /api/storage/stats HTTP/1.1" 403 51
INFO 2025-07-14 21:28:27,116 logging 18992 10856 Request started - ID: 0def1489-6aa1-4fa2-8a95-5023da425fe4 | Method: GET | Path: /api/storage/categories | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:27,140 logging 18992 10856 Request completed - ID: 0def1489-6aa1-4fa2-8a95-5023da425fe4 | Status: 200 | Duration: 0.024s
INFO 2025-07-14 21:28:27,141 basehttp 18992 10856 "GET /api/storage/categories HTTP/1.1" 200 173
INFO 2025-07-14 21:28:27,170 logging 18992 4756 Request started - ID: 421fddd1-b3c0-4d43-8c4b-cbb422b5c6ad | Method: GET | Path: /api/i18n/languages | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:27,196 logging 18992 4756 Request completed - ID: 421fddd1-b3c0-4d43-8c4b-cbb422b5c6ad | Status: 200 | Duration: 0.026s
INFO 2025-07-14 21:28:27,197 basehttp 18992 4756 "GET /api/i18n/languages HTTP/1.1" 200 246
INFO 2025-07-14 21:28:27,217 logging 18992 14144 Request started - ID: 9d8e0b75-4eee-46a1-a935-b487460490a9 | Method: GET | Path: /api/i18n/translations/en | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:27,251 logging 18992 14144 Request completed - ID: 9d8e0b75-4eee-46a1-a935-b487460490a9 | Status: 200 | Duration: 0.034s
INFO 2025-07-14 21:28:27,252 basehttp 18992 14144 "GET /api/i18n/translations/en HTTP/1.1" 200 2
INFO 2025-07-14 21:28:27,281 logging 18992 9572 Request started - ID: e3600ce0-a374-4296-8500-079e6b53d568 | Method: GET | Path: /api/i18n/stats | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:27,296 logging 18992 9572 Request completed - ID: e3600ce0-a374-4296-8500-079e6b53d568 | Status: 401 | Duration: 0.016s
WARNING 2025-07-14 21:28:27,297 log 18992 9572 Unauthorized: /api/i18n/stats
WARNING 2025-07-14 21:28:27,298 basehttp 18992 9572 "GET /api/i18n/stats HTTP/1.1" 401 37
INFO 2025-07-14 21:28:27,308 logging 18992 21104 Request started - ID: e3c40145-64b4-4661-b57c-b67303b902e6 | Method: DELETE | Path: /api/auth/delete-account | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:27,364 logging 18992 21104 Request completed - ID: e3c40145-64b4-4661-b57c-b67303b902e6 | Status: 404 | Duration: 0.056s
WARNING 2025-07-14 21:28:27,364 log 18992 21104 Not Found: /api/auth/delete-account
WARNING 2025-07-14 21:28:27,365 basehttp 18992 21104 "DELETE /api/auth/delete-account HTTP/1.1" 404 38858
INFO 2025-07-14 21:28:37,859 logging 18992 19068 Request started - ID: 76e4b008-f2cc-4285-8af0-d0b8ae3b82d9 | Method: POST | Path: /api/model-designer/projects/3/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:28:38,100 logging 18992 19068 Request completed - ID: 76e4b008-f2cc-4285-8af0-d0b8ae3b82d9 | Status: 200 | Duration: 0.242s
INFO 2025-07-14 21:28:38,100 basehttp 18992 19068 "POST /api/model-designer/projects/3/generate HTTP/1.1" 200 1118
INFO 2025-07-14 21:30:22,339 autoreload 18992 17764 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:30:24,376 autoreload 25844 31764 Watching for file changes with StatReloader
INFO 2025-07-14 21:30:30,157 autoreload 25844 31764 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:30:31,913 autoreload 39732 26260 Watching for file changes with StatReloader
INFO 2025-07-14 21:31:20,747 logging 39732 26748 Request started - ID: 3600d47c-4ded-4b74-ab6b-ac8fcd1f4fa3 | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:20,853 logging 39732 26748 Request completed - ID: 3600d47c-4ded-4b74-ab6b-ac8fcd1f4fa3 | Status: 200 | Duration: 0.105s
INFO 2025-07-14 21:31:20,856 basehttp 39732 26748 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 21:31:22,066 logging 39732 26748 Request started - ID: b6ca739e-4d2c-4442-9014-6af9af86e84c | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:22,400 logging 39732 26748 Request completed - ID: b6ca739e-4d2c-4442-9014-6af9af86e84c | Status: 200 | Duration: 0.335s
INFO 2025-07-14 21:31:22,401 basehttp 39732 26748 "GET /api/model-designer/projects HTTP/1.1" 200 785
INFO 2025-07-14 21:31:22,617 logging 39732 26748 Request started - ID: 98f91bfe-6c65-4491-8c62-cd2ca5962053 | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:22,634 logging 39732 26748 Request completed - ID: 98f91bfe-6c65-4491-8c62-cd2ca5962053 | Status: 200 | Duration: 0.018s
INFO 2025-07-14 21:31:22,636 basehttp 39732 26748 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:31:22,717 logging 39732 26748 Request started - ID: 2763399b-cd83-4b56-bd2e-8c82abd7660b | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:31:22,740 errors 39732 26748 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:31:22,790 logging 39732 26748 Request completed - ID: 2763399b-cd83-4b56-bd2e-8c82abd7660b | Status: 500 | Duration: 0.073s
ERROR 2025-07-14 21:31:22,792 log 39732 26748 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:31:22,793 basehttp 39732 26748 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:31:22,828 logging 39732 26748 Request started - ID: ca7bceb9-22f8-413e-9d75-06f4576cf680 | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:22,858 logging 39732 26748 Request completed - ID: ca7bceb9-22f8-413e-9d75-06f4576cf680 | Status: 200 | Duration: 0.031s
INFO 2025-07-14 21:31:22,859 basehttp 39732 26748 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:31:23,386 logging 39732 26748 Request started - ID: 36c5ab06-0e29-4ad5-baa0-bbb0ee7ecad7 | Method: GET | Path: /api/model-designer/projects/3/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:23,490 logging 39732 26748 Request completed - ID: 36c5ab06-0e29-4ad5-baa0-bbb0ee7ecad7 | Status: 200 | Duration: 0.104s
INFO 2025-07-14 21:31:23,490 basehttp 39732 26748 "GET /api/model-designer/projects/3/models HTTP/1.1" 200 665
INFO 2025-07-14 21:31:23,501 logging 39732 26748 Request started - ID: 5d7598ed-2585-479d-8b16-c17aef0ea2a1 | Method: GET | Path: /api/model-designer/models/4/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:23,539 logging 39732 26748 Request completed - ID: 5d7598ed-2585-479d-8b16-c17aef0ea2a1 | Status: 200 | Duration: 0.039s
INFO 2025-07-14 21:31:23,540 basehttp 39732 26748 "GET /api/model-designer/models/4/fields HTTP/1.1" 200 986
INFO 2025-07-14 21:31:23,550 logging 39732 26748 Request started - ID: 573d9c3b-601a-4c3b-8250-8ea8cf2d8aa2 | Method: GET | Path: /api/model-designer/models/5/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:23,587 logging 39732 26748 Request completed - ID: 573d9c3b-601a-4c3b-8250-8ea8cf2d8aa2 | Status: 200 | Duration: 0.037s
INFO 2025-07-14 21:31:23,587 basehttp 39732 26748 "GET /api/model-designer/models/5/fields HTTP/1.1" 200 499
INFO 2025-07-14 21:31:24,341 logging 39732 26748 Request started - ID: 5c35eb2e-c7cf-48ab-a71e-c7bc322906c0 | Method: POST | Path: /api/model-designer/projects/3/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:24,422 logging 39732 26748 Request completed - ID: 5c35eb2e-c7cf-48ab-a71e-c7bc322906c0 | Status: 200 | Duration: 0.081s
INFO 2025-07-14 21:31:24,424 basehttp 39732 26748 "POST /api/model-designer/projects/3/generate HTTP/1.1" 200 504
INFO 2025-07-14 21:31:26,577 logging 39732 26748 Request started - ID: 2384ae7d-1a4e-4227-a86e-36d99ce18057 | Method: GET | Path: /api/model-designer/projects/2/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:26,679 logging 39732 26748 Request completed - ID: 2384ae7d-1a4e-4227-a86e-36d99ce18057 | Status: 200 | Duration: 0.102s
INFO 2025-07-14 21:31:26,680 basehttp 39732 26748 "GET /api/model-designer/projects/2/models HTTP/1.1" 200 387
INFO 2025-07-14 21:31:26,692 logging 39732 26748 Request started - ID: b1733a26-8223-4d45-bbaa-314b7301b5d0 | Method: GET | Path: /api/model-designer/models/3/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:26,733 logging 39732 26748 Request completed - ID: b1733a26-8223-4d45-bbaa-314b7301b5d0 | Status: 200 | Duration: 0.042s
INFO 2025-07-14 21:31:26,734 basehttp 39732 26748 "GET /api/model-designer/models/3/fields HTTP/1.1" 200 2549
INFO 2025-07-14 21:31:28,951 logging 39732 26748 Request started - ID: 9aa370a1-f62e-4d64-9c75-8c5c5a68309b | Method: POST | Path: /api/model-designer/projects/2/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:29,036 logging 39732 26748 Request completed - ID: 9aa370a1-f62e-4d64-9c75-8c5c5a68309b | Status: 200 | Duration: 0.085s
INFO 2025-07-14 21:31:29,038 basehttp 39732 26748 "POST /api/model-designer/projects/2/generate HTTP/1.1" 200 521
INFO 2025-07-14 21:31:31,683 logging 39732 26748 Request started - ID: bf45112b-1a83-4245-bbf6-c0cfa29d6726 | Method: GET | Path: /api/model-designer/projects/1/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:31,805 logging 39732 26748 Request completed - ID: bf45112b-1a83-4245-bbf6-c0cfa29d6726 | Status: 200 | Duration: 0.121s
INFO 2025-07-14 21:31:31,806 basehttp 39732 26748 "GET /api/model-designer/projects/1/models HTTP/1.1" 200 799
INFO 2025-07-14 21:31:31,818 logging 39732 26748 Request started - ID: 4754ca41-c927-42a0-9c45-203e23307eb8 | Method: GET | Path: /api/model-designer/models/1/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:31,865 logging 39732 26748 Request completed - ID: 4754ca41-c927-42a0-9c45-203e23307eb8 | Status: 200 | Duration: 0.048s
INFO 2025-07-14 21:31:31,866 basehttp 39732 26748 "GET /api/model-designer/models/1/fields HTTP/1.1" 200 2554
INFO 2025-07-14 21:31:31,879 logging 39732 26748 Request started - ID: 63f2fc58-d689-4a92-bf24-639c07dc9422 | Method: GET | Path: /api/model-designer/models/2/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:31,922 logging 39732 26748 Request completed - ID: 63f2fc58-d689-4a92-bf24-639c07dc9422 | Status: 200 | Duration: 0.043s
INFO 2025-07-14 21:31:31,923 basehttp 39732 26748 "GET /api/model-designer/models/2/fields HTTP/1.1" 200 1527
INFO 2025-07-14 21:31:32,551 logging 39732 26748 Request started - ID: 1eea37c4-d98d-4da3-893c-cf93a918fd01 | Method: POST | Path: /api/model-designer/projects/1/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:31:32,700 logging 39732 26748 Request completed - ID: 1eea37c4-d98d-4da3-893c-cf93a918fd01 | Status: 200 | Duration: 0.148s
INFO 2025-07-14 21:31:32,701 basehttp 39732 26748 "POST /api/model-designer/projects/1/generate HTTP/1.1" 200 506
INFO 2025-07-14 21:32:07,822 autoreload 39732 26260 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:32:09,942 autoreload 3400 31476 Watching for file changes with StatReloader
INFO 2025-07-14 21:32:33,209 autoreload 3400 31476 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:32:35,014 autoreload 9092 13340 Watching for file changes with StatReloader
INFO 2025-07-14 21:35:30,307 autoreload 9092 13340 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:35:32,175 autoreload 29064 33292 Watching for file changes with StatReloader
INFO 2025-07-14 21:35:42,566 autoreload 29064 33292 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:35:44,382 autoreload 38064 18000 Watching for file changes with StatReloader
INFO 2025-07-14 21:38:00,320 autoreload 5072 28008 Watching for file changes with StatReloader
INFO 2025-07-14 21:38:03,240 logging 5072 27280 Request started - ID: 8ab9ca6f-67a7-4ce4-ace1-f54a9a2a08c7 | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:03,367 logging 5072 27280 Request completed - ID: 8ab9ca6f-67a7-4ce4-ace1-f54a9a2a08c7 | Status: 200 | Duration: 0.127s
INFO 2025-07-14 21:38:03,368 basehttp 5072 27280 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 21:38:04,617 logging 5072 27280 Request started - ID: 8f3fdb43-89b6-4834-ae17-4505f2fcb9ac | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:04,829 logging 5072 27280 Request completed - ID: 8f3fdb43-89b6-4834-ae17-4505f2fcb9ac | Status: 200 | Duration: 0.212s
INFO 2025-07-14 21:38:04,830 basehttp 5072 27280 "GET /api/model-designer/projects HTTP/1.1" 200 785
INFO 2025-07-14 21:38:05,081 logging 5072 27280 Request started - ID: 1254f34b-f270-48ba-bd0b-850641538d29 | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:05,099 logging 5072 27280 Request completed - ID: 1254f34b-f270-48ba-bd0b-850641538d29 | Status: 200 | Duration: 0.019s
INFO 2025-07-14 21:38:05,100 basehttp 5072 27280 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:38:05,265 logging 5072 27280 Request started - ID: 6e9333a3-af31-43f6-8f01-64549b3ee1c5 | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:38:05,285 errors 5072 27280 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:38:05,326 logging 5072 27280 Request completed - ID: 6e9333a3-af31-43f6-8f01-64549b3ee1c5 | Status: 500 | Duration: 0.060s
ERROR 2025-07-14 21:38:05,361 log 5072 27280 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:38:05,366 basehttp 5072 27280 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:38:05,478 logging 5072 27280 Request started - ID: 5f9be81c-008c-42e3-8b81-4f693c99937f | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:05,508 logging 5072 27280 Request completed - ID: 5f9be81c-008c-42e3-8b81-4f693c99937f | Status: 200 | Duration: 0.030s
INFO 2025-07-14 21:38:05,509 basehttp 5072 27280 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:38:05,924 logging 5072 27280 Request started - ID: 39c797a7-911d-4d96-9a36-e5ba43b6113b | Method: GET | Path: /api/model-designer/projects/1/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:05,992 logging 5072 27280 Request completed - ID: 39c797a7-911d-4d96-9a36-e5ba43b6113b | Status: 200 | Duration: 0.067s
INFO 2025-07-14 21:38:05,993 basehttp 5072 27280 "GET /api/model-designer/projects/1/models HTTP/1.1" 200 799
INFO 2025-07-14 21:38:06,005 logging 5072 27280 Request started - ID: a22b2e36-4c86-455e-bc40-4178782919b7 | Method: GET | Path: /api/model-designer/models/1/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:06,051 logging 5072 27280 Request completed - ID: a22b2e36-4c86-455e-bc40-4178782919b7 | Status: 200 | Duration: 0.046s
INFO 2025-07-14 21:38:06,053 basehttp 5072 27280 "GET /api/model-designer/models/1/fields HTTP/1.1" 200 2554
INFO 2025-07-14 21:38:06,061 logging 5072 27280 Request started - ID: c021a5d5-ab2d-405a-8a1b-993306b47067 | Method: GET | Path: /api/model-designer/models/2/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:06,102 logging 5072 27280 Request completed - ID: c021a5d5-ab2d-405a-8a1b-993306b47067 | Status: 200 | Duration: 0.042s
INFO 2025-07-14 21:38:06,103 basehttp 5072 27280 "GET /api/model-designer/models/2/fields HTTP/1.1" 200 1527
INFO 2025-07-14 21:38:07,336 logging 5072 27280 Request started - ID: 0ac22cd6-005f-4ec2-8c9e-31ae8d10dd2d | Method: POST | Path: /api/model-designer/projects/1/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:38:07,540 logging 5072 27280 Request completed - ID: 0ac22cd6-005f-4ec2-8c9e-31ae8d10dd2d | Status: 200 | Duration: 0.204s
INFO 2025-07-14 21:38:07,541 basehttp 5072 27280 "POST /api/model-designer/projects/1/generate HTTP/1.1" 200 530
INFO 2025-07-14 21:41:38,124 autoreload 5072 28008 F:\PycharmProjects\django_ninja_template\apps\core\management\commands\generate_from_config.py changed, reloading.
INFO 2025-07-14 21:41:40,011 autoreload 5044 23508 Watching for file changes with StatReloader
INFO 2025-07-14 21:42:45,981 autoreload 5044 23508 F:\PycharmProjects\django_ninja_template\apps\model_designer\schemas.py changed, reloading.
INFO 2025-07-14 21:42:47,914 autoreload 33264 6668 Watching for file changes with StatReloader
INFO 2025-07-14 21:43:38,816 logging 33264 19400 Request started - ID: d813dd82-1f18-4e00-9d29-7769006fe9c2 | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:38,925 logging 33264 19400 Request completed - ID: d813dd82-1f18-4e00-9d29-7769006fe9c2 | Status: 200 | Duration: 0.109s
INFO 2025-07-14 21:43:38,927 basehttp 33264 19400 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 21:43:40,191 logging 33264 19400 Request started - ID: edfb5208-95a9-4b2d-9940-7fceee328b88 | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:40,422 logging 33264 19400 Request completed - ID: edfb5208-95a9-4b2d-9940-7fceee328b88 | Status: 200 | Duration: 0.232s
INFO 2025-07-14 21:43:40,423 basehttp 33264 19400 "GET /api/model-designer/projects HTTP/1.1" 200 785
INFO 2025-07-14 21:43:40,685 logging 33264 19400 Request started - ID: ecda72fc-1304-4a01-b489-863c97ffa93f | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:40,697 logging 33264 19400 Request completed - ID: ecda72fc-1304-4a01-b489-863c97ffa93f | Status: 200 | Duration: 0.012s
INFO 2025-07-14 21:43:40,698 basehttp 33264 19400 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:43:40,820 logging 33264 19400 Request started - ID: 6536b0f0-3eb9-4e9a-b619-42866a3cec46 | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:43:40,828 errors 33264 19400 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:43:40,856 logging 33264 19400 Request completed - ID: 6536b0f0-3eb9-4e9a-b619-42866a3cec46 | Status: 500 | Duration: 0.036s
ERROR 2025-07-14 21:43:40,858 log 33264 19400 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:43:40,860 basehttp 33264 19400 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:43:40,999 logging 33264 19400 Request started - ID: 751aba18-1d04-48a6-8985-06324218b218 | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:41,023 logging 33264 19400 Request completed - ID: 751aba18-1d04-48a6-8985-06324218b218 | Status: 200 | Duration: 0.024s
INFO 2025-07-14 21:43:41,025 basehttp 33264 19400 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:43:41,559 logging 33264 19400 Request started - ID: 52861940-80d0-41ff-9748-0bf4f9a061e1 | Method: GET | Path: /api/model-designer/projects/3/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:41,604 logging 33264 19400 Request completed - ID: 52861940-80d0-41ff-9748-0bf4f9a061e1 | Status: 200 | Duration: 0.045s
INFO 2025-07-14 21:43:41,606 basehttp 33264 19400 "GET /api/model-designer/projects/3/models HTTP/1.1" 200 665
INFO 2025-07-14 21:43:41,615 logging 33264 19400 Request started - ID: f3a0e8ff-e5ad-41f3-a39c-94cbe7aa7444 | Method: GET | Path: /api/model-designer/models/4/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:41,654 logging 33264 19400 Request completed - ID: f3a0e8ff-e5ad-41f3-a39c-94cbe7aa7444 | Status: 200 | Duration: 0.039s
INFO 2025-07-14 21:43:41,655 basehttp 33264 19400 "GET /api/model-designer/models/4/fields HTTP/1.1" 200 986
INFO 2025-07-14 21:43:41,664 logging 33264 19400 Request started - ID: 01a78b6c-25eb-4b22-9c40-552c3371217e | Method: GET | Path: /api/model-designer/models/5/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:41,704 logging 33264 19400 Request completed - ID: 01a78b6c-25eb-4b22-9c40-552c3371217e | Status: 200 | Duration: 0.040s
INFO 2025-07-14 21:43:41,705 basehttp 33264 19400 "GET /api/model-designer/models/5/fields HTTP/1.1" 200 499
INFO 2025-07-14 21:43:43,441 logging 33264 19400 Request started - ID: 70d90c03-51c8-401d-8ad1-77ff68bf0c69 | Method: GET | Path: /api/model-designer/projects/1/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:43,528 logging 33264 19400 Request completed - ID: 70d90c03-51c8-401d-8ad1-77ff68bf0c69 | Status: 200 | Duration: 0.087s
INFO 2025-07-14 21:43:43,529 basehttp 33264 19400 "GET /api/model-designer/projects/1/models HTTP/1.1" 200 799
INFO 2025-07-14 21:43:43,541 logging 33264 19400 Request started - ID: d46c19e2-1a30-4abd-a38d-6a0a2ff242e0 | Method: GET | Path: /api/model-designer/models/1/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:43,582 logging 33264 19400 Request completed - ID: d46c19e2-1a30-4abd-a38d-6a0a2ff242e0 | Status: 200 | Duration: 0.041s
INFO 2025-07-14 21:43:43,583 basehttp 33264 19400 "GET /api/model-designer/models/1/fields HTTP/1.1" 200 2554
INFO 2025-07-14 21:43:43,596 logging 33264 19400 Request started - ID: 8c27fddd-afaf-4d53-b26f-5c76a39b77ca | Method: GET | Path: /api/model-designer/models/2/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:43:43,634 logging 33264 19400 Request completed - ID: 8c27fddd-afaf-4d53-b26f-5c76a39b77ca | Status: 200 | Duration: 0.038s
INFO 2025-07-14 21:43:43,635 basehttp 33264 19400 "GET /api/model-designer/models/2/fields HTTP/1.1" 200 1527
INFO 2025-07-14 21:43:46,759 logging 33264 19400 Request started - ID: 1027343a-9506-428c-9f60-0f0622442daa | Method: POST | Path: /api/model-designer/projects/1/generate | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:43:46,774 errors 33264 19400 NOT NULL constraint failed: model_designer_generation_history.output_directory
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: model_designer_generation_history.output_directory

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\api.py", line 295, in generate_code
    history = CodeGenerationService.generate_code(project, user, options)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py", line 222, in generate_code
    history = CodeGenerationHistory.objects.create(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
           ^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: model_designer_generation_history.output_directory
INFO 2025-07-14 21:43:46,843 logging 33264 19400 Request completed - ID: 1027343a-9506-428c-9f60-0f0622442daa | Status: 500 | Duration: 0.084s
ERROR 2025-07-14 21:43:46,844 log 33264 19400 Internal Server Error: /api/model-designer/projects/1/generate
ERROR 2025-07-14 21:43:46,846 basehttp 33264 19400 "POST /api/model-designer/projects/1/generate HTTP/1.1" 500 5151
INFO 2025-07-14 21:44:23,430 logging 33264 19400 Request started - ID: 1f2a06df-2455-4a82-82c8-c64326698d81 | Method: POST | Path: /api/model-designer/import-yaml | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:23,873 logging 33264 19400 Request completed - ID: 1f2a06df-2455-4a82-82c8-c64326698d81 | Status: 200 | Duration: 0.442s
INFO 2025-07-14 21:44:23,874 basehttp 33264 19400 "POST /api/model-designer/import-yaml HTTP/1.1" 200 5589
INFO 2025-07-14 21:44:25,238 logging 33264 19400 Request started - ID: 4e07fe5f-e377-4e8c-ada9-b4002af11b90 | Method: GET | Path: /api/model-designer/projects/4/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:25,293 logging 33264 19400 Request completed - ID: 4e07fe5f-e377-4e8c-ada9-b4002af11b90 | Status: 200 | Duration: 0.055s
INFO 2025-07-14 21:44:25,293 basehttp 33264 19400 "GET /api/model-designer/projects/4/models HTTP/1.1" 200 1525
INFO 2025-07-14 21:44:25,303 logging 33264 19400 Request started - ID: 4a66e464-7c9c-41c4-8ffc-abfe2bc2c752 | Method: GET | Path: /api/model-designer/models/6/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:25,354 logging 33264 19400 Request completed - ID: 4a66e464-7c9c-41c4-8ffc-abfe2bc2c752 | Status: 200 | Duration: 0.051s
INFO 2025-07-14 21:44:25,355 basehttp 33264 19400 "GET /api/model-designer/models/6/fields HTTP/1.1" 200 9948
INFO 2025-07-14 21:44:25,367 logging 33264 19400 Request started - ID: 91b341f7-2234-43ce-bb5d-335a68363388 | Method: GET | Path: /api/model-designer/models/7/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:25,413 logging 33264 19400 Request completed - ID: 91b341f7-2234-43ce-bb5d-335a68363388 | Status: 200 | Duration: 0.046s
INFO 2025-07-14 21:44:25,414 basehttp 33264 19400 "GET /api/model-designer/models/7/fields HTTP/1.1" 200 3162
INFO 2025-07-14 21:44:25,429 logging 33264 19400 Request started - ID: 0bc8a257-fece-4f58-811f-be1698628da4 | Method: GET | Path: /api/model-designer/models/8/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:25,466 logging 33264 19400 Request completed - ID: 0bc8a257-fece-4f58-811f-be1698628da4 | Status: 200 | Duration: 0.038s
INFO 2025-07-14 21:44:25,467 basehttp 33264 19400 "GET /api/model-designer/models/8/fields HTTP/1.1" 200 1617
INFO 2025-07-14 21:44:27,085 autoreload 33264 6668 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:44:29,250 autoreload 27996 20028 Watching for file changes with StatReloader
INFO 2025-07-14 21:44:30,175 logging 27996 35424 Request started - ID: de06b9cf-ee89-4000-9881-4d81cd7f3e33 | Method: GET | Path: /api/model-designer/projects/4/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:30,477 logging 27996 35424 Request completed - ID: de06b9cf-ee89-4000-9881-4d81cd7f3e33 | Status: 200 | Duration: 0.302s
INFO 2025-07-14 21:44:30,478 basehttp 27996 35424 "GET /api/model-designer/projects/4/models HTTP/1.1" 200 1525
INFO 2025-07-14 21:44:30,486 logging 27996 35424 Request started - ID: d087ad44-3140-46e1-8827-aa07cc4cb9e8 | Method: GET | Path: /api/model-designer/models/6/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:30,518 logging 27996 35424 Request completed - ID: d087ad44-3140-46e1-8827-aa07cc4cb9e8 | Status: 200 | Duration: 0.032s
INFO 2025-07-14 21:44:30,519 basehttp 27996 35424 "GET /api/model-designer/models/6/fields HTTP/1.1" 200 9948
INFO 2025-07-14 21:44:30,527 logging 27996 35424 Request started - ID: af44c8c2-7626-4d12-972e-b31c5454a95c | Method: GET | Path: /api/model-designer/models/7/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:30,561 logging 27996 35424 Request completed - ID: af44c8c2-7626-4d12-972e-b31c5454a95c | Status: 200 | Duration: 0.035s
INFO 2025-07-14 21:44:30,562 basehttp 27996 35424 "GET /api/model-designer/models/7/fields HTTP/1.1" 200 3162
INFO 2025-07-14 21:44:30,570 logging 27996 35424 Request started - ID: f9c7945f-bd29-44aa-9f71-1d79da680428 | Method: GET | Path: /api/model-designer/models/8/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:44:30,600 logging 27996 35424 Request completed - ID: f9c7945f-bd29-44aa-9f71-1d79da680428 | Status: 200 | Duration: 0.030s
INFO 2025-07-14 21:44:30,601 basehttp 27996 35424 "GET /api/model-designer/models/8/fields HTTP/1.1" 200 1617
INFO 2025-07-14 21:44:38,706 autoreload 27996 20028 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:44:40,564 autoreload 13848 3112 Watching for file changes with StatReloader
INFO 2025-07-14 21:45:08,214 logging 13848 28424 Request started - ID: 9c73051d-c039-4da7-9c74-205ece83cafe | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:08,298 logging 13848 28424 Request completed - ID: 9c73051d-c039-4da7-9c74-205ece83cafe | Status: 200 | Duration: 0.085s
INFO 2025-07-14 21:45:08,299 basehttp 13848 28424 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 21:45:08,941 logging 13848 28424 Request started - ID: b9cd88d5-5d85-4bd4-a357-22b8034570c2 | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:09,288 logging 13848 28424 Request completed - ID: b9cd88d5-5d85-4bd4-a357-22b8034570c2 | Status: 200 | Duration: 0.347s
INFO 2025-07-14 21:45:09,290 basehttp 13848 28424 "GET /api/model-designer/projects HTTP/1.1" 200 1074
INFO 2025-07-14 21:45:09,501 logging 13848 28424 Request started - ID: e0bed68c-3b90-4f42-9c63-5b9b254ca791 | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:09,520 logging 13848 28424 Request completed - ID: e0bed68c-3b90-4f42-9c63-5b9b254ca791 | Status: 200 | Duration: 0.019s
INFO 2025-07-14 21:45:09,522 basehttp 13848 28424 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:45:09,586 logging 13848 28424 Request started - ID: 1a1e7289-5ebb-4ceb-8ee0-2f0eb76c60b4 | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:45:09,619 errors 13848 28424 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:45:09,672 logging 13848 28424 Request completed - ID: 1a1e7289-5ebb-4ceb-8ee0-2f0eb76c60b4 | Status: 500 | Duration: 0.086s
ERROR 2025-07-14 21:45:09,674 log 13848 28424 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:45:09,676 basehttp 13848 28424 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:45:09,727 logging 13848 28424 Request started - ID: e9ce4071-53cf-4513-bdff-9069e7967fce | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:09,775 logging 13848 28424 Request completed - ID: e9ce4071-53cf-4513-bdff-9069e7967fce | Status: 200 | Duration: 0.048s
INFO 2025-07-14 21:45:09,776 basehttp 13848 28424 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:45:10,536 logging 13848 28424 Request started - ID: ddb05231-a825-4c02-98e2-2d274cc538e9 | Method: GET | Path: /api/model-designer/projects/2/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:10,640 logging 13848 28424 Request completed - ID: ddb05231-a825-4c02-98e2-2d274cc538e9 | Status: 200 | Duration: 0.105s
INFO 2025-07-14 21:45:10,641 basehttp 13848 28424 "GET /api/model-designer/projects/2/models HTTP/1.1" 200 387
INFO 2025-07-14 21:45:10,650 logging 13848 28424 Request started - ID: d4d638d2-4ee7-4eb5-b5b6-b4b3b75c59d4 | Method: GET | Path: /api/model-designer/models/3/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:10,705 logging 13848 28424 Request completed - ID: d4d638d2-4ee7-4eb5-b5b6-b4b3b75c59d4 | Status: 200 | Duration: 0.055s
INFO 2025-07-14 21:45:10,707 basehttp 13848 28424 "GET /api/model-designer/models/3/fields HTTP/1.1" 200 2549
INFO 2025-07-14 21:45:11,412 logging 13848 28424 Request started - ID: 8bf4e075-e02e-4ee0-be7c-b3e54d4069f7 | Method: GET | Path: /api/model-designer/projects/1/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:11,458 logging 13848 28424 Request completed - ID: 8bf4e075-e02e-4ee0-be7c-b3e54d4069f7 | Status: 200 | Duration: 0.046s
INFO 2025-07-14 21:45:11,459 basehttp 13848 28424 "GET /api/model-designer/projects/1/models HTTP/1.1" 200 799
INFO 2025-07-14 21:45:11,467 logging 13848 28424 Request started - ID: 19f5c078-f9c1-440e-a44e-5c45584eb4fc | Method: GET | Path: /api/model-designer/models/1/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:11,511 logging 13848 28424 Request completed - ID: 19f5c078-f9c1-440e-a44e-5c45584eb4fc | Status: 200 | Duration: 0.044s
INFO 2025-07-14 21:45:11,513 basehttp 13848 28424 "GET /api/model-designer/models/1/fields HTTP/1.1" 200 2554
INFO 2025-07-14 21:45:11,525 logging 13848 28424 Request started - ID: 11562989-b76c-4775-b5e2-390a7db1f4a1 | Method: GET | Path: /api/model-designer/models/2/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:11,573 logging 13848 28424 Request completed - ID: 11562989-b76c-4775-b5e2-390a7db1f4a1 | Status: 200 | Duration: 0.048s
INFO 2025-07-14 21:45:11,574 basehttp 13848 28424 "GET /api/model-designer/models/2/fields HTTP/1.1" 200 1527
INFO 2025-07-14 21:45:15,503 logging 13848 28424 Request started - ID: 1ccaf5ef-d620-4175-8f17-4d9980209d5d | Method: POST | Path: /api/model-designer/projects/1/generate | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:45:15,516 errors 13848 28424 NOT NULL constraint failed: model_designer_generation_history.output_directory
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: model_designer_generation_history.output_directory

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\api.py", line 295, in generate_code
    history = CodeGenerationService.generate_code(project, user, options)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py", line 231, in generate_code
    history = CodeGenerationHistory.objects.create(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
           ^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: model_designer_generation_history.output_directory
INFO 2025-07-14 21:45:15,557 logging 13848 28424 Request completed - ID: 1ccaf5ef-d620-4175-8f17-4d9980209d5d | Status: 500 | Duration: 0.055s
ERROR 2025-07-14 21:45:15,558 log 13848 28424 Internal Server Error: /api/model-designer/projects/1/generate
ERROR 2025-07-14 21:45:15,558 basehttp 13848 28424 "POST /api/model-designer/projects/1/generate HTTP/1.1" 500 5151
INFO 2025-07-14 21:45:23,925 autoreload 25384 38364 Watching for file changes with StatReloader
INFO 2025-07-14 21:45:28,441 logging 25384 6648 Request started - ID: c87fec90-0cb9-4d33-9feb-d0d6e02aeab3 | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:28,560 logging 25384 6648 Request completed - ID: c87fec90-0cb9-4d33-9feb-d0d6e02aeab3 | Status: 200 | Duration: 0.119s
INFO 2025-07-14 21:45:28,565 basehttp 25384 6648 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 21:45:28,964 logging 25384 6648 Request started - ID: 6596fa3c-c66a-4459-b2b9-ed1869299167 | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:29,431 logging 25384 6648 Request completed - ID: 6596fa3c-c66a-4459-b2b9-ed1869299167 | Status: 200 | Duration: 0.466s
INFO 2025-07-14 21:45:29,433 basehttp 25384 6648 "GET /api/model-designer/projects HTTP/1.1" 200 1074
INFO 2025-07-14 21:45:29,697 logging 25384 6648 Request started - ID: 251ea0e7-3133-41c2-b820-3b54eba9495a | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:29,727 logging 25384 6648 Request completed - ID: 251ea0e7-3133-41c2-b820-3b54eba9495a | Status: 200 | Duration: 0.031s
INFO 2025-07-14 21:45:29,729 basehttp 25384 6648 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:45:29,754 logging 25384 6648 Request started - ID: e060a8cf-3a18-4287-b260-2178c50d84cf | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:45:29,810 errors 25384 6648 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:45:29,861 logging 25384 6648 Request completed - ID: e060a8cf-3a18-4287-b260-2178c50d84cf | Status: 500 | Duration: 0.106s
ERROR 2025-07-14 21:45:29,866 log 25384 6648 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:45:29,869 basehttp 25384 6648 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:45:29,921 logging 25384 6648 Request started - ID: 00c79dab-b73c-43a3-84de-8a1975fc7679 | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:29,970 logging 25384 6648 Request completed - ID: 00c79dab-b73c-43a3-84de-8a1975fc7679 | Status: 200 | Duration: 0.049s
INFO 2025-07-14 21:45:29,971 basehttp 25384 6648 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:45:31,353 logging 25384 6648 Request started - ID: d6f053c9-da52-4568-9476-9a45d281305e | Method: GET | Path: /api/model-designer/projects/1/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:31,395 logging 25384 6648 Request completed - ID: d6f053c9-da52-4568-9476-9a45d281305e | Status: 200 | Duration: 0.042s
INFO 2025-07-14 21:45:31,396 basehttp 25384 6648 "GET /api/model-designer/projects/1/models HTTP/1.1" 200 799
INFO 2025-07-14 21:45:31,404 logging 25384 6648 Request started - ID: 64deff89-2f2c-44ef-82a8-cc40e4b8350f | Method: GET | Path: /api/model-designer/models/1/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:31,448 logging 25384 6648 Request completed - ID: 64deff89-2f2c-44ef-82a8-cc40e4b8350f | Status: 200 | Duration: 0.044s
INFO 2025-07-14 21:45:31,451 basehttp 25384 6648 "GET /api/model-designer/models/1/fields HTTP/1.1" 200 2554
INFO 2025-07-14 21:45:31,467 logging 25384 6648 Request started - ID: f8a4605f-4b36-4ba4-b07c-304883e4107c | Method: GET | Path: /api/model-designer/models/2/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:45:31,517 logging 25384 6648 Request completed - ID: f8a4605f-4b36-4ba4-b07c-304883e4107c | Status: 200 | Duration: 0.050s
INFO 2025-07-14 21:45:31,519 basehttp 25384 6648 "GET /api/model-designer/models/2/fields HTTP/1.1" 200 1527
INFO 2025-07-14 21:45:32,426 logging 25384 6648 Request started - ID: 84863a83-bb6f-499b-a1f3-d92268bfa441 | Method: POST | Path: /api/model-designer/projects/1/generate | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:45:32,440 errors 25384 6648 NOT NULL constraint failed: model_designer_generation_history.output_directory
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: model_designer_generation_history.output_directory

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\api.py", line 295, in generate_code
    history = CodeGenerationService.generate_code(project, user, options)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py", line 231, in generate_code
    history = CodeGenerationHistory.objects.create(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
           ^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: model_designer_generation_history.output_directory
INFO 2025-07-14 21:45:32,510 logging 25384 6648 Request completed - ID: 84863a83-bb6f-499b-a1f3-d92268bfa441 | Status: 500 | Duration: 0.084s
ERROR 2025-07-14 21:45:32,511 log 25384 6648 Internal Server Error: /api/model-designer/projects/1/generate
ERROR 2025-07-14 21:45:32,512 basehttp 25384 6648 "POST /api/model-designer/projects/1/generate HTTP/1.1" 500 5151
INFO 2025-07-14 21:46:44,143 autoreload 25384 38364 F:\PycharmProjects\django_ninja_template\apps\model_designer\api.py changed, reloading.
INFO 2025-07-14 21:46:46,056 autoreload 31528 31488 Watching for file changes with StatReloader
INFO 2025-07-14 21:46:55,088 autoreload 31528 31488 F:\PycharmProjects\django_ninja_template\apps\model_designer\services.py changed, reloading.
INFO 2025-07-14 21:46:56,985 autoreload 17736 21000 Watching for file changes with StatReloader
INFO 2025-07-14 21:47:22,867 logging 17736 15972 Request started - ID: 87a4aefc-cfa0-4fa3-a0f1-2de3eb99a484 | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:22,894 logging 17736 15972 Request completed - ID: 87a4aefc-cfa0-4fa3-a0f1-2de3eb99a484 | Status: 200 | Duration: 0.027s
INFO 2025-07-14 21:47:22,895 basehttp 17736 15972 "GET /model-designer/ HTTP/1.1" 200 46607
INFO 2025-07-14 21:47:23,591 logging 17736 15972 Request started - ID: 24db2cdc-eb08-4e69-a4f3-459aa41abc42 | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:23,923 logging 17736 15972 Request completed - ID: 24db2cdc-eb08-4e69-a4f3-459aa41abc42 | Status: 200 | Duration: 0.333s
INFO 2025-07-14 21:47:23,924 basehttp 17736 15972 "GET /api/model-designer/projects HTTP/1.1" 200 1074
INFO 2025-07-14 21:47:24,105 logging 17736 15972 Request started - ID: c83fe1cf-cd0d-4917-920b-4d561187af5a | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:24,117 logging 17736 15972 Request completed - ID: c83fe1cf-cd0d-4917-920b-4d561187af5a | Status: 200 | Duration: 0.013s
INFO 2025-07-14 21:47:24,118 basehttp 17736 15972 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:47:24,181 logging 17736 15972 Request started - ID: 4f9e7117-23d5-407d-83b8-095c6ddae1c0 | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:47:24,220 errors 17736 15972 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:47:24,245 logging 17736 15972 Request completed - ID: 4f9e7117-23d5-407d-83b8-095c6ddae1c0 | Status: 500 | Duration: 0.064s
ERROR 2025-07-14 21:47:24,247 log 17736 15972 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:47:24,248 basehttp 17736 15972 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:47:24,279 logging 17736 15972 Request started - ID: 1676c684-a752-465f-b5e7-4ff4a0dcac67 | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:24,317 logging 17736 15972 Request completed - ID: 1676c684-a752-465f-b5e7-4ff4a0dcac67 | Status: 200 | Duration: 0.038s
INFO 2025-07-14 21:47:24,319 basehttp 17736 15972 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:47:25,061 logging 17736 15972 Request started - ID: abdefb47-9548-40f5-97c7-9deed0652b34 | Method: GET | Path: /api/model-designer/projects/1/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:25,145 logging 17736 15972 Request completed - ID: abdefb47-9548-40f5-97c7-9deed0652b34 | Status: 200 | Duration: 0.085s
INFO 2025-07-14 21:47:25,146 basehttp 17736 15972 "GET /api/model-designer/projects/1/models HTTP/1.1" 200 799
INFO 2025-07-14 21:47:25,155 logging 17736 15972 Request started - ID: 0a681adc-1b74-47cc-a199-b0d7d979e2f1 | Method: GET | Path: /api/model-designer/models/1/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:25,191 logging 17736 15972 Request completed - ID: 0a681adc-1b74-47cc-a199-b0d7d979e2f1 | Status: 200 | Duration: 0.036s
INFO 2025-07-14 21:47:25,192 basehttp 17736 15972 "GET /api/model-designer/models/1/fields HTTP/1.1" 200 2554
INFO 2025-07-14 21:47:25,200 logging 17736 15972 Request started - ID: 9728043e-d843-450c-94fb-36c17f6c556c | Method: GET | Path: /api/model-designer/models/2/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:25,238 logging 17736 15972 Request completed - ID: 9728043e-d843-450c-94fb-36c17f6c556c | Status: 200 | Duration: 0.038s
INFO 2025-07-14 21:47:25,239 basehttp 17736 15972 "GET /api/model-designer/models/2/fields HTTP/1.1" 200 1527
INFO 2025-07-14 21:47:26,116 logging 17736 15972 Request started - ID: 2dae79f0-3086-4289-8ab3-38131fea09aa | Method: POST | Path: /api/model-designer/projects/1/generate | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:47:26,336 logging 17736 15972 Request completed - ID: 2dae79f0-3086-4289-8ab3-38131fea09aa | Status: 200 | Duration: 0.220s
INFO 2025-07-14 21:47:26,337 basehttp 17736 15972 "POST /api/model-designer/projects/1/generate HTTP/1.1" 200 540
INFO 2025-07-14 21:50:51,241 autoreload 17736 21000 F:\PycharmProjects\django_ninja_template\apps\model_designer\api.py changed, reloading.
INFO 2025-07-14 21:50:53,143 autoreload 15612 39560 Watching for file changes with StatReloader
INFO 2025-07-14 21:51:20,456 logging 15612 27696 Request started - ID: 2a65aa78-7fcf-44af-a0e9-21e115d3532b | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:51:20,472 logging 15612 27696 Request failed - ID: 2a65aa78-7fcf-44af-a0e9-21e115d3532b | Exception: TemplateSyntaxError: Could not parse the remainder: ' === 'full' ? '应用名' : ''' from 'generateForm.generation_type === 'full' ? '应用名' : '''
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\views.py", line 17, in model_designer
    return render(request, "model_designer/designer.html")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' === 'full' ? '应用名' : ''' from 'generateForm.generation_type === 'full' ? '应用名' : '''
ERROR 2025-07-14 21:51:20,595 log 15612 27696 Internal Server Error: /model-designer/
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\apps\model_designer\views.py", line 17, in model_designer
    return render(request, "model_designer/designer.html")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' === 'full' ? '应用名' : ''' from 'generateForm.generation_type === 'full' ? '应用名' : '''
INFO 2025-07-14 21:51:20,664 logging 15612 27696 Request completed - ID: 2a65aa78-7fcf-44af-a0e9-21e115d3532b | Status: 500 | Duration: 0.208s
ERROR 2025-07-14 21:51:20,665 basehttp 15612 27696 "GET /model-designer/ HTTP/1.1" **********
INFO 2025-07-14 21:54:12,921 autoreload 15612 39560 F:\PycharmProjects\django_ninja_template\apps\model_designer\schemas.py changed, reloading.
INFO 2025-07-14 21:54:14,809 autoreload 19480 28452 Watching for file changes with StatReloader
INFO 2025-07-14 21:56:12,260 autoreload 36320 4792 Watching for file changes with StatReloader
INFO 2025-07-14 21:56:15,855 logging 36320 28264 Request started - ID: fb188c76-8f2e-4328-92a0-ee5d0b748d1e | Method: GET | Path: /model-designer/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:15,883 logging 36320 28264 Request completed - ID: fb188c76-8f2e-4328-92a0-ee5d0b748d1e | Status: 200 | Duration: 0.028s
INFO 2025-07-14 21:56:15,884 basehttp 36320 28264 "GET /model-designer/ HTTP/1.1" 200 49038
INFO 2025-07-14 21:56:17,090 logging 36320 28264 Request started - ID: 281f9342-0fd5-45e4-b89f-663b7f07d498 | Method: GET | Path: /api/model-designer/projects | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:17,307 logging 36320 28264 Request completed - ID: 281f9342-0fd5-45e4-b89f-663b7f07d498 | Status: 200 | Duration: 0.218s
INFO 2025-07-14 21:56:17,308 basehttp 36320 28264 "GET /api/model-designer/projects HTTP/1.1" 200 1074
INFO 2025-07-14 21:56:17,579 logging 36320 28264 Request started - ID: 9e1727cd-417d-4d10-9109-5ee848c63bbc | Method: GET | Path: /api/model-designer/field-types | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:17,594 logging 36320 28264 Request completed - ID: 9e1727cd-417d-4d10-9109-5ee848c63bbc | Status: 200 | Duration: 0.016s
INFO 2025-07-14 21:56:17,594 basehttp 36320 28264 "GET /api/model-designer/field-types HTTP/1.1" 200 1088
INFO 2025-07-14 21:56:17,762 logging 36320 28264 Request started - ID: 9e103921-6a47-447b-b3c8-fcb302209f1c | Method: GET | Path: /api/model-designer/templates | User:  | IP: 127.0.0.1
ERROR 2025-07-14 21:56:17,770 errors 36320 28264 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 134, in run
    return self._result_to_response(request, result, temporal_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\ninja\operation.py", line 280, in _result_to_response
    validated_object = response_model.model_validate(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_template\.venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for NinjaResponseSchema
response.0.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Page', 'verbos...', 'default': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.1.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'Category', 've...', 'default': False}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
response.2.template_config
  Input should be a valid dictionary [type=dict_type, input_value=[{'name': 'ProductCategor...注', 'blank': True}]}], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
INFO 2025-07-14 21:56:17,802 logging 36320 28264 Request completed - ID: 9e103921-6a47-447b-b3c8-fcb302209f1c | Status: 500 | Duration: 0.040s
ERROR 2025-07-14 21:56:17,805 log 36320 28264 Internal Server Error: /api/model-designer/templates
ERROR 2025-07-14 21:56:17,807 basehttp 36320 28264 "GET /api/model-designer/templates HTTP/1.1" 500 1570
INFO 2025-07-14 21:56:17,950 logging 36320 28264 Request started - ID: 1f79de65-be1f-4ef5-9813-3e4333e42e71 | Method: GET | Path: /api/model-designer/field-presets | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:17,973 logging 36320 28264 Request completed - ID: 1f79de65-be1f-4ef5-9813-3e4333e42e71 | Status: 200 | Duration: 0.023s
INFO 2025-07-14 21:56:17,974 basehttp 36320 28264 "GET /api/model-designer/field-presets HTTP/1.1" 200 3020
INFO 2025-07-14 21:56:20,291 logging 36320 28264 Request started - ID: f7773dd3-cde3-4ba1-898d-362f0997a3f1 | Method: GET | Path: /api/model-designer/projects/1/models | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:20,345 logging 36320 28264 Request completed - ID: f7773dd3-cde3-4ba1-898d-362f0997a3f1 | Status: 200 | Duration: 0.054s
INFO 2025-07-14 21:56:20,346 basehttp 36320 28264 "GET /api/model-designer/projects/1/models HTTP/1.1" 200 799
INFO 2025-07-14 21:56:20,355 logging 36320 28264 Request started - ID: 856d9e32-6e24-46a9-b2ec-fe36eb8fc004 | Method: GET | Path: /api/model-designer/models/1/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:20,407 logging 36320 28264 Request completed - ID: 856d9e32-6e24-46a9-b2ec-fe36eb8fc004 | Status: 200 | Duration: 0.052s
INFO 2025-07-14 21:56:20,409 basehttp 36320 28264 "GET /api/model-designer/models/1/fields HTTP/1.1" 200 2554
INFO 2025-07-14 21:56:20,420 logging 36320 28264 Request started - ID: a17e4fe6-8f16-4e50-a8bb-a239ce204846 | Method: GET | Path: /api/model-designer/models/2/fields | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:20,481 logging 36320 28264 Request completed - ID: a17e4fe6-8f16-4e50-a8bb-a239ce204846 | Status: 200 | Duration: 0.062s
INFO 2025-07-14 21:56:20,482 basehttp 36320 28264 "GET /api/model-designer/models/2/fields HTTP/1.1" 200 1527
INFO 2025-07-14 21:56:28,871 logging 36320 28264 Request started - ID: b9ef61f7-f384-4ed0-9ea9-d1f12eb1cd57 | Method: POST | Path: /api/model-designer/projects/1/generate/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:29,023 logging 36320 28264 Request completed - ID: b9ef61f7-f384-4ed0-9ea9-d1f12eb1cd57 | Status: 404 | Duration: 0.152s
WARNING 2025-07-14 21:56:29,024 log 36320 28264 Not Found: /api/model-designer/projects/1/generate/
WARNING 2025-07-14 21:56:29,025 basehttp 36320 28264 "POST /api/model-designer/projects/1/generate/ HTTP/1.1" 404 38921
INFO 2025-07-14 21:56:32,231 logging 36320 28264 Request started - ID: 4afa07dd-5ced-4e5b-8f5b-7ebeb0f18edd | Method: POST | Path: /api/model-designer/projects/1/generate/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:32,323 logging 36320 28264 Request completed - ID: 4afa07dd-5ced-4e5b-8f5b-7ebeb0f18edd | Status: 404 | Duration: 0.094s
WARNING 2025-07-14 21:56:32,325 log 36320 28264 Not Found: /api/model-designer/projects/1/generate/
WARNING 2025-07-14 21:56:32,326 basehttp 36320 28264 "POST /api/model-designer/projects/1/generate/ HTTP/1.1" 404 38920
INFO 2025-07-14 21:56:33,843 logging 36320 28264 Request started - ID: 7c8cf774-ed59-4d49-a3b6-0b79b40889b4 | Method: POST | Path: /api/model-designer/projects/1/generate/ | User:  | IP: 127.0.0.1
INFO 2025-07-14 21:56:33,973 logging 36320 28264 Request completed - ID: 7c8cf774-ed59-4d49-a3b6-0b79b40889b4 | Status: 404 | Duration: 0.131s
WARNING 2025-07-14 21:56:33,974 log 36320 28264 Not Found: /api/model-designer/projects/1/generate/
WARNING 2025-07-14 21:56:33,974 basehttp 36320 28264 "POST /api/model-designer/projects/1/generate/ HTTP/1.1" 404 38921
INFO 2025-07-14 22:26:09,602 autoreload 25224 18044 Watching for file changes with StatReloader
INFO 2025-07-14 22:26:48,710 autoreload 30176 33776 Watching for file changes with StatReloader
INFO 2025-07-14 22:27:36,409 autoreload 29904 31164 Watching for file changes with StatReloader
INFO 2025-07-14 22:29:24,285 autoreload 23672 34752 Watching for file changes with StatReloader
INFO 2025-07-14 22:31:17,927 autoreload 26000 16976 Watching for file changes with StatReloader
INFO 2025-07-14 22:33:26,542 autoreload 16880 8116 Watching for file changes with StatReloader
INFO 2025-07-14 22:33:39,720 autoreload 15828 39200 Watching for file changes with StatReloader
INFO 2025-07-14 22:34:18,080 autoreload 20028 14992 Watching for file changes with StatReloader
INFO 2025-07-14 22:36:55,052 autoreload 22028 35016 Watching for file changes with StatReloader
INFO 2025-07-14 22:37:38,174 autoreload 15588 5248 Watching for file changes with StatReloader
