"""
权限管理服务。
"""
from typing import List, Optional, Set
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from .models import Permission, Role, UserRole, UserPermission

User = get_user_model()


class PermissionService:
    """
    权限管理操作的服务类。
    """
    
    CACHE_TIMEOUT = 300  # 5 minutes
    
    @classmethod
    def user_has_permission(cls, user: User, permission_codename: str) -> bool:
        """
        检查用户是否具有特定权限。
        首先检查直接权限，然后检查基于角色的权限。
        """
        if not user or not user.is_active:
            return False
        
        # Superuser has all permissions
        if user.is_superuser:
            return True
        
        # Check cache first
        cache_key = f"user_permission_{user.id}_{permission_codename}"
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        # Check direct user permissions first (they override role permissions)
        direct_permission = cls._get_direct_user_permission(user, permission_codename)
        if direct_permission is not None:
            result = direct_permission
        else:
            # Check role-based permissions
            result = cls._check_role_permissions(user, permission_codename)
        
        # Cache the result
        cache.set(cache_key, result, cls.CACHE_TIMEOUT)
        return result
    
    @classmethod
    def get_user_permissions(cls, user: User) -> Set[str]:
        """
        Get all permissions for a user (both direct and role-based).
        """
        if not user or not user.is_active:
            return set()
        
        # Superuser has all permissions
        if user.is_superuser:
            return set(Permission.objects.values_list('codename', flat=True))
        
        # Check cache first
        cache_key = f"user_all_permissions_{user.id}"
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        permissions = set()
        
        # Get role-based permissions
        role_permissions = cls._get_role_permissions(user)
        permissions.update(role_permissions)
        
        # Apply direct user permissions (grants and denials)
        direct_permissions = cls._get_direct_user_permissions(user)
        for perm_codename, granted in direct_permissions.items():
            if granted:
                permissions.add(perm_codename)
            else:
                permissions.discard(perm_codename)
        
        # Cache the result
        cache.set(cache_key, permissions, cls.CACHE_TIMEOUT)
        return permissions
    
    @classmethod
    def assign_role_to_user(cls, user: User, role: Role, assigned_by: User = None, expires_at=None) -> UserRole:
        """
        Assign a role to a user.
        """
        user_role, created = UserRole.objects.get_or_create(
            user=user,
            role=role,
            defaults={
                'assigned_by': assigned_by,
                'expires_at': expires_at,
                'is_active': True
            }
        )
        
        if not created:
            # Update existing assignment
            user_role.assigned_by = assigned_by
            user_role.expires_at = expires_at
            user_role.is_active = True
            user_role.save()
        
        # Clear user permission cache
        cls._clear_user_cache(user)
        
        return user_role
    
    @classmethod
    def remove_role_from_user(cls, user: User, role: Role) -> bool:
        """
        Remove a role from a user.
        """
        try:
            user_role = UserRole.objects.get(user=user, role=role)
            user_role.delete()
            
            # Clear user permission cache
            cls._clear_user_cache(user)
            
            return True
        except UserRole.DoesNotExist:
            return False
    
    @classmethod
    def grant_permission_to_user(cls, user: User, permission: Permission, granted_by: User = None, expires_at=None) -> UserPermission:
        """
        Grant a direct permission to a user.
        """
        user_permission, created = UserPermission.objects.update_or_create(
            user=user,
            permission=permission,
            defaults={
                'granted': True,
                'granted_by': granted_by,
                'expires_at': expires_at
            }
        )
        
        # Clear user permission cache
        cls._clear_user_cache(user)
        
        return user_permission
    
    @classmethod
    def deny_permission_to_user(cls, user: User, permission: Permission, granted_by: User = None, expires_at=None) -> UserPermission:
        """
        Deny a direct permission to a user (overrides role permissions).
        """
        user_permission, created = UserPermission.objects.update_or_create(
            user=user,
            permission=permission,
            defaults={
                'granted': False,
                'granted_by': granted_by,
                'expires_at': expires_at
            }
        )
        
        # Clear user permission cache
        cls._clear_user_cache(user)
        
        return user_permission
    
    @classmethod
    def remove_direct_permission(cls, user: User, permission: Permission) -> bool:
        """
        Remove a direct permission from a user.
        """
        try:
            user_permission = UserPermission.objects.get(user=user, permission=permission)
            user_permission.delete()
            
            # Clear user permission cache
            cls._clear_user_cache(user)
            
            return True
        except UserPermission.DoesNotExist:
            return False
    
    @classmethod
    def _get_direct_user_permission(cls, user: User, permission_codename: str) -> Optional[bool]:
        """
        Get direct user permission (returns None if not found, True/False if found).
        """
        try:
            user_permission = UserPermission.objects.select_related('permission').get(
                user=user,
                permission__codename=permission_codename
            )
            
            # Check if permission is expired
            if user_permission.is_expired():
                return None
            
            return user_permission.granted
        except UserPermission.DoesNotExist:
            return None
    
    @classmethod
    def _check_role_permissions(cls, user: User, permission_codename: str) -> bool:
        """
        Check if user has permission through roles.
        """
        # Get active, non-expired roles for the user
        user_roles = UserRole.objects.filter(
            user=user,
            is_active=True,
            role__is_active=True
        ).select_related('role')
        
        for user_role in user_roles:
            # Skip expired roles
            if user_role.is_expired():
                continue
            
            # Check if role has the permission
            if user_role.role.has_permission(permission_codename):
                return True
        
        return False
    
    @classmethod
    def _get_role_permissions(cls, user: User) -> Set[str]:
        """
        Get all permissions from user's roles.
        """
        permissions = set()
        
        # Get active, non-expired roles for the user
        user_roles = UserRole.objects.filter(
            user=user,
            is_active=True,
            role__is_active=True
        ).select_related('role').prefetch_related('role__permissions')
        
        for user_role in user_roles:
            # Skip expired roles
            if user_role.is_expired():
                continue
            
            # Add all permissions from this role
            role_permissions = user_role.role.permissions.values_list('codename', flat=True)
            permissions.update(role_permissions)
        
        return permissions
    
    @classmethod
    def _get_direct_user_permissions(cls, user: User) -> dict:
        """
        Get all direct user permissions as a dict {codename: granted}.
        """
        permissions = {}
        
        user_permissions = UserPermission.objects.filter(
            user=user
        ).select_related('permission')
        
        for user_permission in user_permissions:
            # Skip expired permissions
            if user_permission.is_expired():
                continue
            
            permissions[user_permission.permission.codename] = user_permission.granted
        
        return permissions
    
    @classmethod
    def _clear_user_cache(cls, user: User):
        """
        Clear all cached permissions for a user.
        """
        # Clear specific permission caches
        all_permissions = Permission.objects.values_list('codename', flat=True)
        for permission_codename in all_permissions:
            cache_key = f"user_permission_{user.id}_{permission_codename}"
            cache.delete(cache_key)
        
        # Clear all permissions cache
        cache_key = f"user_all_permissions_{user.id}"
        cache.delete(cache_key)
