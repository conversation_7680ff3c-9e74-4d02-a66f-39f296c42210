# 📱 实际案例 - 社交媒体平台

本案例将展示如何使用 Django Ninja 脚手架项目构建一个现代化的社交媒体平台，包括用户关系、动态发布、实时通知、推荐算法等高级功能。

## 🎯 案例目标

通过本案例，您将学会：
- ✅ 设计复杂的社交关系模型
- ✅ 实现动态发布和时间线功能
- ✅ 构建实时通知系统
- ✅ 实现推荐算法和个性化推送
- ✅ 处理大量数据的性能优化
- ✅ 实现内容审核和安全机制

## 📊 业务需求分析

### 核心功能模块
```
社交媒体平台
├── 👥 用户关系
│   ├── 关注/粉丝系统
│   ├── 好友申请
│   ├── 黑名单管理
│   └── 用户推荐
├── 📝 内容发布
│   ├── 动态发布
│   ├── 图片/视频上传
│   ├── 话题标签
│   └── 地理位置
├── 💬 互动功能
│   ├── 点赞/收藏
│   ├── 评论回复
│   ├── 转发分享
│   └── 私信聊天
├── 📺 时间线
│   ├── 个人时间线
│   ├── 关注时间线
│   ├── 推荐时间线
│   └── 话题时间线
├── 🔔 通知系统
│   ├── 实时通知
│   ├── 推送消息
│   ├── 邮件通知
│   └── 通知设置
└── 🛡️ 安全审核
    ├── 内容审核
    ├── 举报处理
    ├── 敏感词过滤
    └── 反垃圾机制
```

## 🏗️ 数据模型设计

### 1. 用户关系模型

```python
# apps/social/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
import uuid

User = get_user_model()

class UserProfile(models.Model):
    """用户资料扩展"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='social_profile'
    )

    # 基本信息
    bio = models.TextField(max_length=500, blank=True, verbose_name='个人简介')
    website = models.URLField(blank=True, verbose_name='个人网站')
    location = models.CharField(max_length=100, blank=True, verbose_name='所在地')
    birth_date = models.DateField(null=True, blank=True, verbose_name='生日')

    # 隐私设置
    is_private = models.BooleanField(default=False, verbose_name='私密账户')
    allow_message_from_strangers = models.BooleanField(default=True, verbose_name='允许陌生人私信')
    show_online_status = models.BooleanField(default=True, verbose_name='显示在线状态')

    # 统计信息
    followers_count = models.PositiveIntegerField(default=0, verbose_name='粉丝数')
    following_count = models.PositiveIntegerField(default=0, verbose_name='关注数')
    posts_count = models.PositiveIntegerField(default=0, verbose_name='动态数')

    # 活跃度
    last_active = models.DateTimeField(auto_now=True, verbose_name='最后活跃时间')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'

    def __str__(self):
        return f"{self.user.username}的资料"

class Follow(models.Model):
    """关注关系"""
    follower = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='following_set',
        verbose_name='关注者'
    )
    following = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='followers_set',
        verbose_name='被关注者'
    )

    # 关注状态
    is_mutual = models.BooleanField(default=False, verbose_name='互相关注')

    # 通知设置
    notify_posts = models.BooleanField(default=True, verbose_name='动态通知')
    notify_live = models.BooleanField(default=False, verbose_name='直播通知')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '关注关系'
        verbose_name_plural = '关注关系'
        unique_together = ['follower', 'following']
        indexes = [
            models.Index(fields=['follower']),
            models.Index(fields=['following']),
        ]

    def __str__(self):
        return f"{self.follower.username} 关注 {self.following.username}"

    def save(self, *args, **kwargs):
        # 检查是否互相关注
        if Follow.objects.filter(
            follower=self.following,
            following=self.follower
        ).exists():
            self.is_mutual = True
            # 同时更新对方的互关状态
            Follow.objects.filter(
                follower=self.following,
                following=self.follower
            ).update(is_mutual=True)

        super().save(*args, **kwargs)

        # 更新统计数据
        self.update_follow_counts()

    def delete(self, *args, **kwargs):
        # 取消互关状态
        Follow.objects.filter(
            follower=self.following,
            following=self.follower
        ).update(is_mutual=False)

        super().delete(*args, **kwargs)

        # 更新统计数据
        self.update_follow_counts()

    def update_follow_counts(self):
        """更新关注数统计"""
        # 更新关注者的关注数
        follower_profile, _ = UserProfile.objects.get_or_create(user=self.follower)
        follower_profile.following_count = Follow.objects.filter(
            follower=self.follower
        ).count()
        follower_profile.save()

        # 更新被关注者的粉丝数
        following_profile, _ = UserProfile.objects.get_or_create(user=self.following)
        following_profile.followers_count = Follow.objects.filter(
            following=self.following
        ).count()
        following_profile.save()

class Block(models.Model):
    """黑名单"""
    blocker = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='blocking_set',
        verbose_name='拉黑者'
    )
    blocked = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='blocked_by_set',
        verbose_name='被拉黑者'
    )

    reason = models.CharField(max_length=200, blank=True, verbose_name='拉黑原因')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '黑名单'
        verbose_name_plural = '黑名单'
        unique_together = ['blocker', 'blocked']

    def __str__(self):
        return f"{self.blocker.username} 拉黑 {self.blocked.username}"

    def save(self, *args, **kwargs):
        # 拉黑时自动取消关注关系
        Follow.objects.filter(
            models.Q(follower=self.blocker, following=self.blocked) |
            models.Q(follower=self.blocked, following=self.blocker)
        ).delete()

        super().save(*args, **kwargs)

class FriendRequest(models.Model):
    """好友申请"""

    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('accepted', '已接受'),
        ('rejected', '已拒绝'),
        ('cancelled', '已取消'),
    ]

    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_friend_requests',
        verbose_name='发送者'
    )
    receiver = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_friend_requests',
        verbose_name='接收者'
    )

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    message = models.TextField(max_length=200, blank=True, verbose_name='申请消息')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '好友申请'
        verbose_name_plural = '好友申请'
        unique_together = ['sender', 'receiver']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.sender.username} 向 {self.receiver.username} 发送好友申请"

class Friendship(models.Model):
    """好友关系"""
    user1 = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='friendships_as_user1'
    )
    user2 = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='friendships_as_user2'
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '好友关系'
        verbose_name_plural = '好友关系'
        unique_together = ['user1', 'user2']

    def __str__(self):
        return f"{self.user1.username} 和 {self.user2.username} 是好友"

    def save(self, *args, **kwargs):
        # 确保 user1.id < user2.id，避免重复记录
        if self.user1.id > self.user2.id:
            self.user1, self.user2 = self.user2, self.user1
        super().save(*args, **kwargs)
```

### 2. 内容发布模型

```python
# 继续 apps/social/models.py

class Topic(models.Model):
    """话题标签"""
    name = models.CharField(max_length=100, unique=True, verbose_name='话题名称')
    description = models.TextField(blank=True, verbose_name='话题描述')

    # 统计信息
    posts_count = models.PositiveIntegerField(default=0, verbose_name='动态数量')
    followers_count = models.PositiveIntegerField(default=0, verbose_name='关注数量')

    # 话题状态
    is_trending = models.BooleanField(default=False, verbose_name='是否热门')
    is_official = models.BooleanField(default=False, verbose_name='官方话题')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '话题'
        verbose_name_plural = '话题'
        ordering = ['-posts_count']

    def __str__(self):
        return f"#{self.name}"

class Post(models.Model):
    """动态/帖子"""

    TYPE_CHOICES = [
        ('text', '文字'),
        ('image', '图片'),
        ('video', '视频'),
        ('link', '链接'),
        ('poll', '投票'),
    ]

    VISIBILITY_CHOICES = [
        ('public', '公开'),
        ('followers', '仅关注者'),
        ('friends', '仅好友'),
        ('private', '仅自己'),
    ]

    # 基本信息
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='posts',
        verbose_name='作者'
    )

    # 内容
    content = models.TextField(max_length=2000, verbose_name='内容')
    post_type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='text')
    visibility = models.CharField(max_length=20, choices=VISIBILITY_CHOICES, default='public')

    # 媒体文件
    images = models.JSONField(default=list, verbose_name='图片列表')
    video_url = models.URLField(blank=True, verbose_name='视频链接')

    # 链接信息
    link_url = models.URLField(blank=True, verbose_name='链接地址')
    link_title = models.CharField(max_length=200, blank=True, verbose_name='链接标题')
    link_description = models.TextField(blank=True, verbose_name='链接描述')
    link_image = models.URLField(blank=True, verbose_name='链接图片')

    # 地理位置
    location_name = models.CharField(max_length=200, blank=True, verbose_name='位置名称')
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='纬度'
    )
    longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='经度'
    )

    # 关联
    topics = models.ManyToManyField(Topic, blank=True, verbose_name='话题标签')
    mentioned_users = models.ManyToManyField(
        User,
        blank=True,
        related_name='mentioned_in_posts',
        verbose_name='提及用户'
    )

    # 转发相关
    original_post = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='reposts',
        verbose_name='原始动态'
    )
    repost_comment = models.TextField(max_length=500, blank=True, verbose_name='转发评论')

    # 统计信息
    likes_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')
    comments_count = models.PositiveIntegerField(default=0, verbose_name='评论数')
    reposts_count = models.PositiveIntegerField(default=0, verbose_name='转发数')
    views_count = models.PositiveIntegerField(default=0, verbose_name='浏览数')

    # 状态
    is_pinned = models.BooleanField(default=False, verbose_name='是否置顶')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')

    # 审核状态
    is_approved = models.BooleanField(default=True, verbose_name='是否审核通过')
    moderation_reason = models.CharField(max_length=200, blank=True, verbose_name='审核原因')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '动态'
        verbose_name_plural = '动态'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['author']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_approved']),
            models.Index(fields=['visibility']),
        ]

    def __str__(self):
        return f"{self.author.username} 的动态"

    @property
    def is_repost(self):
        """是否为转发"""
        return self.original_post is not None

    def save(self, *args, **kwargs):
        # 提取话题标签
        if self.content:
            self.extract_topics()

        # 提取提及用户
        if self.content:
            self.extract_mentions()

        super().save(*args, **kwargs)

    def extract_topics(self):
        """提取话题标签"""
        import re
        topic_pattern = r'#([^#\s]+)'
        topics = re.findall(topic_pattern, self.content)

        for topic_name in topics:
            topic, created = Topic.objects.get_or_create(name=topic_name)
            self.topics.add(topic)

    def extract_mentions(self):
        """提取提及用户"""
        import re
        mention_pattern = r'@([a-zA-Z0-9_]+)'
        usernames = re.findall(mention_pattern, self.content)

        for username in usernames:
            try:
                user = User.objects.get(username=username)
                self.mentioned_users.add(user)
            except User.DoesNotExist:
                continue

class PostMedia(models.Model):
    """动态媒体文件"""

    TYPE_CHOICES = [
        ('image', '图片'),
        ('video', '视频'),
        ('audio', '音频'),
        ('document', '文档'),
    ]

    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='media_files',
        verbose_name='动态'
    )

    file_type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    file_url = models.URLField(verbose_name='文件链接')
    thumbnail_url = models.URLField(blank=True, verbose_name='缩略图链接')

    # 文件信息
    file_size = models.PositiveIntegerField(null=True, blank=True, verbose_name='文件大小')
    duration = models.PositiveIntegerField(null=True, blank=True, verbose_name='时长(秒)')
    width = models.PositiveIntegerField(null=True, blank=True, verbose_name='宽度')
    height = models.PositiveIntegerField(null=True, blank=True, verbose_name='高度')

    # 排序
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '动态媒体'
        verbose_name_plural = '动态媒体'
        ordering = ['sort_order', 'created_at']

    def __str__(self):
        return f"{self.post.author.username} 的 {self.get_file_type_display()}"

class Like(models.Model):
    """点赞"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='likes',
        verbose_name='用户'
    )

    # 使用通用外键支持多种内容类型
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.UUIDField()
    content_object = GenericForeignKey('content_type', 'object_id')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '点赞'
        verbose_name_plural = '点赞'
        unique_together = ['user', 'content_type', 'object_id']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['user']),
        ]

    def __str__(self):
        return f"{self.user.username} 点赞了 {self.content_object}"

class Comment(models.Model):
    """评论"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='动态'
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='作者'
    )

    # 评论内容
    content = models.TextField(max_length=1000, verbose_name='评论内容')

    # 回复关系
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name='父评论'
    )

    # 提及用户
    mentioned_users = models.ManyToManyField(
        User,
        blank=True,
        related_name='mentioned_in_comments',
        verbose_name='提及用户'
    )

    # 统计信息
    likes_count = models.PositiveIntegerField(default=0, verbose_name='点赞数')
    replies_count = models.PositiveIntegerField(default=0, verbose_name='回复数')

    # 状态
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')
    is_approved = models.BooleanField(default=True, verbose_name='是否审核通过')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '评论'
        verbose_name_plural = '评论'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['post']),
            models.Index(fields=['author']),
            models.Index(fields=['parent']),
        ]

    def __str__(self):
        return f"{self.author.username} 评论了 {self.post.author.username} 的动态"

    def save(self, *args, **kwargs):
        # 提取提及用户
        if self.content:
            self.extract_mentions()

        super().save(*args, **kwargs)

        # 更新动态评论数
        if not self.parent:  # 只有顶级评论才计入动态评论数
            self.post.comments_count = self.post.comments.filter(parent=None).count()
            self.post.save()

    def extract_mentions(self):
        """提取提及用户"""
        import re
        mention_pattern = r'@([a-zA-Z0-9_]+)'
        usernames = re.findall(mention_pattern, self.content)

        for username in usernames:
            try:
                user = User.objects.get(username=username)
                self.mentioned_users.add(user)
            except User.DoesNotExist:
                continue
```