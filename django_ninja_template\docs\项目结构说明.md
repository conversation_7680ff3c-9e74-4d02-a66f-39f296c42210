# 🏗️ 项目结构说明

本文档详细说明了 Django Ninja 脚手架项目的目录结构和各个模块的作用。

## 📁 项目总体结构

```
django_ninja_cms/
├── 📁 apps/                    # 应用模块目录
├── 📁 core/                    # 项目核心配置
├── 📁 docs/                    # 项目文档
├── 📁 frontend/                # 前端代码
├── 📁 logs/                    # 日志文件
├── 📁 static/                  # 静态文件
├── 📁 media/                   # 媒体文件
├── 📄 manage.py               # Django 管理脚本
├── 📄 requirements.txt        # Python 依赖
├── 📄 docker-compose.yml      # Docker 编排
├── 📄 Dockerfile             # Docker 镜像
└── 📄 README.md              # 项目说明
```

## 🎯 核心目录详解

### 📁 apps/ - 应用模块目录

Django Ninja 脚手架项目采用模块化设计，所有功能模块都放在 `apps/` 目录下：

```
apps/
├── 📁 authentication/         # 认证系统
├── 📁 core/                   # 核心功能模块
├── 📁 users/                  # 用户管理
├── 📁 permissions/            # 权限管理
├── 📁 storage/                # 文件存储
├── 📁 i18n/                   # 国际化
└── 📁 cms/                    # CMS 内容管理（示例）
```

#### 🔐 authentication/ - 认证系统
```
authentication/
├── 📄 __init__.py
├── 📄 apps.py                 # 应用配置
├── 📄 models.py               # 认证相关模型
├── 📄 api.py                  # 认证 API 接口
├── 📄 schemas.py              # 数据验证模式
├── 📄 services.py             # 业务逻辑服务
├── 📄 tasks.py                # 异步任务
└── 📄 admin.py                # Admin 配置
```

**主要功能**:
- JWT 令牌认证
- 用户注册、登录、登出
- 令牌刷新机制
- 登录监控和安全检查

#### 🛠️ core/ - 核心功能模块
```
core/
├── 📁 admin/                  # 自动 Admin 配置
├── 📁 factories/              # 数据工厂
├── 📁 frontend/               # 前端生成器
├── 📁 generators/             # 代码生成器
├── 📁 graphql/                # GraphQL 支持
├── 📁 management/             # 管理命令
├── 📁 permissions/            # 权限工具
├── 📄 models.py               # 基础模型
├── 📄 services.py             # 核心服务
└── 📄 tasks.py                # 核心任务
```

**主要功能**:
- 基础模型类 (BaseModel)
- 代码生成工具
- 自动 Admin 配置
- 数据工厂
- 管理命令

#### 👤 users/ - 用户管理
```
users/
├── 📄 models.py               # 用户模型
├── 📄 api.py                  # 用户 API
├── 📄 schemas.py              # 用户数据模式
├── 📄 services.py             # 用户服务
└── 📄 admin.py                # 用户 Admin
```

**主要功能**:
- 自定义用户模型
- 用户资料管理
- 用户偏好设置
- 头像上传

#### 🔒 permissions/ - 权限管理
```
permissions/
├── 📁 management/             # 权限管理命令
├── 📄 models.py               # 权限模型
├── 📄 api.py                  # 权限 API
├── 📄 decorators.py           # 权限装饰器
├── 📄 services.py             # 权限服务
└── 📄 admin.py                # 权限 Admin
```

**主要功能**:
- RBAC 权限模型
- 权限装饰器
- 动态权限分配
- 角色管理

#### 📁 storage/ - 文件存储
```
storage/
├── 📄 models.py               # 文件模型
├── 📄 api.py                  # 文件 API
├── 📄 services.py             # 存储服务
├── 📄 tasks.py                # 文件处理任务
└── 📄 admin.py                # 文件 Admin
```

**主要功能**:
- 多存储后端支持
- 文件上传和管理
- 图片处理
- 文件分享

#### 🌍 i18n/ - 国际化
```
i18n/
├── 📄 models.py               # 翻译模型
├── 📄 api.py                  # 国际化 API
├── 📄 middleware.py           # 语言中间件
├── 📄 services.py             # 翻译服务
└── 📄 admin.py                # 翻译 Admin
```

**主要功能**:
- 多语言支持
- 动态翻译管理
- 语言自动检测
- 翻译进度跟踪

### 📁 core/ - 项目核心配置

```
core/
├── 📁 settings/               # 多环境配置
│   ├── 📄 __init__.py
│   ├── 📄 base.py             # 基础配置
│   ├── 📄 development.py      # 开发环境
│   ├── 📄 production.py       # 生产环境
│   ├── 📄 testing.py          # 测试环境
│   └── 📄 staging.py          # 预发布环境
├── 📁 middleware/             # 自定义中间件
├── 📄 urls.py                 # 主路由配置
├── 📄 wsgi.py                 # WSGI 配置
├── 📄 asgi.py                 # ASGI 配置
└── 📄 celery.py               # Celery 配置
```

**主要功能**:
- 多环境配置管理
- 路由配置
- 中间件配置
- Celery 异步任务配置

### 📁 frontend/ - 前端代码

```
frontend/
├── 📁 src/
│   ├── 📁 components/         # Vue 组件
│   ├── 📁 views/              # 页面视图
│   ├── 📁 router/             # 路由配置
│   ├── 📁 store/              # 状态管理
│   ├── 📁 utils/              # 工具函数
│   └── 📁 assets/             # 静态资源
├── 📄 package.json            # 前端依赖
├── 📄 vite.config.js          # Vite 配置
└── 📄 index.html              # 入口页面
```

**主要功能**:
- Vue.js 3 前端框架
- 组件化开发
- 路由管理
- 状态管理

## 🔧 配置文件说明

### 📄 requirements.txt - Python 依赖
包含项目所需的所有 Python 包及其版本：

```txt
Django==5.2.4              # Web 框架
django-ninja==1.4.3        # API 框架
pydantic==2.11.7           # 数据验证
psycopg2-binary==2.9.10    # PostgreSQL 驱动
redis==5.2.1               # Redis 客户端
celery==5.4.0              # 异步任务队列
PyYAML==6.0.2               # YAML 解析
# ... 更多依赖
```

### 📄 docker-compose.yml - Docker 编排
定义了完整的开发环境：

```yaml
services:
  web:                      # Django 应用
  db:                       # PostgreSQL 数据库
  redis:                    # Redis 缓存
  celery:                   # Celery 工作进程
  nginx:                    # Nginx 反向代理
```

### 📄 .env - 环境变量
存储敏感配置信息：

```env
DJANGO_ENVIRONMENT=development
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://...
REDIS_URL=redis://localhost:6379/1
```

## 📝 文件命名规范

### Python 文件
- `models.py` - 数据模型定义
- `api.py` - API 接口定义
- `schemas.py` - Pydantic 数据模式
- `services.py` - 业务逻辑服务
- `tasks.py` - Celery 异步任务
- `admin.py` - Django Admin 配置
- `apps.py` - 应用配置
- `tests.py` - 测试用例

### 目录结构
- `management/commands/` - Django 管理命令
- `migrations/` - 数据库迁移文件
- `templates/` - HTML 模板
- `static/` - 静态文件
- `locale/` - 国际化文件

## 🎯 模块设计原则

### 1. 单一职责原则
每个模块只负责一个特定的功能领域：
- `authentication` 只处理认证相关
- `permissions` 只处理权限相关
- `storage` 只处理文件存储相关

### 2. 松耦合设计
模块之间通过明确的接口进行交互：
- 使用服务层 (`services.py`) 封装业务逻辑
- 通过 API 层 (`api.py`) 提供外部接口
- 使用事件和信号进行模块间通信

### 3. 可扩展性
支持轻松添加新模块：
- 统一的目录结构
- 标准的文件命名
- 一致的代码风格

### 4. 配置驱动
通过配置文件控制行为：
- 多环境配置支持
- 功能开关配置
- 第三方服务配置

## 🔍 如何添加新模块

### 1. 使用代码生成工具
```bash
# 生成新模块
python manage.py generate module blog --fields "title:str,content:text" --admin --api
```

### 2. 手动创建
```bash
# 创建目录结构
mkdir apps/new_module
cd apps/new_module

# 创建标准文件
touch __init__.py models.py api.py schemas.py services.py admin.py
```

### 3. 注册模块
在 `core/settings/base.py` 中添加：
```python
INSTALLED_APPS = [
    # ...
    'apps.new_module',
]
```

## 📚 相关文档

- [开发指南](开发指南.md) - 详细的开发流程
- [代码生成工具指南](代码生成工具指南.md) - 快速生成模块
- [API使用指南](API使用指南.md) - API 接口文档

---

**提示**: 理解项目结构是高效开发的基础，建议新手开发者仔细阅读本文档。
