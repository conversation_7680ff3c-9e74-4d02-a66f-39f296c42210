"""
云存储配置示例
将这些配置添加到你的Django settings文件中
"""

# 默认存储后端
DEFAULT_STORAGE_BACKEND = 'local'  # 可选: 'local', 'aws_s3', 'aliyun_oss', 'tencent_cos'
DEFAULT_CLOUD_STORAGE_BACKEND = 'aws_s3'  # 云存储默认后端

# AWS S3 配置
AWS_ACCESS_KEY_ID = 'your-access-key-id'
AWS_SECRET_ACCESS_KEY = 'your-secret-access-key'
AWS_STORAGE_BUCKET_NAME = 'your-bucket-name'
AWS_S3_REGION_NAME = 'us-east-1'
AWS_S3_CUSTOM_DOMAIN = None  # 可选: 自定义域名

# 阿里云OSS配置
ALIYUN_OSS_ACCESS_KEY_ID = 'your-access-key-id'
ALIYUN_OSS_ACCESS_KEY_SECRET = 'your-access-key-secret'
ALIYUN_OSS_BUCKET_NAME = 'your-bucket-name'
ALIYUN_OSS_ENDPOINT = 'https://oss-cn-hangzhou.aliyuncs.com'

# 腾讯云COS配置
TENCENT_COS_SECRET_ID = 'your-secret-id'
TENCENT_COS_SECRET_KEY = 'your-secret-key'
TENCENT_COS_BUCKET_NAME = 'your-bucket-name'
TENCENT_COS_REGION = 'ap-beijing'

# 文件上传配置
FILE_UPLOAD_MAX_MEMORY_SIZE = 2621440  # 2.5MB
FILE_UPLOAD_TEMP_DIR = None
FILE_UPLOAD_PERMISSIONS = 0o644
FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o755

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# 静态文件配置（生产环境建议使用CDN）
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# 安全配置
SECURE_FILE_UPLOAD = True
ALLOWED_FILE_EXTENSIONS = [
    # 图片
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg',
    # 文档
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf',
    # 压缩文件
    'zip', 'rar', '7z', 'tar', 'gz',
    # 音视频
    'mp3', 'wav', 'mp4', 'avi', 'mov', 'wmv', 'flv',
]

MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB

# 缓存配置（用于文件元数据缓存）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'storage_cache',
        'TIMEOUT': 300,  # 5分钟
    }
}

# Celery配置（用于异步文件处理）
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Shanghai'