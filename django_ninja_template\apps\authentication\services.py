"""
JWT 令牌管理的认证服务。
"""

import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from django.conf import settings
from django.contrib.auth import get_user_model, authenticate
from django.utils import timezone
from django.core.cache import cache
from ninja_jwt.tokens import RefreshToken as NinjaRefreshToken
from ninja_jwt.exceptions import TokenError, InvalidToken
from .models import (
    RefreshToken,
    LoginAttempt,
    PasswordResetToken,
    EmailVerificationToken,
)

User = get_user_model()


class AuthService:
    """
    认证操作的服务类。
    """

    @classmethod
    def authenticate_user(
        cls, email: str, password: str, ip_address: str = None, user_agent: str = None
    ) -> Optional[User]:
        """
        使用邮箱和密码验证用户。
        """
        # 检查频率限制
        if cls._is_rate_limited(email, ip_address):
            cls._log_login_attempt(
                email, ip_address, user_agent, False, "too_many_attempts"
            )
            return None

        # 验证用户
        user = authenticate(username=email, password=password)

        if user is not None:
            if user.is_active:
                cls._log_login_attempt(email, ip_address, user_agent, True)
                cls._clear_failed_attempts(email, ip_address)
                return user
            else:
                cls._log_login_attempt(
                    email, ip_address, user_agent, False, "account_disabled"
                )
        else:
            cls._log_login_attempt(
                email, ip_address, user_agent, False, "invalid_credentials"
            )

        return None

    @classmethod
    def generate_tokens(
        cls,
        user: User,
        device_name: str = None,
        ip_address: str = None,
        user_agent: str = None,
    ) -> Dict[str, str]:
        """
        为用户生成访问令牌和刷新令牌。
        """
        # 使用 ninja-jwt 生成令牌
        refresh = NinjaRefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        refresh_token_value = str(refresh)

        # 在数据库中存储刷新令牌记录
        refresh_token = RefreshToken.objects.create(
            user=user,
            token=refresh_token_value,
            expires_at=timezone.now() + settings.NINJA_JWT["REFRESH_TOKEN_LIFETIME"],
            device_name=device_name or "Unknown Device",
            ip_address=ip_address,
            user_agent=user_agent or "",
        )

        return {
            "access_token": access_token,
            "refresh_token": refresh_token_value,
            "token_type": "Bearer",
            "expires_in": int(
                settings.NINJA_JWT["ACCESS_TOKEN_LIFETIME"].total_seconds()
            ),
        }

    @classmethod
    def refresh_access_token(cls, refresh_token: str) -> Optional[Dict[str, str]]:
        """
        使用刷新令牌生成新的访问令牌。
        """
        try:
            # 验证数据库中的刷新令牌记录
            token_obj = RefreshToken.objects.get(
                token=refresh_token, is_revoked=False, expires_at__gt=timezone.now()
            )

            # 使用 ninja-jwt 验证和刷新令牌
            refresh = NinjaRefreshToken(refresh_token)
            access_token = str(refresh.access_token)

            return {
                "access_token": access_token,
                "token_type": "Bearer",
                "expires_in": int(
                    settings.NINJA_JWT["ACCESS_TOKEN_LIFETIME"].total_seconds()
                ),
            }

        except (RefreshToken.DoesNotExist, TokenError, InvalidToken):
            return None

    @classmethod
    def revoke_refresh_token(cls, refresh_token: str) -> bool:
        """
        撤销刷新令牌。
        """
        try:
            # 撤销数据库中的令牌记录
            token_obj = RefreshToken.objects.get(token=refresh_token)
            token_obj.revoke()

            # 如果启用了黑名单，也将令牌加入黑名单
            if settings.NINJA_JWT.get("BLACKLIST_AFTER_ROTATION", False):
                try:
                    refresh = NinjaRefreshToken(refresh_token)
                    refresh.blacklist()
                except (TokenError, InvalidToken):
                    pass  # 令牌可能已经无效

            return True
        except RefreshToken.DoesNotExist:
            return False

    @classmethod
    def authenticate_token(cls, token: str) -> Optional[User]:
        """
        使用访问令牌验证用户。
        """
        try:
            from ninja_jwt.tokens import AccessToken

            # 验证访问令牌
            access_token = AccessToken(token)
            user_id = access_token.get("user_id")

            if user_id:
                user = User.objects.get(id=user_id, is_active=True)
                return user

        except (TokenError, InvalidToken, User.DoesNotExist):
            pass

        return None

    @classmethod
    def _is_rate_limited(cls, email: str, ip_address: str) -> bool:
        """
        Check if user/IP is rate limited.
        """
        # Check failed attempts in last 15 minutes
        recent_attempts = LoginAttempt.objects.filter(
            email=email,
            success=False,
            attempted_at__gte=timezone.now() - timedelta(minutes=15),
        ).count()

        if recent_attempts >= 5:
            return True

        # Check IP-based rate limiting
        if ip_address:
            ip_attempts = LoginAttempt.objects.filter(
                ip_address=ip_address,
                success=False,
                attempted_at__gte=timezone.now() - timedelta(minutes=15),
            ).count()

            if ip_attempts >= 10:
                return True

        return False

    @classmethod
    def _log_login_attempt(
        cls,
        email: str,
        ip_address: str,
        user_agent: str,
        success: bool,
        failure_reason: str = None,
    ):
        """
        Log login attempt.
        """
        LoginAttempt.objects.create(
            email=email,
            ip_address=ip_address,
            user_agent=user_agent or "",
            success=success,
            failure_reason=failure_reason or "",
        )

    @classmethod
    def _clear_failed_attempts(cls, email: str, ip_address: str):
        """
        Clear failed login attempts after successful login.
        """
        # Clear email-based attempts
        LoginAttempt.objects.filter(email=email, success=False).delete()

        # Clear IP-based attempts
        if ip_address:
            LoginAttempt.objects.filter(ip_address=ip_address, success=False).delete()
