"""
Staging settings for django_ninja_template project.
"""
from .production import *

# Debug can be enabled in staging for troubleshooting
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

# Staging-specific allowed hosts
ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'staging.example.com').split(',')

# Logging for staging (more verbose than production)
LOGGING['handlers']['console']['level'] = 'INFO'
LOGGING['loggers']['django']['level'] = 'INFO'

# Email configuration for staging (can use different settings)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Security settings for staging (slightly relaxed)
SECURE_SSL_REDIRECT = os.getenv('SECURE_SSL_REDIRECT', 'False').lower() == 'true'

# Sentry configuration for staging
SENTRY_DSN = os.getenv('SENTRY_DSN')
if SENTRY_DSN:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration
    from sentry_sdk.integrations.celery import CeleryIntegration
    
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[
            DjangoIntegration(auto_enabling=True),
            CeleryIntegration(monitor_beat_tasks=True),
        ],
        traces_sample_rate=0.5,  # Higher sampling rate for staging
        send_default_pii=True,
        environment='staging',
    )
