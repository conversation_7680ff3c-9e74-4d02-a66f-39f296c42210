"""
使用 Django Ninja 的用户 API 端点。
"""
from typing import List
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.core.paginator import Paginator
from ninja import Router, Query
from ninja.errors import HttpError
from ninja_jwt.authentication import JWTAuth

from .schemas import (
    UserResponse, UserUpdateRequest, UserProfileResponse,
    UserProfileUpdateRequest, UserListResponse
)
from .models import UserProfile

User = get_user_model()
users_router = Router()



@users_router.get("/me", response=UserResponse, auth=JWTAuth())
def get_current_user(request: HttpRequest):
    """
    获取当前认证用户。
    """
    return UserResponse.from_orm(request.auth)


@users_router.put("/me", response=UserResponse, auth=JWTAuth())
def update_current_user(request: HttpRequest, data: UserUpdateRequest):
    """
    更新当前认证用户。
    """
    user = request.auth
    
    # Update user fields
    update_fields = []
    for field, value in data.dict(exclude_unset=True).items():
        if hasattr(user, field) and value is not None:
            setattr(user, field, value)
            update_fields.append(field)
    
    if update_fields:
        user.save(update_fields=update_fields)
    
    return UserResponse.from_orm(user)


@users_router.get("/me/profile", response=UserProfileResponse, auth=JWTAuth())
def get_current_user_profile(request: HttpRequest):
    """
    获取当前认证用户的详细资料。
    """
    user = request.auth
    
    # Get or create profile
    profile, created = UserProfile.objects.get_or_create(user=user)
    
    profile_data = {
        "website": profile.website,
        "github": profile.github,
        "linkedin": profile.linkedin,
        "twitter": profile.twitter,
        "company": profile.company,
        "job_title": profile.job_title,
        "location": profile.location,
        "email_notifications": profile.email_notifications,
        "push_notifications": profile.push_notifications,
        "marketing_emails": profile.marketing_emails,
        "profile_visibility": profile.profile_visibility,
    }
    
    return UserProfileResponse(
        user=UserResponse.from_orm(user),
        profile=profile_data
    )


@users_router.put("/me/profile", response=UserProfileResponse, auth=JWTAuth())
def update_current_user_profile(request: HttpRequest, data: UserProfileUpdateRequest):
    """
    更新当前认证用户的资料。
    """
    user = request.auth
    
    # Get or create profile
    profile, created = UserProfile.objects.get_or_create(user=user)
    
    # Update profile fields
    update_fields = []
    for field, value in data.dict(exclude_unset=True).items():
        if hasattr(profile, field) and value is not None:
            setattr(profile, field, value)
            update_fields.append(field)
    
    if update_fields:
        profile.save(update_fields=update_fields)
    
    profile_data = {
        "website": profile.website,
        "github": profile.github,
        "linkedin": profile.linkedin,
        "twitter": profile.twitter,
        "company": profile.company,
        "job_title": profile.job_title,
        "location": profile.location,
        "email_notifications": profile.email_notifications,
        "push_notifications": profile.push_notifications,
        "marketing_emails": profile.marketing_emails,
        "profile_visibility": profile.profile_visibility,
    }
    
    return UserProfileResponse(
        user=UserResponse.from_orm(user),
        profile=profile_data
    )


@users_router.get("/", response=UserListResponse, auth=JWTAuth())
def list_users(request: HttpRequest, page: int = Query(1, ge=1), per_page: int = Query(20, ge=1, le=100)):
    """
    分页列出用户。
    """
    # Only allow access to public profiles or admin users
    queryset = User.objects.filter(is_active=True)
    
    # Apply pagination
    paginator = Paginator(queryset, per_page)
    page_obj = paginator.get_page(page)
    
    users = [UserResponse.from_orm(user) for user in page_obj.object_list]
    
    return UserListResponse(
        users=users,
        total=paginator.count,
        page=page,
        per_page=per_page,
        pages=paginator.num_pages
    )


@users_router.get("/{user_id}", response=UserResponse, auth=JWTAuth())
def get_user(request: HttpRequest, user_id: int):
    """
    根据ID获取用户。
    """
    try:
        user = User.objects.get(id=user_id, is_active=True)
        
        # Check if profile is public or user is viewing their own profile
        if user.id != request.auth.id:
            profile = getattr(user, 'profile', None)
            if profile and profile.profile_visibility != 'public':
                raise HttpError(403, "User profile is private")
        
        return UserResponse.from_orm(user)
        
    except User.DoesNotExist:
        raise HttpError(404, "User not found")
