#!/usr/bin/env python
"""
Django Ninja 脚手架项目 API 功能测试脚本
测试所有应用的核心API功能

使用方法：
uv run python tmp_rovodev_api_test.py

注意：需要先运行数据库迁移和启动开发服务器
"""

import json
import time
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

class APITester:
    """API 测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.access_token = None
        self.refresh_token = None
        self.test_user_email = None
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if data and not success:
            print(f"   Data: {data}")
    
    def make_request(self, method: str, endpoint: str, data: Dict = None, 
                    headers: Dict = None, auth_required: bool = True) -> Dict:
        """发送HTTP请求"""
        url = f"{self.base_url}/api{endpoint}"
        
        # 设置默认headers
        request_headers = {"Content-Type": "application/json"}
        if headers:
            request_headers.update(headers)
            
        # 添加认证token
        if auth_required and self.access_token:
            request_headers["Authorization"] = f"Bearer {self.access_token}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=request_headers, params=data, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, headers=request_headers, 
                                       json=data if data else {}, timeout=10)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=request_headers, 
                                      json=data if data else {}, timeout=10)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=request_headers, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
                
            try:
                response_data = response.json() if response.content else {}
            except json.JSONDecodeError:
                response_data = {"raw_response": response.text}
                
            return {
                "status_code": response.status_code,
                "data": response_data,
                "success": 200 <= response.status_code < 300
            }
        except requests.exceptions.RequestException as e:
            return {
                "status_code": 0,
                "data": {"error": str(e)},
                "success": False
            }

    def test_core_api(self):
        """测试 Core 应用 API"""
        print("\n🔧 测试 Core 应用...")
        
        # 测试健康检查
        result = self.make_request("GET", "/core/health", auth_required=False)
        self.log_test(
            "Core - Health Check",
            result["success"] and result["data"].get("status") == "healthy",
            f"状态码: {result['status_code']}, 健康状态: {result['data'].get('status', 'unknown')}",
            result["data"]
        )
        
        # 测试系统信息
        result = self.make_request("GET", "/core/info", auth_required=False)
        self.log_test(
            "Core - System Info",
            result["success"] and "application" in result["data"],
            f"状态码: {result['status_code']}, 包含应用信息: {'application' in result['data']}",
            result["data"].get("application", {})
        )
        
        # 测试 ping
        result = self.make_request("GET", "/core/ping", auth_required=False)
        self.log_test(
            "Core - Ping",
            result["success"] and result["data"].get("message") == "pong",
            f"状态码: {result['status_code']}, 响应: {result['data'].get('message', 'none')}",
            result["data"]
        )

    def test_authentication_api(self):
        """测试 Authentication 应用 API"""
        print("\n🔐 测试 Authentication 应用...")
        
        # 生成唯一的测试用户邮箱
        unique_id = str(uuid.uuid4())[:8]
        self.test_user_email = f"test_{unique_id}@example.com"
        
        # 测试用户注册
        register_data = {
            "email": self.test_user_email,
            "password": "testpassword123",
            "username": f"testuser_{unique_id}",
            "first_name": "Test",
            "last_name": "User"
        }
        
        result = self.make_request("POST", "/auth/register", register_data, auth_required=False)
        self.log_test(
            "Auth - User Registration",
            result["success"],
            f"状态码: {result['status_code']}, 消息: {result['data'].get('message', result['data'].get('detail', 'none'))}",
            result["data"]
        )
        
        # 测试用户登录
        login_data = {
            "email": self.test_user_email,
            "password": "testpassword123",
            "device_name": "test_device"
        }
        
        result = self.make_request("POST", "/auth/login", login_data, auth_required=False)
        self.log_test(
            "Auth - User Login",
            result["success"] and "access_token" in result["data"],
            f"状态码: {result['status_code']}, 获得token: {'access_token' in result['data']}",
            {"user": result["data"].get("user", {})} if result["success"] else result["data"]
        )
        
        if result["success"]:
            self.access_token = result["data"].get("access_token")
            self.refresh_token = result["data"].get("refresh_token")
        
        # 测试获取当前用户信息
        if self.access_token:
            result = self.make_request("GET", "/auth/me")
            self.log_test(
                "Auth - Get Current User",
                result["success"] and "id" in result["data"],
                f"状态码: {result['status_code']}, 用户ID: {result['data'].get('id', 'none')}",
                result["data"] if result["success"] else result["data"]
            )
        
        # 测试token验证
        if self.access_token:
            result = self.make_request("GET", f"/auth/verify-token?token={self.access_token}", 
                                     auth_required=False)
            self.log_test(
                "Auth - Token Verification",
                result["success"] and result["data"].get("valid") == True,
                f"状态码: {result['status_code']}, Token有效: {result['data'].get('valid', False)}",
                result["data"]
            )

    def test_users_api(self):
        """测试 Users 应用 API"""
        print("\n👥 测试 Users 应用...")
        
        if not self.access_token:
            self.log_test("Users - Skip Tests", False, "需要先登录获取token")
            return
        
        # 测试获取当前用户
        result = self.make_request("GET", "/users/me")
        self.log_test(
            "Users - Get Current User",
            result["success"] and "id" in result["data"],
            f"状态码: {result['status_code']}, 用户信息完整: {'email' in result['data']}",
            {"id": result["data"].get("id"), "email": result["data"].get("email")} if result["success"] else result["data"]
        )
        
        # 测试更新当前用户
        update_data = {
            "first_name": "Updated",
            "bio": "Updated bio for testing"
        }
        result = self.make_request("PUT", "/users/me", update_data)
        self.log_test(
            "Users - Update Current User",
            result["success"],
            f"状态码: {result['status_code']}, 更新成功: {result['success']}",
            result["data"]
        )
        
        # 测试获取用户资料
        result = self.make_request("GET", "/users/me/profile")
        self.log_test(
            "Users - Get User Profile",
            result["success"] and "user" in result["data"] and "profile" in result["data"],
            f"状态码: {result['status_code']}, 包含资料: {'profile' in result['data']}",
            result["data"].get("profile", {}) if result["success"] else result["data"]
        )
        
        # 测试更新用户资料
        profile_data = {
            "company": "Test Company",
            "job_title": "Test Developer",
            "location": "Test City"
        }
        result = self.make_request("PUT", "/users/me/profile", profile_data)
        self.log_test(
            "Users - Update User Profile",
            result["success"],
            f"状态码: {result['status_code']}, 更新成功: {result['success']}",
            result["data"]
        )
        
        # 测试用户列表
        result = self.make_request("GET", "/users/?page=1&per_page=10")
        self.log_test(
            "Users - List Users",
            result["success"] and "users" in result["data"],
            f"状态码: {result['status_code']}, 用户数量: {len(result['data'].get('users', []))}",
            {"total": result["data"].get("total", 0)} if result["success"] else result["data"]
        )

    def test_permissions_api(self):
        """测试 Permissions 应用 API"""
        print("\n🔒 测试 Permissions 应用...")
        
        if not self.access_token:
            self.log_test("Permissions - Skip Tests", False, "需要先登录获取token")
            return
        
        # 测试获取权限列表（可能需要特定权限）
        result = self.make_request("GET", "/permissions/permissions")
        self.log_test(
            "Permissions - List Permissions",
            result["status_code"] in [200, 403],  # 200成功或403权限不足都是正常的
            f"状态码: {result['status_code']}, 响应: {result['data'].get('detail', 'success') if result['status_code'] == 403 else 'success'}",
            result["data"] if result["status_code"] == 200 else None
        )
        
        # 测试获取角色列表
        result = self.make_request("GET", "/permissions/roles")
        self.log_test(
            "Permissions - List Roles",
            result["status_code"] in [200, 403],
            f"状态码: {result['status_code']}, 响应: {result['data'].get('detail', 'success') if result['status_code'] == 403 else 'success'}",
            result["data"] if result["status_code"] == 200 else None
        )
        
        # 测试获取当前用户权限
        result = self.make_request("GET", "/permissions/my-permissions")
        self.log_test(
            "Permissions - Get My Permissions",
            result["success"] or result["status_code"] == 403,
            f"状态码: {result['status_code']}, 权限检查: {'permissions' in result['data'] if result['success'] else '需要权限'}",
            result["data"] if result["success"] else None
        )

    def test_storage_api(self):
        """测试 Storage 应用 API"""
        print("\n📁 测试 Storage 应用...")
        
        if not self.access_token:
            self.log_test("Storage - Skip Tests", False, "需要先登录获取token")
            return
        
        # 测试获取文件列表
        result = self.make_request("GET", "/storage/files/?page=1&per_page=10")
        self.log_test(
            "Storage - List Files",
            result["success"] and "files" in result["data"],
            f"状态码: {result['status_code']}, 文件数量: {len(result['data'].get('files', []))}",
            {"total": result["data"].get("total", 0)} if result["success"] else result["data"]
        )
        
        # 测试获取文件统计
        result = self.make_request("GET", "/storage/stats")
        self.log_test(
            "Storage - Get File Stats",
            result["success"] and "total_files" in result["data"],
            f"状态码: {result['status_code']}, 总文件数: {result['data'].get('total_files', 0)}",
            result["data"] if result["success"] else result["data"]
        )
        
        # 测试获取文件分类
        result = self.make_request("GET", "/storage/categories")
        self.log_test(
            "Storage - List Categories",
            result["success"],
            f"状态码: {result['status_code']}, 分类数量: {len(result['data']) if isinstance(result['data'], list) else 0}",
            result["data"] if isinstance(result["data"], list) else result["data"]
        )

    def test_i18n_api(self):
        """测试 I18n 应用 API"""
        print("\n🌐 测试 I18n 应用...")
        
        # 测试获取语言列表（不需要认证）
        result = self.make_request("GET", "/i18n/languages", auth_required=False)
        self.log_test(
            "I18n - List Languages",
            result["success"],
            f"状态码: {result['status_code']}, 语言数量: {len(result['data']) if isinstance(result['data'], list) else 0}",
            result["data"] if isinstance(result["data"], list) else result["data"]
        )
        
        # 测试获取翻译
        result = self.make_request("GET", "/i18n/translations/en", auth_required=False)
        self.log_test(
            "I18n - Get Translations",
            result["success"] or result["status_code"] == 404,  # 可能没有翻译数据
            f"状态码: {result['status_code']}, 翻译数据: {'translations' in result['data'] if result['success'] else '无数据'}",
            result["data"] if result["success"] else None
        )
        
        # 测试获取翻译统计
        result = self.make_request("GET", "/i18n/stats", auth_required=False)
        self.log_test(
            "I18n - Get Translation Stats",
            result["success"] or result["status_code"] == 404,
            f"状态码: {result['status_code']}, 统计信息: {'total_keys' in result['data'] if result['success'] else '无数据'}",
            result["data"] if result["success"] else None
        )

    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        if self.access_token and self.test_user_email:
            # 尝试删除测试用户（如果API支持）
            result = self.make_request("DELETE", "/auth/delete-account")
            if result["success"]:
                print(f"✅ 已删除测试用户: {self.test_user_email}")
            else:
                print(f"ℹ️  测试用户需要手动清理: {self.test_user_email}")

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始 Django Ninja 脚手架项目 API 功能测试")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行各个应用的测试
        self.test_core_api()
        self.test_authentication_api()
        self.test_users_api()
        self.test_permissions_api()
        self.test_storage_api()
        self.test_i18n_api()
        
        end_time = time.time()
        
        # 生成测试报告
        self.generate_report(end_time - start_time)
        
        # 清理测试数据
        self.cleanup_test_data()

    def generate_report(self, duration: float):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        print(f"测试耗时: {duration:.2f}秒")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        # 按应用分组显示结果
        print("\n📋 按应用分组的测试结果:")
        apps = {}
        for result in self.test_results:
            app_name = result["test"].split(" - ")[0]
            if app_name not in apps:
                apps[app_name] = {"total": 0, "passed": 0}
            apps[app_name]["total"] += 1
            if result["success"]:
                apps[app_name]["passed"] += 1
        
        for app_name, stats in apps.items():
            success_rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
            print(f"  {app_name}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")
        
        # 保存详细报告到文件
        report_file = f"tmp_rovodev_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "success_rate": passed_tests/total_tests*100,
                    "duration": duration,
                    "timestamp": datetime.now().isoformat()
                },
                "results": self.test_results,
                "apps": apps
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")


def main():
    """主函数"""
    print("Django Ninja 脚手架项目 API 功能测试")
    print("注意: 请确保开发服务器正在运行 (uv run python manage.py runserver)")
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://127.0.0.1:8000/api/core/ping", timeout=5)
        if response.status_code != 200:
            print("❌ 无法连接到开发服务器，请确保服务器正在运行")
            print("请运行: uv run python manage.py runserver")
            return
    except requests.exceptions.RequestException:
        print("❌ 无法连接到开发服务器，请先运行: uv run python manage.py runserver")
        return
    
    # 运行测试
    tester = APITester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()