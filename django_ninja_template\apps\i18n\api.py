"""
使用 Django Ninja 的国际化 API 端点。
"""
from typing import List, Dict
from django.http import HttpRequest
from ninja import Router
from ninja.errors import HttpError
from ninja_jwt.authentication import J<PERSON>TAuth

from .models import Language, Translation
from .schemas import (
    LanguageResponse, TranslationResponse, TranslationRequest,
    TranslationBatchRequest, TranslationProgressResponse, LanguageStatsResponse
)
from .services import I18nService
from apps.permissions.decorators import require_permissions

i18n_router = Router()


@i18n_router.get("/languages", response=List[LanguageResponse], auth=None)
def list_languages(request: HttpRequest):
    """
    Get list of supported languages.
    """
    languages = I18nService.get_supported_languages()
    return [LanguageResponse(**lang) for lang in languages]


@i18n_router.get("/translations/{language_code}", response=Dict[str, str], auth=None)
def get_translations(request: HttpRequest, language_code: str):
    """
    Get all translations for a language.
    """
    translations = I18nService.get_translations_for_language(language_code)
    return translations


@i18n_router.get("/translation/{language_code}/{key}", response=str, auth=None)
def get_translation(request: HttpRequest, language_code: str, key: str, context: str = ""):
    """
    Get a specific translation.
    """
    translation = I18nService.get_translation(key, language_code, context)
    return translation


@i18n_router.post("/translation", response=TranslationResponse)
@require_permissions("i18n.manage_translations")
def create_translation(request: HttpRequest, data: TranslationRequest):
    """
    Create or update a translation.
    """
    success = I18nService.set_translation(
        key=data.key,
        language_code=data.language_code,
        value=data.value,
        context=data.context,
        is_approved=True
    )
    
    if not success:
        raise HttpError(400, "Invalid language code")
    
    # Return the created translation
    try:
        language = Language.objects.get(code=data.language_code)
        translation = Translation.objects.get(
            key=data.key,
            language=language,
            context=data.context
        )
        return TranslationResponse(
            key=translation.key,
            value=translation.value,
            context=translation.context,
            language_code=language.code,
            is_approved=translation.is_approved
        )
    except (Language.DoesNotExist, Translation.DoesNotExist):
        raise HttpError(404, "Translation not found")


@i18n_router.post("/translations/batch")
@require_permissions("i18n.manage_translations")
def create_translations_batch(request: HttpRequest, data: TranslationBatchRequest):
    """
    Create or update multiple translations at once.
    """
    try:
        language = Language.objects.get(code=data.language_code, is_active=True)
    except Language.DoesNotExist:
        raise HttpError(400, "Invalid language code")
    
    created_count = 0
    updated_count = 0
    
    for key, value in data.translations.items():
        translation, created = Translation.objects.update_or_create(
            key=key,
            language=language,
            context="",  # Default context for batch operations
            defaults={
                'value': value,
                'is_approved': True,
                'is_fuzzy': False
            }
        )
        
        if created:
            created_count += 1
        else:
            updated_count += 1
    
    # Clear cache for this language
    I18nService.clear_translation_cache(data.language_code)
    
    return {
        "message": f"Processed {len(data.translations)} translations",
        "created": created_count,
        "updated": updated_count
    }


@i18n_router.get("/progress/{language_code}", response=TranslationProgressResponse)
@require_permissions("i18n.view_progress")
def get_translation_progress(request: HttpRequest, language_code: str):
    """
    Get translation progress for a language.
    """
    progress = I18nService.get_translation_progress(language_code)
    
    if not progress:
        raise HttpError(404, "Language not found")
    
    return TranslationProgressResponse(**progress)


@i18n_router.get("/missing/{language_code}", response=List[str])
@require_permissions("i18n.view_progress")
def get_missing_translations(request: HttpRequest, language_code: str):
    """
    Get list of missing translation keys for a language.
    """
    missing_keys = I18nService.get_missing_translations(language_code)
    return missing_keys


@i18n_router.get("/stats", response=LanguageStatsResponse)
@require_permissions("i18n.view_stats")
def get_language_stats(request: HttpRequest):
    """
    Get translation statistics for all languages.
    """
    languages = I18nService.get_supported_languages()
    language_stats = []
    
    total_keys = 0
    total_translations = 0
    
    for lang in languages:
        progress = I18nService.get_translation_progress(lang['code'])
        if progress:
            language_stats.append(TranslationProgressResponse(**progress))
            if lang['is_default']:
                total_keys = progress['total_count']
            total_translations += progress['translated_count']
    
    return LanguageStatsResponse(
        languages=language_stats,
        total_keys=total_keys,
        total_translations=total_translations
    )


@i18n_router.post("/cache/clear")
@require_permissions("i18n.manage_cache")
def clear_translation_cache(request: HttpRequest, language_code: str = None):
    """
    Clear translation cache.
    """
    I18nService.clear_translation_cache(language_code)
    
    if language_code:
        return {"message": f"Cache cleared for language: {language_code}"}
    else:
        return {"message": "All translation cache cleared"}


@i18n_router.post("/language/activate")
def activate_language(request: HttpRequest, language_code: str):
    """
    Activate a language for the current session.
    """
    success = I18nService.activate_language(language_code)
    
    if not success:
        raise HttpError(400, "Invalid language code")
    
    # Store in session
    request.session['language_code'] = language_code
    
    return {"message": f"Language activated: {language_code}"}
