"""
示例模型文件 - 博客应用
应用名称: blog
"""

from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Category(models.Model):
    """文章分类"""
    name = models.CharField(max_length=100, verbose_name="分类名称")
    slug = models.SlugField(max_length=100, unique=True, verbose_name="URL别名")
    description = models.TextField(blank=True, verbose_name="分类描述")
    
    class Meta:
        verbose_name = "文章分类"
        verbose_name_plural = "文章分类"

class Post(models.Model):
    """博客文章"""
    title = models.CharField(max_length=200, verbose_name="标题")
    slug = models.SlugField(max_length=200, unique=True, verbose_name="URL别名")
    content = models.TextField(verbose_name="内容")
    excerpt = models.TextField(max_length=500, blank=True, verbose_name="摘要")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="分类")
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="作者")
    is_published = models.BooleanField(default=False, verbose_name="是否发布")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "博客文章"
        verbose_name_plural = "博客文章"
        ordering = ['-created_at']

class Comment(models.Model):
    """评论"""
    post = models.ForeignKey(Post, on_delete=models.CASCADE, verbose_name="文章")
    author_name = models.CharField(max_length=100, verbose_name="评论者姓名")
    author_email = models.EmailField(verbose_name="评论者邮箱")
    content = models.TextField(verbose_name="评论内容")
    is_approved = models.BooleanField(default=False, verbose_name="是否审核通过")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    class Meta:
        verbose_name = "评论"
        verbose_name_plural = "评论"
        ordering = ['-created_at']