"""
RBAC (Role-Based Access Control) models.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel

User = get_user_model()


class Permission(BaseModel):
    """
    Custom permission model for fine-grained access control.
    """
    name = models.CharField(_('name'), max_length=255, unique=True)
    codename = models.CharField(_('codename'), max_length=100, unique=True)
    description = models.TextField(_('description'), blank=True)
    resource = models.CharField(_('resource'), max_length=100)
    action = models.CharField(_('action'), max_length=50)
    
    class Meta:
        verbose_name = _('Permission')
        verbose_name_plural = _('Permissions')
        db_table = 'permissions_permission'
        indexes = [
            models.Index(fields=['codename']),
            models.Index(fields=['resource', 'action']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.codename})"


class Role(BaseModel):
    """
    Role model for grouping permissions.
    """
    name = models.CharField(_('name'), max_length=255, unique=True)
    description = models.TextField(_('description'), blank=True)
    permissions = models.ManyToManyField(
        Permission,
        through='RolePermission',
        related_name='roles'
    )
    is_system_role = models.BooleanField(_('is system role'), default=False)
    is_active = models.BooleanField(_('is active'), default=True)
    
    class Meta:
        verbose_name = _('Role')
        verbose_name_plural = _('Roles')
        db_table = 'permissions_role'
    
    def __str__(self):
        return self.name
    
    def add_permission(self, permission, granted_by=None):
        """Add a permission to this role."""
        role_permission, created = RolePermission.objects.get_or_create(
            role=self,
            permission=permission,
            defaults={'granted_by': granted_by}
        )
        return role_permission
    
    def remove_permission(self, permission):
        """Remove a permission from this role."""
        RolePermission.objects.filter(role=self, permission=permission).delete()
    
    def has_permission(self, permission_codename):
        """Check if role has a specific permission."""
        return self.permissions.filter(codename=permission_codename).exists()


class RolePermission(BaseModel):
    """
    Through model for Role-Permission relationship with additional metadata.
    """
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_permissions'
    )
    
    class Meta:
        verbose_name = _('Role Permission')
        verbose_name_plural = _('Role Permissions')
        db_table = 'permissions_role_permission'
        unique_together = ('role', 'permission')
    
    def __str__(self):
        return f"{self.role.name} - {self.permission.name}"


class UserRole(BaseModel):
    """
    Model to assign roles to users.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='user_roles')
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_roles'
    )
    is_active = models.BooleanField(_('is active'), default=True)
    expires_at = models.DateTimeField(_('expires at'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('User Role')
        verbose_name_plural = _('User Roles')
        db_table = 'permissions_user_role'
        unique_together = ('user', 'role')
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.role.name}"
    
    def is_expired(self):
        """Check if the role assignment is expired."""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False


class UserPermission(BaseModel):
    """
    Model for direct user permissions (overrides role permissions).
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_user_permissions')
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    granted = models.BooleanField(_('granted'), default=True)  # True = grant, False = deny
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_user_permissions'
    )
    expires_at = models.DateTimeField(_('expires at'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('User Permission')
        verbose_name_plural = _('User Permissions')
        db_table = 'permissions_user_permission'
        unique_together = ('user', 'permission')
        indexes = [
            models.Index(fields=['user', 'granted']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        status = 'Granted' if self.granted else 'Denied'
        return f"{self.user.email} - {self.permission.name} ({status})"
    
    def is_expired(self):
        """Check if the permission is expired."""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
