"""
Core services for caching and utilities.
"""

import json
import hashlib
from typing import Any, Optional, Union, Dict
from django.core.cache import cache
from django.conf import settings
from django.core.serializers.json import DjangoJSONEncoder


class CacheService:
    """
    Service class for advanced caching operations.
    """

    DEFAULT_TIMEOUT = 300  # 5 minutes

    @classmethod
    def get(cls, key: str, default: Any = None) -> Any:
        """
        Get value from cache.
        """
        try:
            return cache.get(key, default)
        except Exception:
            return default

    @classmethod
    def set(cls, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """
        Set value in cache.
        """
        try:
            timeout = timeout or cls.DEFAULT_TIMEOUT
            return cache.set(key, value, timeout)
        except Exception:
            return False

    @classmethod
    def delete(cls, key: str) -> bool:
        """
        Delete value from cache.
        """
        try:
            return cache.delete(key)
        except Exception:
            return False

    @classmethod
    def get_or_set(cls, key: str, callable_func, timeout: Optional[int] = None) -> Any:
        """
        Get value from cache or set it using the callable function.
        """
        try:
            timeout = timeout or cls.DEFAULT_TIMEOUT
            return cache.get_or_set(key, callable_func, timeout)
        except Exception:
            # If cache fails, call the function directly
            return callable_func()

    @classmethod
    def get_many(cls, keys: list) -> Dict[str, Any]:
        """
        Get multiple values from cache.
        """
        try:
            return cache.get_many(keys)
        except Exception:
            return {}

    @classmethod
    def set_many(cls, data: Dict[str, Any], timeout: Optional[int] = None) -> bool:
        """
        Set multiple values in cache.
        """
        try:
            timeout = timeout or cls.DEFAULT_TIMEOUT
            cache.set_many(data, timeout)
            return True
        except Exception:
            return False

    @classmethod
    def delete_many(cls, keys: list) -> bool:
        """
        Delete multiple values from cache.
        """
        try:
            cache.delete_many(keys)
            return True
        except Exception:
            return False

    @classmethod
    def clear(cls) -> bool:
        """
        Clear all cache.
        """
        try:
            cache.clear()
            return True
        except Exception:
            return False

    @classmethod
    def make_key(cls, *args, **kwargs) -> str:
        """
        Generate a cache key from arguments.
        """
        # Create a string representation of all arguments
        key_parts = []

        # Add positional arguments
        for arg in args:
            if hasattr(arg, "pk"):
                key_parts.append(f"{arg.__class__.__name__}_{arg.pk}")
            else:
                key_parts.append(str(arg))

        # Add keyword arguments
        for k, v in sorted(kwargs.items()):
            if hasattr(v, "pk"):
                key_parts.append(f"{k}_{v.__class__.__name__}_{v.pk}")
            else:
                key_parts.append(f"{k}_{v}")

        # Join and hash if too long
        key = "_".join(key_parts)
        if len(key) > 200:  # Redis key length limit
            key = hashlib.md5(key.encode()).hexdigest()

        return key

    @classmethod
    def cache_model_instance(cls, instance, timeout: Optional[int] = None) -> bool:
        """
        Cache a model instance.
        """
        if not hasattr(instance, "pk") or not instance.pk:
            return False

        key = f"{instance.__class__.__name__}_{instance.pk}"
        return cls.set(key, instance, timeout)

    @classmethod
    def get_cached_model_instance(cls, model_class, pk: Union[int, str]):
        """
        Get a cached model instance.
        """
        key = f"{model_class.__name__}_{pk}"
        return cls.get(key)

    @classmethod
    def invalidate_model_cache(cls, instance) -> bool:
        """
        Invalidate cache for a model instance.
        """
        if not hasattr(instance, "pk") or not instance.pk:
            return False

        key = f"{instance.__class__.__name__}_{instance.pk}"
        return cls.delete(key)


class ConfigService:
    """
    Service class for system configuration management.
    """

    @classmethod
    def get_config(cls, key: str, default: Any = None) -> Any:
        """
        Get configuration value with caching.
        """
        cache_key = f"config_{key}"

        # Try to get from cache first
        value = CacheService.get(cache_key)
        if value is not None:
            return value

        # Get from database
        from .models import SystemConfiguration

        try:
            config = SystemConfiguration.objects.get(key=key, is_active=True)
            value = cls._parse_config_value(config.value)

            # Cache for 1 hour
            CacheService.set(cache_key, value, 3600)
            return value
        except SystemConfiguration.DoesNotExist:
            return default

    @classmethod
    def set_config(cls, key: str, value: Any, description: str = "") -> bool:
        """
        Set configuration value.
        """
        from .models import SystemConfiguration

        try:
            # Convert value to string for storage
            str_value = cls._serialize_config_value(value)

            # Update or create configuration
            config, created = SystemConfiguration.objects.update_or_create(
                key=key,
                defaults={
                    "value": str_value,
                    "description": description,
                    "is_active": True,
                },
            )

            # Invalidate cache
            cache_key = f"config_{key}"
            CacheService.delete(cache_key)

            return True
        except Exception:
            return False

    @classmethod
    def delete_config(cls, key: str) -> bool:
        """
        Delete configuration value.
        """
        from .models import SystemConfiguration

        try:
            SystemConfiguration.objects.filter(key=key).delete()

            # Invalidate cache
            cache_key = f"config_{key}"
            CacheService.delete(cache_key)

            return True
        except Exception:
            return False

    @classmethod
    def _parse_config_value(cls, value: str) -> Any:
        """
        Parse configuration value from string.
        """
        # Try to parse as JSON first
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            # Return as string if not valid JSON
            return value

    @classmethod
    def _serialize_config_value(cls, value: Any) -> str:
        """
        Serialize configuration value to string.
        """
        if isinstance(value, str):
            return value

        try:
            return json.dumps(value, cls=DjangoJSONEncoder)
        except (TypeError, ValueError):
            return str(value)
