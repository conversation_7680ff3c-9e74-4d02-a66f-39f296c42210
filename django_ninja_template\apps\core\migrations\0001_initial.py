# Generated by Django 5.2.4 on 2025-07-14 08:26

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("action", models.CharField(max_length=100, verbose_name="action")),
                (
                    "resource_type",
                    models.Char<PERSON>ield(max_length=100, verbose_name="resource type"),
                ),
                (
                    "resource_id",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="resource ID"
                    ),
                ),
                ("details", models.<PERSON><PERSON><PERSON>ield(default=dict, verbose_name="details")),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP address"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="user agent")),
            ],
            options={
                "verbose_name": "Audit Log",
                "verbose_name_plural": "Audit Logs",
                "db_table": "core_audit_logs",
            },
        ),
        migrations.CreateModel(
            name="SystemConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "key",
                    models.CharField(max_length=255, unique=True, verbose_name="key"),
                ),
                ("value", models.TextField(verbose_name="value")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="description"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="is active"),
                ),
            ],
            options={
                "verbose_name": "System Configuration",
                "verbose_name_plural": "System Configurations",
                "db_table": "core_system_configurations",
            },
        ),
    ]
