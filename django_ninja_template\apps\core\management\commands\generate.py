"""
代码生成 CLI 工具
快速生成模块、模型、API 等代码
"""

from django.core.management.base import BaseCommand, CommandError
from django.template import Template, Context
from django.conf import settings
import os
from pathlib import Path
import re


class Command(BaseCommand):
    help = "代码生成工具 - 快速生成模块、模型、API 等代码"

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest="command", help="生成类型")

        # 生成模块
        module_parser = subparsers.add_parser("module", help="生成完整模块")
        module_parser.add_argument("name", type=str, help="模块名称")
        module_parser.add_argument(
            "--fields", type=str, help="字段定义 (name:str,age:int,is_active:bool)"
        )
        module_parser.add_argument(
            "--permissions", type=str, help="权限前缀", default=None
        )
        module_parser.add_argument(
            "--admin", action="store_true", help="生成 Admin 配置"
        )
        module_parser.add_argument(
            "--api", action="store_true", help="生成 API 接口", default=True
        )

        # 生成模型
        model_parser = subparsers.add_parser("model", help="生成模型")
        model_parser.add_argument("app", type=str, help="应用名称")
        model_parser.add_argument("name", type=str, help="模型名称")
        model_parser.add_argument("--fields", type=str, help="字段定义")

        # 生成 API
        api_parser = subparsers.add_parser("api", help="生成 API")
        api_parser.add_argument("app", type=str, help="应用名称")
        api_parser.add_argument("model", type=str, help="模型名称")
        api_parser.add_argument("--permissions", type=str, help="权限前缀")

        # 生成权限
        permission_parser = subparsers.add_parser("permissions", help="生成权限配置")
        permission_parser.add_argument("app", type=str, help="应用名称")
        permission_parser.add_argument(
            "--models", type=str, help="模型名称列表 (用逗号分隔)"
        )

    def handle(self, *args, **options):
        command = options["command"]

        if command == "module":
            self.generate_module(options)
        elif command == "model":
            self.generate_model(options)
        elif command == "api":
            self.generate_api(options)
        elif command == "permissions":
            self.generate_permissions(options)
        else:
            raise CommandError("未知命令")

    def generate_module(self, options):
        """生成完整模块"""
        name = options["name"]
        fields = self.parse_fields(options.get("fields", ""))
        permissions_prefix = options.get("permissions") or f"{name}.item"

        self.stdout.write(f"正在生成模块: {name}")

        # 创建目录结构
        module_path = Path(f"apps/{name}")
        module_path.mkdir(exist_ok=True)
        (module_path / "migrations").mkdir(exist_ok=True)

        # 生成上下文
        context = {
            "module_name": name,
            "model_name": name.title(),
            "fields": fields,
            "permissions_prefix": permissions_prefix,
            "enable_admin": options.get("admin", True),
            "enable_api": options.get("api", True),
        }

        # 生成文件
        files_to_generate = [
            ("__init__.py", "module/__init__.py.template"),
            ("apps.py", "module/apps.py.template"),
            ("models.py", "module/models.py.template"),
            ("admin.py", "module/admin.py.template"),
            ("migrations/__init__.py", "module/migrations_init.py.template"),
        ]

        if options.get("api", True):
            files_to_generate.extend(
                [
                    ("schemas.py", "module/schemas.py.template"),
                    ("api.py", "module/api.py.template"),
                    ("services.py", "module/services.py.template"),
                ]
            )

        files_to_generate.extend(
            [
                ("permissions.py", "module/permissions.py.template"),
                ("tests.py", "module/tests.py.template"),
            ]
        )

        for file_name, template_name in files_to_generate:
            file_path = module_path / file_name
            self.generate_file_from_template(template_name, file_path, context)
            self.stdout.write(f"  ✓ 生成 {file_name}")

        # 更新 settings
        self.update_installed_apps(name)

        self.stdout.write(self.style.SUCCESS(f"模块 {name} 生成完成！"))
        self.stdout.write("下一步:")
        self.stdout.write(f"  1. python manage.py makemigrations {name}")
        self.stdout.write(f"  2. python manage.py migrate")
        self.stdout.write(
            f"  3. python manage.py auto_generate_permissions {name} --create-roles"
        )
        self.stdout.write(
            f'  4. 在 core/urls.py 中添加路由: api.add_router("/{name}/", {name}_router)'
        )

    def generate_model(self, options):
        """生成模型"""
        app_name = options["app"]
        model_name = options["name"]
        fields = self.parse_fields(options.get("fields", ""))

        context = {
            "app_name": app_name,
            "model_name": model_name,
            "fields": fields,
        }

        model_code = self.render_template("model/model.py.template", context)

        # 追加到 models.py
        models_file = Path(f"apps/{app_name}/models.py")
        if models_file.exists():
            with open(models_file, "a", encoding="utf-8") as f:
                f.write("\n\n" + model_code)
            self.stdout.write(
                self.style.SUCCESS(f"模型 {model_name} 已添加到 {app_name}/models.py")
            )
        else:
            self.stdout.write(self.style.ERROR(f"应用 {app_name} 不存在"))

    def generate_api(self, options):
        """生成 API"""
        app_name = options["app"]
        model_name = options["model"]
        permissions_prefix = (
            options.get("permissions") or f"{app_name}.{model_name.lower()}"
        )

        context = {
            "app_name": app_name,
            "model_name": model_name,
            "permissions_prefix": permissions_prefix,
        }

        # 生成 API 文件
        api_path = Path(f"apps/{app_name}/api.py")
        self.generate_file_from_template("api/api.py.template", api_path, context)

        # 生成 schemas 文件
        schemas_path = Path(f"apps/{app_name}/schemas.py")
        self.generate_file_from_template(
            "api/schemas.py.template", schemas_path, context
        )

        self.stdout.write(self.style.SUCCESS(f"API 接口已生成到 {app_name}/"))

    def generate_permissions(self, options):
        """生成权限配置"""
        app_name = options["app"]
        models = options.get("models", "").split(",") if options.get("models") else []

        context = {
            "app_name": app_name,
            "models": [model.strip() for model in models if model.strip()],
        }

        permissions_path = Path(f"apps/{app_name}/permissions.py")
        self.generate_file_from_template(
            "permissions/permissions.py.template", permissions_path, context
        )

        self.stdout.write(
            self.style.SUCCESS(f"权限配置已生成到 {app_name}/permissions.py")
        )

    def parse_fields(self, fields_str: str) -> list:
        """解析字段定义"""
        if not fields_str:
            return [
                {
                    "name": "name",
                    "type": "str",
                    "django_field": "CharField(max_length=255)",
                    "pydantic_type": "str",
                },
                {
                    "name": "description",
                    "type": "text",
                    "django_field": "TextField(blank=True)",
                    "pydantic_type": "Optional[str]",
                },
                {
                    "name": "is_active",
                    "type": "bool",
                    "django_field": "BooleanField(default=True)",
                    "pydantic_type": "bool",
                },
            ]

        fields = []
        for field_def in fields_str.split(","):
            if ":" in field_def:
                name, field_type = field_def.strip().split(":")
                name = name.strip()
                field_type = field_type.strip()

                django_field, pydantic_type = self.get_field_types(field_type)

                fields.append(
                    {
                        "name": name,
                        "type": field_type,
                        "django_field": django_field,
                        "pydantic_type": pydantic_type,
                        "verbose_name": name.replace("_", " ").title(),
                    }
                )

        return fields

    def get_field_types(self, field_type: str) -> tuple:
        """转换字段类型为 Django 字段和 Pydantic 类型"""
        type_mapping = {
            "str": ("CharField(max_length=255)", "str"),
            "text": ("TextField()", "str"),
            "int": ("IntegerField()", "int"),
            "float": ("FloatField()", "float"),
            "bool": ("BooleanField(default=False)", "bool"),
            "date": ("DateField()", "date"),
            "datetime": ("DateTimeField()", "datetime"),
            "email": ("EmailField()", "str"),
            "url": ("URLField()", "str"),
            "json": ("JSONField(default=dict)", "dict"),
            "user": ("ForeignKey(User, on_delete=models.CASCADE)", "int"),
        }

        # 处理外键类型 (如 fk:blog.Post)
        if field_type.startswith("fk:"):
            related_model = field_type[3:]
            return (f'ForeignKey("{related_model}", on_delete=models.CASCADE)', "int")

        return type_mapping.get(field_type, ("CharField(max_length=255)", "str"))

    def render_template(self, template_path: str, context: dict) -> str:
        """渲染模板"""
        # 直接根据模板路径生成内容，不依赖模板文件
        if "models.py" in template_path:
            return self.generate_model_content(context)
        elif "model.py" in template_path:
            return self.generate_single_model_content(context)
        elif "api.py" in template_path:
            return self.generate_api_content(context)
        elif "admin.py" in template_path:
            return self.generate_admin_content(context)
        elif "schemas.py" in template_path:
            return self.generate_schemas_content(context)
        elif "services.py" in template_path:
            return self.generate_services_content(context)
        elif "permissions.py" in template_path:
            return self.generate_permissions_content(context)
        elif "tests.py" in template_path:
            return self.generate_tests_content(context)
        elif "apps.py" in template_path:
            return self.generate_apps_content(context)
        else:
            return "# Generated file\n"

    def generate_file_from_template(
        self, template_path: str, output_path: Path, context: dict
    ):
        """从模板生成文件"""
        content = self.render_template(template_path, context)

        # 确保目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, "w", encoding="utf-8") as f:
            f.write(content)

    def update_installed_apps(self, app_name: str):
        """更新 INSTALLED_APPS"""
        settings_file = Path("core/settings/base.py")
        if not settings_file.exists():
            return

        with open(settings_file, "r", encoding="utf-8") as f:
            content = f.read()

        app_entry = f'"apps.{app_name}",'

        if app_entry not in content:
            # 在 LOCAL_APPS 中添加应用
            pattern = r"(LOCAL_APPS\s*=\s*\[)(.*?)(\])"

            def replace_func(match):
                start, apps, end = match.groups()
                # 在最后一个应用后添加新应用
                apps = apps.rstrip()
                if not apps.endswith(","):
                    apps += ","
                return f"{start}{apps}\n    {app_entry}\n{end}"

            new_content = re.sub(pattern, replace_func, content, flags=re.DOTALL)

            with open(settings_file, "w", encoding="utf-8") as f:
                f.write(new_content)

            self.stdout.write(f"  ✓ 已添加到 INSTALLED_APPS: apps.{app_name}")

    def create_default_template(self, template_file: Path, template_path: str):
        """创建默认模板"""
        template_file.parent.mkdir(parents=True, exist_ok=True)

        # 根据模板路径创建默认内容
        if "models.py" in template_path:
            content = self.get_default_model_template()
        elif "api.py" in template_path:
            content = self.get_default_api_template()
        elif "admin.py" in template_path:
            content = self.get_default_admin_template()
        else:
            content = "# Generated file\n"

        with open(template_file, "w", encoding="utf-8") as f:
            f.write(content)

    def get_default_model_template(self) -> str:
        """获取默认模型模板"""
        return '''"""
{{ module_name|title }} 模块数据模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel

User = get_user_model()


class {{ model_name }}(BaseModel):
    """{{ model_name }} 模型"""
    
    {% for field in fields %}
    {{ field.name }} = models.{{ field.django_field }}{% if field.verbose_name %}, verbose_name=_('{{ field.verbose_name }}'){% endif %}
    {% endfor %}
    
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='{{ module_name }}_set',
        verbose_name=_('所有者')
    )
    
    class Meta:
        verbose_name = _('{{ model_name }}')
        verbose_name_plural = _('{{ model_name }} 列表')
        db_table = '{{ module_name }}_{{ model_name|lower }}'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return self.name if hasattr(self, 'name') else f'{{ model_name }} #{self.id}'
'''

    def get_default_api_template(self) -> str:
        """获取默认 API 模板"""
        return '''"""
{{ module_name|title }} API 接口
"""
from apps.core.generators.crud_generator import create_crud_router
from .models import {{ model_name }}

# 自动生成 CRUD 路由
router = create_crud_router(
    model={{ model_name }},
    permissions_prefix='{{ permissions_prefix }}',
    search_fields=['name'] if hasattr({{ model_name }}, 'name') else [],
    filter_fields=['is_active'] if hasattr({{ model_name }}, 'is_active') else [],
)
'''

    def get_default_admin_template(self) -> str:
        """获取默认 Admin 模板"""
        return '''"""
{{ module_name|title }} Admin 配置
"""
from apps.core.admin.auto_admin import register_auto_admin
from .models import {{ model_name }}

# 自动注册 Admin
register_auto_admin({{ model_name }})
'''

    def generate_model_content(self, context: dict) -> str:
        """生成模型文件内容"""
        module_name = context["module_name"]
        model_name = context["model_name"]
        fields = context["fields"]

        content = f'''"""
{module_name.title()} 模块数据模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel

User = get_user_model()


class {model_name}(BaseModel):
    """{model_name} 模型"""

'''

        # 生成字段
        for field in fields:
            django_field = field["django_field"]
            verbose_name = field.get(
                "verbose_name", field["name"].replace("_", " ").title()
            )
            content += f"    {field['name']} = models.{django_field}"
            if not django_field.endswith(")"):
                content += "()"
            if verbose_name:
                # 在括号内添加 verbose_name
                if content.endswith("()"):
                    content = content[:-1] + f"verbose_name=_('{verbose_name}'))"
                else:
                    content = content[:-1] + f", verbose_name=_('{verbose_name}'))"
            content += "\n"

        # 添加 owner 字段和 Meta 类
        content += f"""
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='{module_name}_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('{model_name}')
        verbose_name_plural = _('{model_name} 列表')
        db_table = '{module_name}_{model_name.lower()}'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):"""

        # 智能选择 __str__ 返回字段
        title_field = None
        for field in fields:
            if field["name"] in ["title", "name", "label"]:
                title_field = field["name"]
                break

        if title_field:
            content += f"\n        return self.{title_field}"
        else:
            content += f"\n        return f'{model_name} #{{self.id}}'"

        content += "\n"
        return content

    def generate_single_model_content(self, context: dict) -> str:
        """生成单个模型类内容（用于追加到现有models.py）"""
        model_name = context["model_name"]
        fields = context["fields"]

        content = f'''class {model_name}(BaseModel):
    """{model_name} 模型"""

'''

        # 生成字段
        for field in fields:
            django_field = field["django_field"]
            verbose_name = field.get(
                "verbose_name", field["name"].replace("_", " ").title()
            )
            content += f"    {field['name']} = models.{django_field}"
            if not django_field.endswith(")"):
                content += "()"
            if verbose_name:
                # 在括号内添加 verbose_name
                if content.endswith("()"):
                    content = content[:-1] + f"verbose_name=_('{verbose_name}'))"
                else:
                    content = content[:-1] + f", verbose_name=_('{verbose_name}'))"
            content += "\n"

        # 添加 owner 字段和 Meta 类
        content += f"""
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='{model_name.lower()}_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('{model_name}')
        verbose_name_plural = _('{model_name} 列表')
        db_table = '{model_name.lower()}'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):"""

        # 智能选择 __str__ 返回字段
        title_field = None
        for field in fields:
            if field["name"] in ["title", "name", "label"]:
                title_field = field["name"]
                break

        if title_field:
            content += f"\n        return self.{title_field}"
        else:
            content += f"\n        return f'{model_name} #{{self.id}}'"

        return content

    def generate_api_content(self, context: dict) -> str:
        """生成API文件内容"""
        module_name = context["module_name"]
        model_name = context["model_name"]
        permissions_prefix = context["permissions_prefix"]
        fields = context["fields"]

        # 找到搜索字段
        search_fields = []
        filter_fields = []

        for field in fields:
            if field["name"] in ["title", "name", "label"]:
                search_fields.append(f"'{field['name']}'")
            if field["type"] == "bool" or field["name"].startswith("is_"):
                filter_fields.append(f"'{field['name']}'")

        search_fields_str = ", ".join(search_fields) if search_fields else ""
        filter_fields_str = ", ".join(filter_fields) if filter_fields else ""

        return f'''"""
{module_name.title()} API 接口
"""
from apps.core.generators.crud_generator import create_crud_router
from .models import {model_name}

# 自动生成 CRUD 路由
router = create_crud_router(
    model={model_name},
    permissions_prefix='{permissions_prefix}',
    search_fields=[{search_fields_str}],
    filter_fields=[{filter_fields_str}],
)
'''

    def generate_schemas_content(self, context: dict) -> str:
        """生成schemas文件内容"""
        module_name = context["module_name"]
        model_name = context["model_name"]
        fields = context["fields"]

        content = f'''"""
{module_name.title()} 模块数据模式
"""
from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class {model_name}Schema(BaseModel):
    """{model_name} 输出模式"""
    id: int
'''

        # 添加字段
        for field in fields:
            pydantic_type = field.get("pydantic_type", "str")
            content += f"    {field['name']}: {pydantic_type}\n"

        content += f'''    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class {model_name}CreateSchema(BaseModel):
    """{model_name} 创建模式"""
'''

        for field in fields:
            pydantic_type = field.get("pydantic_type", "str")
            content += f"    {field['name']}: {pydantic_type}\n"

        content += f'''

class {model_name}UpdateSchema(BaseModel):
    """{model_name} 更新模式"""
'''

        for field in fields:
            pydantic_type = field.get("pydantic_type", "str")
            content += f"    {field['name']}: Optional[{pydantic_type}] = None\n"

        return content

    def generate_admin_content(self, context: dict) -> str:
        """生成admin文件内容"""
        module_name = context["module_name"]
        model_name = context["model_name"]

        return f'''"""
{module_name.title()} Admin 配置
"""
from apps.core.admin.auto_admin import register_auto_admin
from .models import {model_name}

# 自动注册 Admin
register_auto_admin({model_name})
'''

    def generate_services_content(self, context: dict) -> str:
        """生成services文件内容"""
        module_name = context["module_name"]
        model_name = context["model_name"]

        return f'''"""
{module_name.title()} 业务逻辑服务
"""
from apps.core.services import BaseService
from .models import {model_name}


class {model_name}Service(BaseService):
    model = {model_name}
'''

    def generate_permissions_content(self, context: dict) -> str:
        """生成permissions文件内容"""
        module_name = context["module_name"]
        model_name = context["model_name"]

        return f'''"""
{module_name.title()} 权限配置
"""

{module_name.upper()}_PERMISSIONS = [
    ('{module_name}.{model_name.lower()}.view', '查看{model_name}', '{module_name}', 'view'),
    ('{module_name}.{model_name.lower()}.create', '创建{model_name}', '{module_name}', 'create'),
    ('{module_name}.{model_name.lower()}.edit', '编辑{model_name}', '{module_name}', 'edit'),
    ('{module_name}.{model_name.lower()}.delete', '删除{model_name}', '{module_name}', 'delete'),
    ('{module_name}.{model_name.lower()}.manage', '管理{model_name}', '{module_name}', 'manage'),
]

{module_name.upper()}_ROLES = [
    {{
        'name': '{model_name} 查看者',
        'description': '可以查看 {model_name} 相关资源',
        'permissions': ['{module_name}.{model_name.lower()}.view']
    }},
    {{
        'name': '{model_name} 编辑者',
        'description': '可以创建和编辑 {model_name} 相关资源',
        'permissions': ['{module_name}.{model_name.lower()}.view', '{module_name}.{model_name.lower()}.create', '{module_name}.{model_name.lower()}.edit']
    }},
    {{
        'name': '{model_name} 管理员',
        'description': '拥有 {model_name} 的完整权限',
        'permissions': ['{module_name}.{model_name.lower()}.view', '{module_name}.{model_name.lower()}.create', '{module_name}.{model_name.lower()}.edit', '{module_name}.{model_name.lower()}.delete', '{module_name}.{model_name.lower()}.manage']
    }}
]
'''

    def generate_tests_content(self, context: dict) -> str:
        """生成tests文件内容"""
        module_name = context["module_name"]
        model_name = context["model_name"]

        return f'''"""
{module_name.title()} 测试用例
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from .models import {model_name}

User = get_user_model()


class {model_name}ModelTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )

    def test_{model_name.lower()}_creation(self):
        {model_name.lower()} = {model_name}.objects.create(
            owner=self.user
        )
        self.assertEqual({model_name.lower()}.owner, self.user)
'''

    def generate_apps_content(self, context: dict) -> str:
        """生成apps文件内容"""
        module_name = context["module_name"]

        return f'''"""
{module_name.title()} 应用配置
"""
from django.apps import AppConfig


class {module_name.title()}Config(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.{module_name}'
    verbose_name = '{module_name.title()}'
'''
