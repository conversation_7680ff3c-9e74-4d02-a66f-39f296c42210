# Django 核心框架
Django==5.2.4
asgiref==3.9.1

# Django Ninja API 框架
django-ninja==1.4.3
django-ninja-extra==0.30.1

# 数据验证和序列化
pydantic==2.11.7
pydantic_core==2.33.2
annotated-types==0.7.0
dnspython==2.7.0
email-validator==2.1.1

# 数据库相关
dj-database-url==3.0.1
psycopg2-binary==2.9.10
sqlparse==0.5.3

# 缓存和 Redis
django-redis==5.4.0
redis==5.2.1

# 异步任务队列
celery==5.4.0

# JWT 认证
django-ninja-jwt==5.3.4

# 图片处理
Pillow==11.1.0

# Web 服务器
gunicorn==23.0.0

# 开发工具
django-debug-toolbar==4.4.6

# 云存储支持
boto3==1.35.84
google-cloud-storage==2.18.0
azure-storage-blob==12.23.1

# 错误监控
sentry-sdk==2.19.2

# 时区处理
pytz==2024.2

# 类型检查
typing-inspection==0.4.1
typing_extensions==4.14.1

# 依赖注入
injector==0.22.0

# 其他工具
contextlib2==21.6.0
tzdata==2025.2
PyYAML==6.0.2

# 开发和测试工具
coverage==7.6.9
factory-boy==3.3.1
faker==33.1.0

# 代码质量工具
black==24.10.0
isort==5.13.2
flake8==7.1.1
mypy==1.13.0

