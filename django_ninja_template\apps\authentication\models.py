"""
Authentication models for JWT tokens and sessions.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
import uuid

User = get_user_model()


class RefreshToken(models.Model):
    """
    Model to store refresh tokens for JWT authentication.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='refresh_tokens'
    )
    token = models.TextField(_('refresh token'))
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    expires_at = models.DateTimeField(_('expires at'))
    is_revoked = models.BooleanField(_('is revoked'), default=False)
    
    # Device information
    device_name = models.CharField(_('device name'), max_length=200, blank=True)
    ip_address = models.GenericIPAddressField(_('IP address'), blank=True, null=True)
    user_agent = models.TextField(_('user agent'), blank=True)
    
    class Meta:
        verbose_name = _('Refresh Token')
        verbose_name_plural = _('Refresh Tokens')
        db_table = 'auth_refresh_tokens'
        indexes = [
            models.Index(fields=['user', 'is_revoked']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Refresh token for {self.user.email}"
    
    def revoke(self):
        """Revoke this refresh token."""
        self.is_revoked = True
        self.save(update_fields=['is_revoked'])


class LoginAttempt(models.Model):
    """
    Model to track login attempts for security monitoring.
    """
    email = models.EmailField(_('email'))
    ip_address = models.GenericIPAddressField(_('IP address'))
    user_agent = models.TextField(_('user agent'), blank=True)
    success = models.BooleanField(_('success'))
    failure_reason = models.CharField(
        _('failure reason'),
        max_length=100,
        blank=True,
        choices=[
            ('invalid_credentials', _('Invalid credentials')),
            ('account_disabled', _('Account disabled')),
            ('account_locked', _('Account locked')),
            ('too_many_attempts', _('Too many attempts')),
        ]
    )
    attempted_at = models.DateTimeField(_('attempted at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Login Attempt')
        verbose_name_plural = _('Login Attempts')
        db_table = 'auth_login_attempts'
        indexes = [
            models.Index(fields=['email', 'attempted_at']),
            models.Index(fields=['ip_address', 'attempted_at']),
        ]
    
    def __str__(self):
        status = 'Success' if self.success else 'Failed'
        return f"{status} login attempt for {self.email}"


class PasswordResetToken(models.Model):
    """
    Model to store password reset tokens.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='password_reset_tokens'
    )
    token = models.CharField(_('token'), max_length=255, unique=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    expires_at = models.DateTimeField(_('expires at'))
    is_used = models.BooleanField(_('is used'), default=False)
    
    class Meta:
        verbose_name = _('Password Reset Token')
        verbose_name_plural = _('Password Reset Tokens')
        db_table = 'auth_password_reset_tokens'
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Password reset token for {self.user.email}"
    
    def use(self):
        """Mark this token as used."""
        self.is_used = True
        self.save(update_fields=['is_used'])


class EmailVerificationToken(models.Model):
    """
    Model to store email verification tokens.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='email_verification_tokens'
    )
    token = models.CharField(_('token'), max_length=255, unique=True)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    expires_at = models.DateTimeField(_('expires at'))
    is_used = models.BooleanField(_('is used'), default=False)
    
    class Meta:
        verbose_name = _('Email Verification Token')
        verbose_name_plural = _('Email Verification Tokens')
        db_table = 'auth_email_verification_tokens'
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Email verification token for {self.user.email}"
    
    def use(self):
        """Mark this token as used."""
        self.is_used = True
        self.save(update_fields=['is_used'])
