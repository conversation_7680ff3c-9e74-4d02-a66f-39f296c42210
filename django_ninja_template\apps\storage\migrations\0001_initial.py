# Generated by Django 5.2.4 on 2025-07-14 08:26

import apps.storage.models
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="FileCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("allowed_extensions", models.J<PERSON>NField(default=list)),
                ("max_file_size", models.BigIntegerField(default=10485760)),
            ],
            options={
                "verbose_name": "文件分类",
                "verbose_name_plural": "文件分类",
            },
        ),
        migrations.CreateModel(
            name="FileShare",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("share_token", models.UUIDField(default=uuid.uuid4, unique=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("download_limit", models.PositiveIntegerField(blank=True, null=True)),
                ("download_count", models.PositiveIntegerField(default=0)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "文件分享",
                "verbose_name_plural": "文件分享",
            },
        ),
        migrations.CreateModel(
            name="ImageThumbnail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("size", models.CharField(max_length=20)),
                ("thumbnail", models.ImageField(upload_to="thumbnails/")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "缩略图",
                "verbose_name_plural": "缩略图",
            },
        ),
        migrations.CreateModel(
            name="StorageFile",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("original_name", models.CharField(max_length=255)),
                ("file", models.FileField(upload_to=apps.storage.models.upload_to)),
                ("file_size", models.BigIntegerField()),
                ("content_type", models.CharField(max_length=100)),
                ("file_hash", models.CharField(max_length=64)),
                ("is_public", models.BooleanField(default=False)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("uploading", "上传中"),
                            ("completed", "已完成"),
                            ("failed", "上传失败"),
                            ("deleted", "已删除"),
                        ],
                        default="uploading",
                        max_length=20,
                    ),
                ),
                ("download_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("metadata", models.JSONField(default=dict)),
            ],
            options={
                "verbose_name": "存储文件",
                "verbose_name_plural": "存储文件",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UploadSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.UUIDField(default=uuid.uuid4, unique=True)),
                ("filename", models.CharField(max_length=255)),
                ("file_size", models.BigIntegerField()),
                ("chunk_size", models.IntegerField(default=1048576)),
                ("uploaded_chunks", models.JSONField(default=list)),
                ("total_chunks", models.IntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "上传会话",
                "verbose_name_plural": "上传会话",
            },
        ),
    ]
