# 🏗️ Django Ninja 脚手架核心应用配置指南

## 🎯 概述

本文档说明如何将现有的apps配置为Django Ninja脚手架项目的核心部分，为用户提供开箱即用的企业级功能。

## 📦 核心应用架构

### 1. 核心应用列表

```python
# core/settings/base.py - LOCAL_APPS 配置
LOCAL_APPS = [
    # 🔐 用户认证核心
    "apps.users",           # 用户管理 - 扩展用户模型、用户资料
    "apps.authentication", # 认证系统 - JWT认证、登录监控、密码重置
    "apps.permissions",     # 权限管理 - RBAC权限、角色管理
    
    # 🛠️ 功能核心
    "apps.storage",         # 文件存储 - 文件上传、下载、断点续传
    "apps.i18n",           # 国际化 - 多语言支持、翻译管理
    "apps.core",           # 核心功能 - 系统配置、审计日志、工具类
    
    # 🎨 开发工具
    
    # 📝 示例应用（可选）
    "apps.cms",            # CMS示例 - 展示如何构建内容管理系统
]
```

### 2. 应用功能矩阵

| 应用 | 核心功能 | 提供的API | 管理界面 | 状态 |
|------|----------|-----------|----------|------|
| **users** | 用户管理、用户资料 | 用户CRUD、资料管理 | ✅ | 核心 |
| **authentication** | JWT认证、安全监控 | 登录、注册、密码重置 | ✅ | 核心 |
| **permissions** | RBAC权限管理 | 权限检查、角色管理 | ✅ | 核心 |
| **storage** | 文件存储管理 | 文件上传、下载、分享 | ✅ | 核心 |
| **i18n** | 国际化支持 | 翻译管理、语言切换 | ✅ | 核心 |
| **core** | 系统核心功能 | 配置管理、健康检查 | ✅ | 核心 |
| **cms** | 内容管理示例 | 文章、分类、评论 | ✅ | 示例 |

## 🔧 配置步骤

### 第一步：执行数据库迁移

```bash
# 1. 生成迁移文件
python manage.py makemigrations

# 2. 执行迁移
python manage.py migrate

# 3. 创建超级用户
python manage.py createsuperuser

# 4. 初始化权限系统
python manage.py setup_permissions
```

### 第二步：验证应用配置

```bash
# 检查项目配置
python manage.py check

# 查看已安装的应用
python manage.py showmigrations

# 启动开发服务器
python manage.py runserver
```

### 第三步：访问核心功能

启动后可以访问：

- **API文档**: http://localhost:8000/api/docs/
- **管理后台**: http://localhost:8000/admin/
- **健康检查**: http://localhost:8000/health/

## 🎯 核心应用详细配置

### 1. users - 用户管理应用

**功能特性**：
- 扩展的用户模型（邮箱登录、头像、个人资料）
- 用户资料管理（社交链接、职业信息、隐私设置）
- 用户偏好设置（语言、时区、通知）

**API端点**：
```
GET    /api/users/me          # 获取当前用户信息
PUT    /api/users/me          # 更新用户信息
GET    /api/users/profile     # 获取用户资料
PUT    /api/users/profile     # 更新用户资料
```

### 2. authentication - 认证系统

**功能特性**：
- JWT认证机制（访问令牌 + 刷新令牌）
- 登录尝试监控和安全防护
- 密码重置和邮箱验证
- 设备管理和会话控制

**API端点**：
```
POST   /api/auth/register     # 用户注册
POST   /api/auth/login        # 用户登录
POST   /api/auth/logout       # 用户登出
POST   /api/auth/refresh      # 刷新令牌
POST   /api/auth/reset-password # 密码重置
```

### 3. permissions - 权限管理

**功能特性**：
- RBAC权限模型（用户-角色-权限）
- 细粒度权限控制
- 权限装饰器和中间件
- 动态权限检查

**API端点**：
```
GET    /api/permissions/      # 获取权限列表
GET    /api/permissions/roles # 获取角色列表
POST   /api/permissions/check # 检查权限
```

### 4. storage - 文件存储

**功能特性**：
- 多存储后端支持（本地/云存储）
- 文件上传、下载、预览
- 断点续传和大文件处理
- 图片处理和缩略图生成
- 文件分享和权限控制

**API端点**：
```
POST   /api/storage/upload    # 文件上传
GET    /api/storage/files     # 文件列表
GET    /api/storage/files/{id}/download # 文件下载
POST   /api/storage/share     # 文件分享
```

### 5. i18n - 国际化

**功能特性**：
- 多语言支持（中文、英文等）
- 动态翻译管理
- 语言切换API
- 翻译缓存优化

**API端点**：
```
GET    /api/i18n/languages   # 支持的语言列表
GET    /api/i18n/translations # 获取翻译
POST   /api/i18n/set-language # 设置语言
```

### 6. core - 核心功能

**功能特性**：
- 系统配置管理
- 审计日志记录
- 健康检查和监控
- 通用工具类和服务

**API端点**：
```
GET    /api/core/health      # 健康检查
GET    /api/core/config      # 系统配置
GET    /api/core/stats       # 系统统计
```

### 7. model_designer - 可视化设计器

**功能特性**：
- 拖拽式模型设计界面
- 实时代码预览
- 一键代码生成
- 模板系统支持

**访问方式**：
- Web界面：http://localhost:8000/model-designer/
- API文档：http://localhost:8000/api/docs/ (model-designer部分)

## 🚀 使用建议

### 作为脚手架核心的优势

1. **开箱即用**：用户克隆项目后即可获得完整的企业级功能
2. **模块化设计**：每个应用都是独立的，可以根据需要启用/禁用
3. **最佳实践**：集成了企业级开发的最佳实践
4. **扩展性强**：用户可以基于这些核心应用快速扩展

### 推荐的使用流程

1. **克隆脚手架项目**
2. **执行初始化命令**（迁移、创建用户、设置权限）
3. **根据需求启用/禁用应用**
4. **使用代码生成器创建业务模块**
5. **基于核心应用扩展业务功能**

### 可选配置

用户可以根据项目需求选择性启用应用：

```python
# 最小配置（仅核心功能）
CORE_APPS = [
    "apps.users",
    "apps.authentication", 
    "apps.core",
]

# 标准配置（包含常用功能）
STANDARD_APPS = CORE_APPS + [
    "apps.permissions",
    "apps.storage",
    "apps.i18n",
]

# 完整配置（包含所有功能）
FULL_APPS = STANDARD_APPS + [
    "apps.cms",  # 示例应用
]
```

## 📝 下一步

配置完成后，建议：

1. 阅读各应用的详细文档
2. 体验可视化模型设计器
3. 使用代码生成工具创建新模块
4. 参考CMS示例学习最佳实践

---

*这些核心应用为Django Ninja脚手架项目提供了坚实的基础，让开发者可以专注于业务逻辑的实现。*