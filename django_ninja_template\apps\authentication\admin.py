"""
Admin configuration for authentication app.
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from .models import RefreshToken, LoginAttempt, PasswordResetToken, EmailVerificationToken


@admin.register(RefreshToken)
class RefreshTokenAdmin(admin.ModelAdmin):
    """
    Admin configuration for RefreshToken model.
    """
    list_display = ('user', 'device_name', 'ip_address', 'created_at', 'expires_at', 'is_revoked')
    list_filter = ('is_revoked', 'created_at', 'expires_at')
    search_fields = ('user__email', 'user__username', 'device_name', 'ip_address')
    ordering = ('-created_at',)
    readonly_fields = ('token', 'created_at')
    
    fieldsets = (
        (_('Token Info'), {'fields': ('user', 'token', 'is_revoked')}),
        (_('Device Info'), {'fields': ('device_name', 'ip_address', 'user_agent')}),
        (_('Timestamps'), {'fields': ('created_at', 'expires_at')}),
    )


@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    """
    Admin configuration for LoginAttempt model.
    """
    list_display = ('email', 'ip_address', 'success', 'failure_reason', 'attempted_at')
    list_filter = ('success', 'failure_reason', 'attempted_at')
    search_fields = ('email', 'ip_address')
    ordering = ('-attempted_at',)
    readonly_fields = ('email', 'ip_address', 'user_agent', 'success', 'failure_reason', 'attempted_at')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    """
    Admin configuration for PasswordResetToken model.
    """
    list_display = ('user', 'created_at', 'expires_at', 'is_used')
    list_filter = ('is_used', 'created_at', 'expires_at')
    search_fields = ('user__email', 'user__username')
    ordering = ('-created_at',)
    readonly_fields = ('token', 'created_at')
    
    fieldsets = (
        (_('Token Info'), {'fields': ('user', 'token', 'is_used')}),
        (_('Timestamps'), {'fields': ('created_at', 'expires_at')}),
    )


@admin.register(EmailVerificationToken)
class EmailVerificationTokenAdmin(admin.ModelAdmin):
    """
    Admin configuration for EmailVerificationToken model.
    """
    list_display = ('user', 'created_at', 'expires_at', 'is_used')
    list_filter = ('is_used', 'created_at', 'expires_at')
    search_fields = ('user__email', 'user__username')
    ordering = ('-created_at',)
    readonly_fields = ('token', 'created_at')
    
    fieldsets = (
        (_('Token Info'), {'fields': ('user', 'token', 'is_used')}),
        (_('Timestamps'), {'fields': ('created_at', 'expires_at')}),
    )
