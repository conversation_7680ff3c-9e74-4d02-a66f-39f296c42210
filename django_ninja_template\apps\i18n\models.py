"""
Internationalization models.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel


class Language(BaseModel):
    """
    Model to store supported languages.
    """
    code = models.CharField(_('language code'), max_length=10, unique=True)
    name = models.CharField(_('language name'), max_length=100)
    native_name = models.CharField(_('native name'), max_length=100)
    is_active = models.BooleanField(_('is active'), default=True)
    is_default = models.BooleanField(_('is default'), default=False)
    
    # Text direction
    text_direction = models.CharField(
        _('text direction'),
        max_length=3,
        choices=[
            ('ltr', _('Left to Right')),
            ('rtl', _('Right to Left')),
        ],
        default='ltr'
    )
    
    class Meta:
        verbose_name = _('Language')
        verbose_name_plural = _('Languages')
        db_table = 'i18n_languages'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def save(self, *args, **kwargs):
        # Ensure only one default language
        if self.is_default:
            Language.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class Translation(BaseModel):
    """
    Model to store translations for dynamic content.
    """
    key = models.CharField(_('translation key'), max_length=255)
    language = models.ForeignKey(
        Language,
        on_delete=models.CASCADE,
        related_name='translations'
    )
    value = models.TextField(_('translated value'))
    context = models.CharField(_('context'), max_length=100, blank=True)
    
    # Metadata
    is_fuzzy = models.BooleanField(_('is fuzzy'), default=False)
    is_approved = models.BooleanField(_('is approved'), default=False)
    
    class Meta:
        verbose_name = _('Translation')
        verbose_name_plural = _('Translations')
        db_table = 'i18n_translations'
        unique_together = ('key', 'language', 'context')
        indexes = [
            models.Index(fields=['key', 'language']),
            models.Index(fields=['language', 'is_approved']),
        ]
    
    def __str__(self):
        return f"{self.key} ({self.language.code})"


class TranslationRequest(BaseModel):
    """
    Model to track translation requests.
    """
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('rejected', _('Rejected')),
    ]
    
    key = models.CharField(_('translation key'), max_length=255)
    source_language = models.ForeignKey(
        Language,
        on_delete=models.CASCADE,
        related_name='source_translation_requests'
    )
    target_language = models.ForeignKey(
        Language,
        on_delete=models.CASCADE,
        related_name='target_translation_requests'
    )
    source_text = models.TextField(_('source text'))
    translated_text = models.TextField(_('translated text'), blank=True)
    context = models.CharField(_('context'), max_length=100, blank=True)
    status = models.CharField(_('status'), max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Assignment
    assigned_to = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_translations'
    )
    
    # Metadata
    priority = models.IntegerField(_('priority'), default=1)
    deadline = models.DateTimeField(_('deadline'), null=True, blank=True)
    notes = models.TextField(_('notes'), blank=True)
    
    class Meta:
        verbose_name = _('Translation Request')
        verbose_name_plural = _('Translation Requests')
        db_table = 'i18n_translation_requests'
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['target_language', 'status']),
            models.Index(fields=['assigned_to', 'status']),
        ]
    
    def __str__(self):
        return f"{self.key} ({self.source_language.code} → {self.target_language.code})"
