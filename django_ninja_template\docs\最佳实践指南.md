# 🏆 Django Ninja 脚手架项目最佳实践指南

本指南汇总了使用 Django Ninja 脚手架项目开发企业级项目的最佳实践，帮助开发者避免常见陷阱，提高代码质量和项目可维护性。

## 🎯 总体原则

### 1. 代码组织原则
- **模块化设计**: 按功能领域划分应用模块
- **单一职责**: 每个模块只负责一个特定功能
- **松耦合**: 模块间通过明确接口交互
- **高内聚**: 相关功能集中在同一模块内

### 2. 开发流程原则
- **测试驱动**: 先写测试，再写实现
- **渐进式开发**: 小步快跑，持续迭代
- **代码审查**: 所有代码都要经过审查
- **文档同步**: 代码和文档同步更新

## 🏗️ 架构设计最佳实践

### 1. 模型设计

#### ✅ 推荐做法

```python
# 使用抽象基类
class BaseModel(models.Model):
    """基础模型类"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='%(class)s_set')
    
    class Meta:
        abstract = True

# 合理的字段约束
class Article(BaseModel):
    title = models.CharField(max_length=200, db_index=True)  # 添加索引
    slug = models.SlugField(max_length=200, unique=True)     # 唯一约束
    content = models.TextField()
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='draft',
        db_index=True  # 经常查询的字段添加索引
    )
    
    class Meta:
        ordering = ['-created_at']  # 默认排序
        indexes = [
            models.Index(fields=['status', 'created_at']),  # 复合索引
        ]
```

#### ❌ 避免的做法

```python
# 避免：没有约束的字段
class BadArticle(models.Model):
    title = models.CharField(max_length=1000)  # 过长的字段
    content = models.TextField(null=True)      # 不必要的 null=True
    status = models.CharField(max_length=50)   # 没有 choices 约束
    
    # 没有索引，查询性能差
    # 没有默认排序
```

### 2. API 设计

#### ✅ 推荐做法

```python
# 使用 Pydantic 模式进行数据验证
class ArticleCreateSchema(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=1)
    tags: List[str] = Field(default=[], max_items=10)
    
    @validator('title')
    def validate_title(cls, v):
        if not v.strip():
            raise ValueError('标题不能为空')
        return v.strip()

# 统一的错误处理
@router.post("/articles", response=ArticleSchema)
def create_article(request, data: ArticleCreateSchema):
    try:
        article = ArticleService.create_article(request.user, data.dict())
        return article
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
```

#### ❌ 避免的做法

```python
# 避免：没有数据验证
@router.post("/articles")
def bad_create_article(request, title: str, content: str):
    # 直接使用原始数据，没有验证
    article = Article.objects.create(
        title=title,
        content=content,
        author=request.user
    )
    return {"id": article.id}  # 返回格式不一致
```

### 3. 服务层设计

#### ✅ 推荐做法

```python
class ArticleService:
    """文章服务层"""
    
    @staticmethod
    @transaction.atomic
    def create_article(user: User, data: dict) -> Article:
        """创建文章"""
        # 权限检查
        if not user.has_perm('cms.add_article'):
            raise PermissionError("没有创建文章的权限")
        
        # 业务逻辑
        article = Article.objects.create(
            title=data['title'],
            content=data['content'],
            author=user,
            owner=user
        )
        
        # 处理标签
        if data.get('tags'):
            ArticleService._handle_tags(article, data['tags'])
        
        # 发送通知
        NotificationService.send_article_created(article)
        
        return article
    
    @staticmethod
    def _handle_tags(article: Article, tag_names: List[str]):
        """处理文章标签"""
        tags = []
        for name in tag_names:
            tag, created = Tag.objects.get_or_create(name=name)
            tags.append(tag)
        article.tags.set(tags)
```

#### ❌ 避免的做法

```python
# 避免：在视图中写业务逻辑
@router.post("/articles")
def bad_create_article(request, data: ArticleCreateSchema):
    # 业务逻辑直接写在视图中
    article = Article.objects.create(
        title=data.title,
        content=data.content,
        author=request.user
    )
    
    # 没有事务保护
    for tag_name in data.tags:
        tag, created = Tag.objects.get_or_create(name=tag_name)
        article.tags.add(tag)
    
    return article
```

## 🔒 安全最佳实践

### 1. 认证和授权

#### ✅ 推荐做法

```python
# 使用装饰器进行权限控制
@router.get("/articles/{article_id}")
@jwt_required
@require_permission('cms.view_article')
def get_article(request, article_id: int):
    article = get_object_or_404(Article, id=article_id)
    
    # 额外的权限检查
    if not ArticleService.can_view_article(request.user, article):
        raise HTTPException(status_code=403, detail="无权查看此文章")
    
    return article

# 细粒度权限检查
class ArticleService:
    @staticmethod
    def can_view_article(user: User, article: Article) -> bool:
        # 公开文章
        if article.status == 'published':
            return True
        
        # 作者可以查看自己的文章
        if article.author == user:
            return True
        
        # 管理员可以查看所有文章
        if user.is_staff:
            return True
        
        return False
```

### 2. 数据验证

#### ✅ 推荐做法

```python
# 输入验证
class CommentCreateSchema(BaseModel):
    content: str = Field(..., min_length=1, max_length=1000)
    
    @validator('content')
    def validate_content(cls, v):
        # 过滤敏感词
        if SensitiveWordFilter.contains_sensitive_words(v):
            raise ValueError('评论包含敏感词')
        
        # 防止 XSS
        clean_content = bleach.clean(v, tags=[], strip=True)
        return clean_content

# SQL 注入防护（使用 ORM）
def search_articles(query: str):
    # ✅ 使用 ORM，自动防护 SQL 注入
    return Article.objects.filter(title__icontains=query)
    
    # ❌ 避免原始 SQL
    # return Article.objects.raw(f"SELECT * FROM articles WHERE title LIKE '%{query}%'")
```

### 3. 文件上传安全

#### ✅ 推荐做法

```python
class FileUploadService:
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    @staticmethod
    def validate_file(file: UploadedFile) -> bool:
        # 检查文件扩展名
        ext = os.path.splitext(file.name)[1].lower()
        if ext not in FileUploadService.ALLOWED_EXTENSIONS:
            raise ValueError(f"不支持的文件类型: {ext}")
        
        # 检查文件大小
        if file.size > FileUploadService.MAX_FILE_SIZE:
            raise ValueError("文件大小超过限制")
        
        # 检查文件内容类型
        if not file.content_type.startswith(('image/', 'application/')):
            raise ValueError("无效的文件类型")
        
        return True
    
    @staticmethod
    def generate_safe_filename(original_name: str) -> str:
        """生成安全的文件名"""
        name, ext = os.path.splitext(original_name)
        safe_name = re.sub(r'[^\w\-_.]', '', name)
        return f"{uuid.uuid4().hex}_{safe_name}{ext}"
```

## 🚀 性能优化最佳实践

### 1. 数据库查询优化

#### ✅ 推荐做法

```python
# 使用 select_related 和 prefetch_related
def get_articles_with_authors():
    return Article.objects.select_related(
        'author', 'category'
    ).prefetch_related(
        'tags', 'comments__author'
    ).filter(status='published')

# 使用 only() 和 defer() 优化字段查询
def get_article_list():
    return Article.objects.only(
        'id', 'title', 'summary', 'created_at', 'author__username'
    ).select_related('author')

# 使用聚合查询
def get_article_stats():
    return Article.objects.aggregate(
        total_count=Count('id'),
        published_count=Count('id', filter=Q(status='published')),
        avg_views=Avg('view_count')
    )

# 批量操作
def bulk_update_articles(articles_data):
    articles = []
    for data in articles_data:
        article = Article(id=data['id'], **data['fields'])
        articles.append(article)
    
    Article.objects.bulk_update(
        articles, 
        ['title', 'content', 'updated_at'],
        batch_size=100
    )
```

#### ❌ 避免的做法

```python
# 避免：N+1 查询问题
def bad_get_articles():
    articles = Article.objects.all()
    for article in articles:
        print(article.author.username)  # 每次都查询数据库
        print(article.category.name)    # 每次都查询数据库

# 避免：在循环中执行数据库操作
def bad_bulk_create():
    for i in range(1000):
        Article.objects.create(title=f"Article {i}")  # 1000次数据库操作
```

### 2. 缓存策略

#### ✅ 推荐做法

```python
from django.core.cache import cache
from django.views.decorators.cache import cache_page

# 视图级缓存
@cache_page(60 * 15)  # 缓存15分钟
def article_list(request):
    return Article.objects.filter(status='published')

# 查询结果缓存
def get_popular_articles():
    cache_key = 'popular_articles'
    articles = cache.get(cache_key)
    
    if articles is None:
        articles = Article.objects.filter(
            status='published'
        ).order_by('-view_count')[:10]
        cache.set(cache_key, articles, 60 * 30)  # 缓存30分钟
    
    return articles

# 缓存失效
def update_article(article_id, data):
    article = Article.objects.get(id=article_id)
    # 更新文章...
    
    # 清除相关缓存
    cache.delete(f'article_{article_id}')
    cache.delete('popular_articles')
    cache.delete_pattern('article_list_*')
```

### 3. 异步任务

#### ✅ 推荐做法

```python
# 使用 Celery 处理耗时任务
@shared_task
def send_notification_email(user_id, message):
    """发送通知邮件"""
    try:
        user = User.objects.get(id=user_id)
        send_mail(
            subject='新通知',
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email]
        )
    except Exception as e:
        logger.error(f"发送邮件失败: {e}")

# 在视图中调用异步任务
def create_article(request, data):
    article = ArticleService.create_article(request.user, data)
    
    # 异步发送通知
    send_notification_email.delay(
        request.user.id, 
        f"您的文章《{article.title}》已发布"
    )
    
    return article
```

## 🧪 测试最佳实践

### 1. 单元测试

#### ✅ 推荐做法

```python
class ArticleServiceTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
        self.category = Category.objects.create(
            name='测试分类',
            slug='test-category'
        )
    
    def test_create_article_success(self):
        """测试成功创建文章"""
        data = {
            'title': '测试文章',
            'content': '测试内容',
            'category_id': self.category.id
        }
        
        article = ArticleService.create_article(self.user, data)
        
        self.assertEqual(article.title, '测试文章')
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.category, self.category)
    
    def test_create_article_without_permission(self):
        """测试无权限创建文章"""
        user_without_permission = User.objects.create_user(
            username='noperm',
            email='<EMAIL>'
        )
        
        data = {'title': '测试文章', 'content': '测试内容'}
        
        with self.assertRaises(PermissionError):
            ArticleService.create_article(user_without_permission, data)
    
    @patch('apps.cms.services.NotificationService.send_article_created')
    def test_create_article_sends_notification(self, mock_send):
        """测试创建文章时发送通知"""
        data = {'title': '测试文章', 'content': '测试内容'}
        
        article = ArticleService.create_article(self.user, data)
        
        mock_send.assert_called_once_with(article)
```

### 2. API 测试

#### ✅ 推荐做法

```python
class ArticleAPITestCase(TestCase):
    def setUp(self):
        self.client = TestClient(router)
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
        self.token = self.get_jwt_token()
    
    def test_create_article_api(self):
        """测试创建文章 API"""
        data = {
            'title': '测试文章',
            'content': '测试内容'
        }
        
        response = self.client.post(
            '/articles',
            json=data,
            headers={'Authorization': f'Bearer {self.token}'}
        )
        
        self.assertEqual(response.status_code, 201)
        
        response_data = response.json()
        self.assertEqual(response_data['title'], '测试文章')
        
        # 验证数据库中的记录
        article = Article.objects.get(id=response_data['id'])
        self.assertEqual(article.title, '测试文章')
    
    def test_create_article_validation_error(self):
        """测试数据验证错误"""
        data = {
            'title': '',  # 空标题
            'content': 'x' * 10001  # 内容过长
        }
        
        response = self.client.post(
            '/articles',
            json=data,
            headers={'Authorization': f'Bearer {self.token}'}
        )
        
        self.assertEqual(response.status_code, 400)
        self.assertIn('errors', response.json())
```

## 📝 代码质量最佳实践

### 1. 代码风格

#### ✅ 推荐做法

```python
# 使用类型提示
def create_article(user: User, data: Dict[str, Any]) -> Article:
    """创建文章
    
    Args:
        user: 创建文章的用户
        data: 文章数据
        
    Returns:
        创建的文章对象
        
    Raises:
        ValueError: 数据验证失败
        PermissionError: 权限不足
    """
    if not user.has_perm('cms.add_article'):
        raise PermissionError("没有创建文章的权限")
    
    return Article.objects.create(
        title=data['title'],
        content=data['content'],
        author=user
    )

# 使用常量
class ArticleStatus:
    DRAFT = 'draft'
    PUBLISHED = 'published'
    ARCHIVED = 'archived'
    
    CHOICES = [
        (DRAFT, '草稿'),
        (PUBLISHED, '已发布'),
        (ARCHIVED, '已归档'),
    ]

# 合理的异常处理
def get_article_by_slug(slug: str) -> Article:
    try:
        return Article.objects.get(slug=slug, status=ArticleStatus.PUBLISHED)
    except Article.DoesNotExist:
        logger.warning(f"文章不存在: {slug}")
        raise Http404("文章不存在")
    except Exception as e:
        logger.error(f"获取文章失败: {e}")
        raise
```

### 2. 日志记录

#### ✅ 推荐做法

```python
import logging

logger = logging.getLogger(__name__)

class ArticleService:
    @staticmethod
    def create_article(user: User, data: dict) -> Article:
        logger.info(f"用户 {user.username} 开始创建文章: {data.get('title')}")
        
        try:
            article = Article.objects.create(**data, author=user)
            logger.info(f"文章创建成功: {article.id}")
            return article
        except Exception as e:
            logger.error(f"文章创建失败: {e}", exc_info=True)
            raise
    
    @staticmethod
    def delete_article(article_id: int, user: User):
        try:
            article = Article.objects.get(id=article_id)
            if article.author != user:
                logger.warning(f"用户 {user.username} 尝试删除他人文章: {article_id}")
                raise PermissionError("无权删除此文章")
            
            article.delete()
            logger.info(f"文章删除成功: {article_id}")
        except Article.DoesNotExist:
            logger.warning(f"尝试删除不存在的文章: {article_id}")
            raise Http404("文章不存在")
```

## 🔧 部署和运维最佳实践

### 1. 环境配置

#### ✅ 推荐做法

```python
# 使用环境变量
import os
from pathlib import Path

# 敏感信息通过环境变量配置
SECRET_KEY = os.environ.get('SECRET_KEY')
DATABASE_URL = os.environ.get('DATABASE_URL')
REDIS_URL = os.environ.get('REDIS_URL')

# 不同环境的配置
ENVIRONMENT = os.environ.get('DJANGO_ENVIRONMENT', 'development')

if ENVIRONMENT == 'production':
    DEBUG = False
    ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')
else:
    DEBUG = True
    ALLOWED_HOSTS = ['localhost', '127.0.0.1']

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 2. 监控和告警

#### ✅ 推荐做法

```python
# 健康检查端点
@router.get("/health")
def health_check():
    """系统健康检查"""
    try:
        # 检查数据库连接
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        # 检查缓存
        from django.core.cache import cache
        cache.set('health_check', 'ok', 10)
        cache.get('health_check')
        
        return {
            "status": "healthy",
            "timestamp": timezone.now().isoformat(),
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": timezone.now().isoformat()
        }

# 性能监控
import time
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"{func.__name__} 执行时间: {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"{func.__name__} 执行失败 ({duration:.3f}s): {e}")
            raise
    return wrapper
```

## 📋 总结

遵循这些最佳实践可以帮助您：

- 🏗️ **构建可维护的架构**: 清晰的模块划分和职责分离
- 🔒 **确保系统安全**: 完善的认证、授权和数据验证
- 🚀 **优化系统性能**: 高效的数据库查询和缓存策略
- 🧪 **保证代码质量**: 完整的测试覆盖和代码规范
- 🔧 **简化运维管理**: 标准化的部署和监控流程

记住，最佳实践不是一成不变的规则，而是经过验证的指导原则。在实际项目中，要根据具体情况灵活应用，持续改进和优化。

---

**🎉 最佳实践指南完成！** 这些实践经验将帮助您构建更加健壮、安全、高效的企业级应用。
