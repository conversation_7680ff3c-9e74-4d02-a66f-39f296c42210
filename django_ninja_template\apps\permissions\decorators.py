"""
API 端点的权限装饰器。
"""
from functools import wraps
from typing import List, Union
from ninja.errors import HttpError
from ninja_jwt.authentication import J<PERSON><PERSON><PERSON>
from .services import PermissionService


def require_permissions(permissions: Union[str, List[str]], require_all: bool = True):
    """
    Decorator to require specific permissions for API endpoints.
    
    Args:
        permissions: Single permission codename or list of permission codenames
        require_all: If True, user must have ALL permissions. If False, user needs ANY permission.
    """
    if isinstance(permissions, str):
        permissions = [permissions]
    
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            user = getattr(request, 'auth', None)
            
            if not user:
                raise HttpError(401, "Authentication required")
            
            if require_all:
                # User must have ALL permissions
                for permission in permissions:
                    if not PermissionService.user_has_permission(user, permission):
                        raise HttpError(403, f"Permission denied: {permission}")
            else:
                # User must have ANY permission
                has_any_permission = any(
                    PermissionService.user_has_permission(user, permission)
                    for permission in permissions
                )
                if not has_any_permission:
                    raise HttpError(403, f"Permission denied: requires one of {permissions}")
            
            return func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def require_role(role_name: str):
    """
    Decorator to require a specific role for API endpoints.
    """
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            user = getattr(request, 'auth', None)
            
            if not user:
                raise HttpError(401, "Authentication required")
            
            # Check if user has the required role
            from .models import UserRole
            has_role = UserRole.objects.filter(
                user=user,
                role__name=role_name,
                is_active=True,
                role__is_active=True
            ).exists()
            
            if not has_role:
                raise HttpError(403, f"Role required: {role_name}")
            
            return func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def require_superuser(func):
    """
    Decorator to require superuser status for API endpoints.
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        user = getattr(request, 'auth', None)
        
        if not user:
            raise HttpError(401, "Authentication required")
        
        if not user.is_superuser:
            raise HttpError(403, "Superuser access required")
        
        return func(request, *args, **kwargs)
    
    return wrapper


def require_staff(func):
    """
    Decorator to require staff status for API endpoints.
    """
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        user = getattr(request, 'auth', None)
        
        if not user:
            raise HttpError(401, "Authentication required")
        
        if not user.is_staff:
            raise HttpError(403, "Staff access required")
        
        return func(request, *args, **kwargs)
    
    return wrapper


def require_owner_or_permission(permission: str, owner_field: str = 'user'):
    """
    Decorator to require either ownership of a resource or a specific permission.
    
    Args:
        permission: Permission codename required if not owner
        owner_field: Field name to check for ownership (default: 'user')
    """
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            user = getattr(request, 'auth', None)
            
            if not user:
                raise HttpError(401, "Authentication required")
            
            # Try to get the resource object from kwargs
            resource_id = kwargs.get('id') or kwargs.get('pk')
            if resource_id:
                # This is a simplified check - in real implementation,
                # you'd need to specify the model and get the actual object
                # For now, we'll just check the permission
                if not PermissionService.user_has_permission(user, permission):
                    raise HttpError(403, f"Permission denied: {permission}")
            
            return func(request, *args, **kwargs)
        
        return wrapper
    return decorator
