"""
前端代码生成管理命令
"""
from django.core.management.base import BaseCommand, CommandError
from django.apps import apps
from django.db import models
from apps.core.frontend.vue_generator import VueComponentGenerator
from apps.core.graphql.auto_schema import generate_graphql_schema
from pathlib import Path
import json


class Command(BaseCommand):
    help = '生成前端代码 (Vue.js 组件、API 服务、GraphQL Schema 等)'
    
    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='command', help='生成类型')
        
        # 生成 Vue 组件
        vue_parser = subparsers.add_parser('vue', help='生成 Vue.js 组件')
        vue_parser.add_argument('app', type=str, help='应用名称')
        vue_parser.add_argument('--model', type=str, help='指定模型名称')
        vue_parser.add_argument('--output', type=str, default='frontend/src', help='输出目录')
        vue_parser.add_argument('--api-prefix', type=str, default='/api', help='API 前缀')
        
        # 生成 GraphQL Schema
        graphql_parser = subparsers.add_parser('graphql', help='生成 GraphQL Schema')
        graphql_parser.add_argument('app', type=str, help='应用名称')
        graphql_parser.add_argument('--output', type=str, help='输出文件路径')
        
        # 生成完整前端项目
        project_parser = subparsers.add_parser('project', help='生成完整前端项目')
        project_parser.add_argument('--name', type=str, default='frontend', help='项目名称')
        project_parser.add_argument('--framework', type=str, choices=['vue', 'react'], default='vue', help='前端框架')
        project_parser.add_argument('--apps', type=str, help='包含的应用列表 (用逗号分隔)')
        
        # 生成 API 客户端
        api_parser = subparsers.add_parser('api', help='生成 API 客户端')
        api_parser.add_argument('app', type=str, help='应用名称')
        api_parser.add_argument('--language', type=str, choices=['javascript', 'typescript'], default='javascript', help='语言类型')
        api_parser.add_argument('--output', type=str, default='api-client', help='输出目录')
    
    def handle(self, *args, **options):
        command = options['command']
        
        if command == 'vue':
            self.generate_vue_components(options)
        elif command == 'graphql':
            self.generate_graphql_schema(options)
        elif command == 'project':
            self.generate_frontend_project(options)
        elif command == 'api':
            self.generate_api_client(options)
        else:
            raise CommandError('未知命令')
    
    def generate_vue_components(self, options):
        """生成 Vue 组件"""
        app_name = options['app']
        model_name = options.get('model')
        output_dir = Path(options['output'])
        api_prefix = options['api_prefix']
        
        try:
            app_config = apps.get_app_config(app_name)
        except LookupError:
            raise CommandError(f'应用 {app_name} 不存在')
        
        models_to_generate = []
        if model_name:
            # 生成指定模型
            model = self.get_model_by_name(app_config, model_name)
            if not model:
                raise CommandError(f'模型 {model_name} 不存在')
            models_to_generate = [model]
        else:
            # 生成所有模型
            models_to_generate = app_config.get_models()
        
        self.stdout.write(f'正在为应用 {app_name} 生成 Vue 组件...')
        
        total_files = 0
        for model in models_to_generate:
            generator = VueComponentGenerator(model, api_prefix)
            components = generator.generate_all_components()
            
            # 创建模型目录
            model_dir = output_dir / 'modules' / app_name / model.__name__.lower()
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成组件文件
            for filename, content in components.items():
                if filename.endswith('.vue'):
                    file_path = model_dir / 'components' / filename
                elif filename.endswith('.js'):
                    if 'Api' in filename:
                        file_path = model_dir / 'api' / filename
                    elif 'Store' in filename:
                        file_path = model_dir / 'store' / filename
                    elif 'Routes' in filename:
                        file_path = model_dir / 'router' / filename
                    else:
                        file_path = model_dir / filename
                else:
                    file_path = model_dir / filename
                
                # 确保目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 写入文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.stdout.write(f'  ✓ 生成 {file_path}')
                total_files += 1
        
        # 生成模块索引文件
        self.generate_module_index(output_dir / 'modules' / app_name, models_to_generate)
        
        self.stdout.write(self.style.SUCCESS(f'Vue 组件生成完成！共生成 {total_files} 个文件'))
        self.stdout.write('下一步:')
        self.stdout.write('  1. 将生成的组件集成到您的 Vue 项目中')
        self.stdout.write('  2. 安装必要的依赖: npm install axios vuex vue-router')
        self.stdout.write('  3. 配置路由和状态管理')
    
    def generate_graphql_schema(self, options):
        """生成 GraphQL Schema"""
        app_name = options['app']
        output_path = options.get('output')
        
        if not output_path:
            output_path = f'apps/{app_name}/schema.py'
        
        self.stdout.write(f'正在为应用 {app_name} 生成 GraphQL Schema...')
        
        try:
            schema_code = generate_graphql_schema(app_name)
            
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(schema_code)
            
            self.stdout.write(self.style.SUCCESS(f'GraphQL Schema 已生成到: {output_path}'))
            self.stdout.write('下一步:')
            self.stdout.write('  1. 安装 GraphQL 依赖: pip install graphene-django')
            self.stdout.write('  2. 在 settings.py 中配置 GRAPHENE')
            self.stdout.write('  3. 在 urls.py 中添加 GraphQL 端点')
            
        except Exception as e:
            raise CommandError(f'生成 GraphQL Schema 失败: {e}')
    
    def generate_frontend_project(self, options):
        """生成完整前端项目"""
        project_name = options['name']
        framework = options['framework']
        apps_list = options.get('apps', '').split(',') if options.get('apps') else []
        
        self.stdout.write(f'正在生成 {framework} 前端项目: {project_name}')
        
        project_dir = Path(project_name)
        project_dir.mkdir(exist_ok=True)
        
        if framework == 'vue':
            self.generate_vue_project(project_dir, apps_list)
        else:
            raise CommandError(f'暂不支持 {framework} 框架')
        
        self.stdout.write(self.style.SUCCESS(f'前端项目 {project_name} 生成完成！'))
    
    def generate_api_client(self, options):
        """生成 API 客户端"""
        app_name = options['app']
        language = options['language']
        output_dir = Path(options['output'])
        
        self.stdout.write(f'正在为应用 {app_name} 生成 {language} API 客户端...')
        
        try:
            app_config = apps.get_app_config(app_name)
            models = app_config.get_models()
            
            if language == 'javascript':
                self.generate_js_api_client(models, output_dir, app_name)
            elif language == 'typescript':
                self.generate_ts_api_client(models, output_dir, app_name)
            
            self.stdout.write(self.style.SUCCESS(f'API 客户端生成完成！'))
            
        except Exception as e:
            raise CommandError(f'生成 API 客户端失败: {e}')
    
    def generate_vue_project(self, project_dir: Path, apps_list: List[str]):
        """生成 Vue 项目结构"""
        # 创建基础目录结构
        dirs = [
            'src/components',
            'src/pages',
            'src/store',
            'src/api',
            'src/router',
            'src/utils',
            'src/assets',
            'public'
        ]
        
        for dir_path in dirs:
            (project_dir / dir_path).mkdir(parents=True, exist_ok=True)
        
        # 生成 package.json
        package_json = {
            "name": project_dir.name,
            "version": "1.0.0",
            "description": "Auto-generated Vue.js frontend",
            "scripts": {
                "serve": "vue-cli-service serve",
                "build": "vue-cli-service build",
                "lint": "vue-cli-service lint"
            },
            "dependencies": {
                "vue": "^3.0.0",
                "vue-router": "^4.0.0",
                "vuex": "^4.0.0",
                "axios": "^0.24.0",
                "bootstrap": "^5.1.0"
            },
            "devDependencies": {
                "@vue/cli-service": "^4.5.0",
                "@vue/compiler-sfc": "^3.0.0"
            }
        }
        
        with open(project_dir / 'package.json', 'w', encoding='utf-8') as f:
            json.dump(package_json, f, indent=2, ensure_ascii=False)
        
        # 生成主要文件
        self.generate_vue_main_files(project_dir)
        
        # 为每个应用生成组件
        for app_name in apps_list:
            if app_name.strip():
                try:
                    app_config = apps.get_app_config(app_name.strip())
                    models = app_config.get_models()
                    
                    for model in models:
                        generator = VueComponentGenerator(model)
                        components = generator.generate_all_components()
                        
                        # 保存组件到项目目录
                        app_dir = project_dir / 'src' / 'modules' / app_name
                        app_dir.mkdir(parents=True, exist_ok=True)
                        
                        for filename, content in components.items():
                            file_path = app_dir / filename
                            file_path.parent.mkdir(parents=True, exist_ok=True)
                            
                            with open(file_path, 'w', encoding='utf-8') as f:
                                f.write(content)
                
                except LookupError:
                    self.stdout.write(self.style.WARNING(f'应用 {app_name} 不存在，跳过'))
    
    def generate_vue_main_files(self, project_dir: Path):
        """生成 Vue 主要文件"""
        # main.js
        main_js = '''import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import 'bootstrap/dist/css/bootstrap.min.css'

createApp(App).use(store).use(router).mount('#app')
'''
        
        # App.vue
        app_vue = '''<template>
  <div id="app">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container">
        <router-link class="navbar-brand" to="/">管理系统</router-link>
        <div class="navbar-nav">
          <router-link class="nav-link" to="/">首页</router-link>
        </div>
      </div>
    </nav>
    
    <main class="container mt-4">
      <router-view />
    </main>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
'''
        
        # 写入文件
        with open(project_dir / 'src' / 'main.js', 'w', encoding='utf-8') as f:
            f.write(main_js)
        
        with open(project_dir / 'src' / 'App.vue', 'w', encoding='utf-8') as f:
            f.write(app_vue)
    
    def generate_js_api_client(self, models: List[models.Model], output_dir: Path, app_name: str):
        """生成 JavaScript API 客户端"""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for model in models:
            generator = VueComponentGenerator(model)
            api_code = generator.generate_api_service()
            
            filename = f'{model.__name__.lower()}Api.js'
            with open(output_dir / filename, 'w', encoding='utf-8') as f:
                f.write(api_code)
    
    def generate_ts_api_client(self, models: List[models.Model], output_dir: Path, app_name: str):
        """生成 TypeScript API 客户端"""
        # TODO: 实现 TypeScript API 客户端生成
        self.stdout.write(self.style.WARNING('TypeScript API 客户端生成功能正在开发中'))
    
    def generate_module_index(self, module_dir: Path, models: List[models.Model]):
        """生成模块索引文件"""
        index_content = f'''/**
 * 模块索引文件
 * 自动生成的模块导出
 */

// 组件导出
{chr(10).join([f"export {{ default as {model.__name__}List }} from './{model.__name__.lower()}/components/{model.__name__}List.vue'" for model in models])}

// API 导出
{chr(10).join([f"export {{ default as {model.__name__.lower()}Api }} from './{model.__name__.lower()}/api/{model.__name__.lower()}Api.js'" for model in models])}

// Store 导出
{chr(10).join([f"export {{ default as {model.__name__.lower()}Store }} from './{model.__name__.lower()}/store/{model.__name__.lower()}Store.js'" for model in models])}

// 路由导出
{chr(10).join([f"export {{ default as {model.__name__.lower()}Routes }} from './{model.__name__.lower()}/router/{model.__name__.lower()}Routes.js'" for model in models])}
'''
        
        with open(module_dir / 'index.js', 'w', encoding='utf-8') as f:
            f.write(index_content)
    
    def get_model_by_name(self, app_config, model_name: str):
        """根据名称获取模型"""
        for model in app_config.get_models():
            if model.__name__ == model_name:
                return model
        return None
