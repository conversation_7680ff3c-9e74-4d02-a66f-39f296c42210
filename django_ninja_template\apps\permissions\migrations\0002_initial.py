# Generated by Django 5.2.4 on 2025-07-14 08:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("permissions", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="rolepermission",
            name="granted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="granted_permissions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="rolepermission",
            name="permission",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="permissions.permission"
            ),
        ),
        migrations.AddField(
            model_name="rolepermission",
            name="role",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="permissions.role"
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="permissions",
            field=models.ManyToManyField(
                related_name="roles",
                through="permissions.RolePermission",
                to="permissions.permission",
            ),
        ),
        migrations.AddField(
            model_name="userpermission",
            name="granted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="granted_user_permissions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="userpermission",
            name="permission",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="permissions.permission"
            ),
        ),
        migrations.AddField(
            model_name="userpermission",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="custom_user_permissions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="assigned_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="assigned_roles",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="role",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_roles",
                to="permissions.role",
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_roles",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="rolepermission",
            unique_together={("role", "permission")},
        ),
        migrations.AddIndex(
            model_name="userpermission",
            index=models.Index(
                fields=["user", "granted"], name="permissions_user_id_5ec3c1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userpermission",
            index=models.Index(
                fields=["expires_at"], name="permissions_expires_046412_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userpermission",
            unique_together={("user", "permission")},
        ),
        migrations.AddIndex(
            model_name="userrole",
            index=models.Index(
                fields=["user", "is_active"], name="permissions_user_id_0622a8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userrole",
            index=models.Index(
                fields=["expires_at"], name="permissions_expires_0194c8_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userrole",
            unique_together={("user", "role")},
        ),
    ]
