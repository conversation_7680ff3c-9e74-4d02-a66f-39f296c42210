# 🔧 Core应用脚本功能指南

Django Ninja 脚手架项目的 Core 应用提供了强大的脚本工具和自动化功能，帮助开发者快速构建和管理项目。

## 📋 目录

- [项目启动脚本](#项目启动脚本)
- [Django管理命令](#django管理命令)
- [代码生成工具](#代码生成工具)
- [数据管理工具](#数据管理工具)
- [权限管理工具](#权限管理工具)
- [前端生成工具](#前端生成工具)
- [异步任务管理](#异步任务管理)
- [部署和运维脚本](#部署和运维脚本)

## 🚀 项目启动脚本

### Python启动脚本 (start.py)

**功能**: 智能项目启动和环境检查

```bash
# 交互式启动（推荐）
python start.py

# 功能特性：
# ✅ 自动检查虚拟环境
# ✅ 验证依赖安装
# ✅ 创建环境配置文件
# ✅ 引导数据库迁移
# ✅ 创建超级用户
# ✅ 初始化权限系统
# ✅ 启动开发服务器
```

**使用场景**:
- 新项目首次启动
- 开发环境快速配置
- 团队成员环境统一

### 跨平台启动脚本

**Linux/macOS (start.sh)**:
```bash
chmod +x start.sh
./start.sh
```

**Windows (start.bat)**:
```cmd
start.bat
```

**功能对比**:
| 功能 | start.py | start.sh | start.bat |
|------|----------|----------|-----------|
| 虚拟环境检查 | ✅ | ✅ | ✅ |
| 依赖自动安装 | ❌ | ✅ | ✅ |
| 项目配置检查 | ❌ | ✅ | ✅ |
| 交互式引导 | ✅ | ✅ | ✅ |

## 🛠️ Django管理命令

### 代码生成命令

#### 1. 基于配置文件的批量生成

```bash
# 命令格式
python manage.py generate_from_config --config <配置文件> [选项]

# 完整CMS系统生成
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend

# 预览生成内容（不实际创建文件）
python manage.py generate_from_config --config cms_config.yaml --full --dry-run

# 只生成后端代码
python manage.py generate_from_config --config cms_config.yaml --full

# 自定义输出目录
python manage.py generate_from_config --config cms_config.yaml --output-dir my_apps
```

**可用选项**:
- `--config`: YAML配置文件路径（必需）
- `--full`: 生成完整功能（模型、API、Admin、权限、测试）
- `--with-frontend`: 生成前端Vue.js组件
- `--output-dir`: 输出目录（默认: apps）
- `--dry-run`: 预览模式，不实际生成文件

**生成内容**:
- Django模型和迁移文件
- RESTful API接口
- Django Admin配置
- 权限和角色配置
- 单元测试文件
- Vue.js前端组件
- API客户端代码

#### 2. 单个模块生成

```bash
# 生成完整模块
python manage.py generate module blog \
  --fields "title:str,content:text,is_published:bool,author:fk:User" \
  --admin --api

# 生成模型
python manage.py generate model blog Article \
  --fields "title:str,content:text,status:str"

# 生成API
python manage.py generate api blog Article --permissions blog

# 生成权限配置
python manage.py generate permissions blog --models Article,Category
```

**字段类型支持**:
- `str`: CharField
- `text`: TextField
- `int`: IntegerField
- `bool`: BooleanField
- `date`: DateField
- `datetime`: DateTimeField
- `decimal`: DecimalField
- `email`: EmailField
- `url`: URLField
- `file`: FileField
- `image`: ImageField
- `fk:Model`: ForeignKey
- `m2m:Model`: ManyToManyField

### 数据管理命令

#### 种子数据生成

```bash
# 生成演示数据
python manage.py seed_data --demo

# 生成指定数量用户
python manage.py seed_data --users 50

# 为特定应用生成数据
python manage.py seed_data --app blog --count 20

# 为特定模型生成数据
python manage.py seed_data --app blog --model Article --count 10

# 基于配置文件生成
python manage.py seed_data --config seed_config.json

# 清除现有数据
python manage.py seed_data --clear --app blog

# 清除所有生成的数据
python manage.py seed_data --clear-all

# 列出可用模型
python manage.py seed_data --list-models

# 显示数据统计
python manage.py seed_data --stats
```

**配置文件示例** (seed_config.json):
```json
{
  "users": 20,
  "blog": {
    "Article": 50,
    "Category": 10,
    "Tag": 30
  },
  "cms": {
    "Page": 15,
    "Media": 100
  }
}
```

### 权限管理命令

#### 自动权限生成

```bash
# 为应用生成权限
python manage.py auto_generate_permissions blog

# 同时创建标准角色
python manage.py auto_generate_permissions blog --create-roles

# 指定模型
python manage.py auto_generate_permissions blog --models Article,Category

# 基于配置文件
python manage.py auto_generate_permissions blog --config permissions_config.json

# 导出权限配置
python manage.py auto_generate_permissions blog --export blog_permissions.json

# 导入权限配置
python manage.py auto_generate_permissions blog --import blog_permissions.json

# 清理未使用的权限
python manage.py auto_generate_permissions blog --cleanup

# 重置权限（删除后重新创建）
python manage.py auto_generate_permissions blog --reset

# 为用户分配管理员角色
python manage.py auto_generate_permissions blog --assign-user <EMAIL>
```

#### 基础权限设置

```bash
# 设置默认权限和角色
python manage.py setup_permissions

# 重置所有权限和角色
python manage.py setup_permissions --reset
```

### 前端生成命令

#### Vue.js组件生成

```bash
# 为应用生成Vue组件
python manage.py generate_frontend vue blog

# 指定模型
python manage.py generate_frontend vue blog --model Article

# 自定义输出目录
python manage.py generate_frontend vue blog --output frontend/src

# 自定义API前缀
python manage.py generate_frontend vue blog --api-prefix /api/v1
```

#### GraphQL Schema生成

```bash
# 生成GraphQL Schema
python manage.py generate_frontend graphql blog

# 指定输出文件
python manage.py generate_frontend graphql blog --output schema.graphql
```

#### 完整前端项目生成

```bash
# 生成Vue.js项目
python manage.py generate_frontend project --name my-frontend --framework vue

# 包含指定应用
python manage.py generate_frontend project --apps blog,cms,users

# 生成React项目
python manage.py generate_frontend project --framework react
```

#### API客户端生成

```bash
# 生成JavaScript API客户端
python manage.py generate_frontend api blog

# 生成TypeScript API客户端
python manage.py generate_frontend api blog --language typescript

# 自定义输出目录
python manage.py generate_frontend api blog --output api-client
```

## 🔄 异步任务管理

### Celery任务

#### 核心任务 (apps/core/tasks.py)

**日志清理任务**:
```python
# 清理90天前的审计日志
cleanup_old_logs.delay()

# 异步创建审计日志
create_audit_log.delay(
    user_id=1,
    action='create',
    resource_type='Article',
    resource_id='123',
    details={'title': 'New Article'},
    ip_address='***********',
    user_agent='Mozilla/5.0...'
)
```

#### 认证任务 (apps/authentication/tasks.py)

**令牌清理任务**:
```python
# 清理过期令牌和登录记录
cleanup_expired_tokens.delay()

# 发送邮箱验证邮件
send_verification_email.delay(user_id=1)

# 发送密码重置邮件
send_password_reset_email.delay(email='<EMAIL>')

# 发送登录通知
send_login_notification.delay(user_id=1, ip_address='***********')
```

#### 存储任务 (apps/storage/tasks.py)

**文件管理任务**:
```python
# 清理过期文件和分享
cleanup_expired_files.delay()

# 生成文件缩略图
generate_thumbnail.delay(file_id=1)

# 优化图片
optimize_image.delay(file_id=1)

# 病毒扫描
scan_file_for_virus.delay(file_id=1)

# 备份文件到云存储
backup_file_to_cloud.delay(file_id=1)
```

### 定时任务配置

**Celery Beat配置** (core/celery.py):
```python
# 每天凌晨2点清理过期令牌
'cleanup-expired-tokens': {
    'task': 'apps.authentication.tasks.cleanup_expired_tokens',
    'schedule': crontab(hour=2, minute=0),
},

# 每天凌晨3点清理过期文件
'cleanup-expired-files': {
    'task': 'apps.storage.tasks.cleanup_expired_files',
    'schedule': crontab(hour=3, minute=0),
},

# 每周日凌晨1点清理旧日志
'cleanup-old-logs': {
    'task': 'apps.core.tasks.cleanup_old_logs',
    'schedule': crontab(hour=1, minute=0, day_of_week=0),
},
```

### 启动Celery服务

```bash
# 启动Worker
celery -A core worker -l info

# 启动Beat调度器
celery -A core beat -l info

# 启动Flower监控
celery -A core flower

# 后台运行
nohup celery -A core worker -l info &
nohup celery -A core beat -l info &
```

## 🐳 部署和运维脚本

### Docker部署

#### 开发环境

```bash
# 启动开发环境
docker-compose up -d

# 查看日志
docker-compose logs -f web

# 进入容器
docker-compose exec web bash

# 停止服务
docker-compose down
```

#### 生产环境

```bash
# 设置生产环境
export DJANGO_ENVIRONMENT=production

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 健康检查
docker-compose exec web python manage.py check --deploy
```

### 数据库管理

```bash
# 创建迁移文件
python manage.py makemigrations

# 执行迁移
python manage.py migrate

# 回滚迁移
python manage.py migrate app_name 0001

# 查看迁移状态
python manage.py showmigrations

# 数据库备份
python manage.py dumpdata > backup.json

# 数据库恢复
python manage.py loaddata backup.json
```

### 静态文件管理

```bash
# 收集静态文件
python manage.py collectstatic

# 压缩静态文件
python manage.py compress

# 清理静态文件
python manage.py collectstatic --clear
```

### 缓存管理

```bash
# 清理所有缓存
python manage.py clear_cache

# 预热缓存
python manage.py warm_cache

# 查看缓存统计
python manage.py cache_stats
```

## 🔍 监控和诊断

### 健康检查

```bash
# 系统健康检查
python manage.py check

# 部署检查
python manage.py check --deploy

# 数据库连接检查
python manage.py dbshell

# 访问健康检查端点
curl http://localhost:8000/health/
```

### 日志管理

**日志文件位置**:
- 应用日志: `logs/django.log`
- 错误日志: `logs/error.log`
- 访问日志: `logs/access.log`

**日志查看**:
```bash
# 实时查看日志
tail -f logs/django.log

# 查看错误日志
tail -f logs/error.log

# 搜索特定错误
grep "ERROR" logs/django.log
```

## 📊 性能优化

### 数据库优化

```bash
# 分析慢查询
python manage.py analyze_queries

# 数据库索引建议
python manage.py suggest_indexes

# 清理无用数据
python manage.py cleanup_data
```

### 缓存优化

```bash
# 缓存预热
python manage.py warm_cache

# 缓存统计
python manage.py cache_stats

# 清理过期缓存
python manage.py clear_expired_cache
```

## 🚨 故障排除

### 常见问题解决

**数据库连接问题**:
```bash
# 检查数据库配置
python manage.py check --database default

# 测试数据库连接
python manage.py dbshell
```

**权限问题**:
```bash
# 重新初始化权限
python manage.py setup_permissions --reset

# 检查用户权限
python manage.py check_user_permissions <EMAIL>
```

**缓存问题**:
```bash
# 清理所有缓存
python manage.py clear_cache

# 重启Redis
docker-compose restart redis
```

**静态文件问题**:
```bash
# 重新收集静态文件
python manage.py collectstatic --clear --noinput

# 检查静态文件配置
python manage.py check --deploy
```

## 🔧 核心服务类

### 认证服务 (AuthService)

**功能**: 用户认证、令牌管理、登录限制

```python
from apps.authentication.services import AuthService

# 用户认证
user = AuthService.authenticate_user(
    email='<EMAIL>',
    password='password123',
    ip_address='***********',
    user_agent='Mozilla/5.0...'
)

# 生成令牌
tokens = AuthService.generate_tokens(
    user=user,
    device_name='iPhone 12',
    ip_address='***********',
    user_agent='Mozilla/5.0...'
)

# 刷新令牌
new_tokens = AuthService.refresh_token(refresh_token='...')

# 撤销令牌
AuthService.revoke_token(refresh_token='...')

# 撤销用户所有令牌
AuthService.revoke_all_user_tokens(user=user)
```

### 存储服务 (StorageService)

**功能**: 文件上传、管理、分享、安全检查

```python
from apps.storage.services import StorageService

# 文件上传
file_upload = StorageService.upload_file(
    user=user,
    uploaded_file=request.FILES['file'],
    is_public=True,
    folder='documents'
)

# 创建文件分享
share = StorageService.create_share(
    file_upload=file_upload,
    expires_in_days=7,
    password='optional_password',
    max_downloads=10
)

# 获取分享文件
file_data = StorageService.get_shared_file(
    share_token='abc123',
    password='optional_password'
)

# 删除文件
StorageService.delete_file(file_upload)

# 生成缩略图
thumbnail = StorageService.generate_thumbnail(
    file_upload=file_upload,
    size=(200, 200)
)
```

### 权限服务 (PermissionService)

**功能**: 权限检查、角色管理、缓存优化

```python
from apps.permissions.services import PermissionService

# 检查用户权限
has_permission = PermissionService.user_has_permission(
    user=user,
    permission_codename='blog.create'
)

# 获取用户所有权限
permissions = PermissionService.get_user_permissions(user)

# 分配角色给用户
PermissionService.assign_role_to_user(
    user=user,
    role_name='editor',
    assigned_by=admin_user
)

# 移除用户角色
PermissionService.remove_role_from_user(
    user=user,
    role_name='editor',
    removed_by=admin_user
)

# 授予直接权限
PermissionService.grant_permission_to_user(
    user=user,
    permission_codename='blog.manage',
    granted_by=admin_user
)

# 清理权限缓存
PermissionService.clear_user_permission_cache(user)
```

### 国际化服务 (I18nService)

**功能**: 多语言支持、翻译管理、语言切换

```python
from apps.i18n.services import I18nService

# 获取支持的语言
languages = I18nService.get_supported_languages()

# 获取默认语言
default_lang = I18nService.get_default_language()

# 获取翻译
translation = I18nService.get_translation(
    key='welcome_message',
    language='zh-cn',
    default='Welcome!'
)

# 批量获取翻译
translations = I18nService.get_translations(
    keys=['welcome', 'goodbye', 'hello'],
    language='zh-cn'
)

# 设置翻译
I18nService.set_translation(
    key='new_message',
    language='zh-cn',
    value='新消息',
    namespace='ui'
)

# 检测语言
detected_lang = I18nService.detect_language_from_request(request)
```

## 📊 API接口功能

### 健康检查接口

```bash
# 系统健康检查
curl http://localhost:8000/api/core/health

# 响应示例
{
  "status": "healthy",
  "timestamp": "2024-12-07T10:30:00",
  "services": {
    "database": "healthy",
    "cache": "healthy"
  },
  "version": "1.0.0"
}
```

### 系统信息接口

```bash
# 获取系统信息
curl http://localhost:8000/api/core/info

# 响应示例
{
  "application": {
    "name": "Django Ninja CMS",
    "version": "1.0.0",
    "environment": "development"
  },
  "system": {
    "python_version": "3.11.0",
    "platform": "Windows-10",
    "architecture": "64bit"
  }
}
```

### Ping接口

```bash
# 简单连通性测试
curl http://localhost:8000/api/core/ping

# 响应示例
{
  "message": "pong",
  "timestamp": "2024-12-07T10:30:00"
}
```

## 📁 配置文件管理

### CMS配置文件 (cms_config.yaml)

**用途**: 定义CMS模块的模型结构和关系

```yaml
app_name: cms
models:
  Article:
    fields:
      title: str
      slug: str
      content: text
      summary: str
      status: str
      is_featured: bool
      view_count: int
      seo_title: str
      seo_description: text
      author: fk:User
      category: fk:Category
      tags: m2m:Tag
    options:
      ordering: ['-created_at']
      verbose_name: '文章'
```

**使用方法**:
```bash
# 基于配置生成完整CMS系统
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend
```

### 环境配置文件 (.env)

**重要配置项**:
```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/dbname

# Redis配置
REDIS_URL=redis://localhost:6379/1

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# 文件存储配置
DEFAULT_FILE_STORAGE=django.core.files.storage.FileSystemStorage
MEDIA_ROOT=/path/to/media
MEDIA_URL=/media/

# 安全配置
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## 📚 示例代码使用

### 博客示例 (blog_example.py)

**功能**: 展示5分钟创建完整博客模块

```bash
# 1. 查看示例代码
cat examples/blog_example.py

# 2. 按照示例步骤执行
python manage.py generate module blog \
  --fields "title:str,content:text,summary:str,is_published:bool,view_count:int"

# 3. 添加到INSTALLED_APPS
# 编辑 core/settings/base.py，添加 'apps.blog'

# 4. 生成迁移文件
python manage.py makemigrations blog

# 5. 执行迁移
python manage.py migrate

# 6. 生成测试数据
python manage.py seed_data --app blog --count 20
```

### 全栈博客示例 (fullstack_blog_example.py)

**功能**: 展示完整的前后端博客系统开发

```bash
# 1. 生成后端代码
python manage.py generate_from_config --config examples/blog_config.yaml --full

# 2. 生成前端组件
python manage.py generate_frontend vue blog --output frontend/src

# 3. 启动开发服务器
python manage.py runserver

# 4. 访问API文档
# http://localhost:8000/api/docs/

# 5. 测试前端组件
# 在frontend目录下运行npm run serve
```

## 🔧 中间件配置

### CORS中间件 (core/middleware/cors.py)

**功能**: 处理跨域请求

```python
# 在settings中启用
MIDDLEWARE = [
    'core.middleware.cors.CorsMiddleware',
    # ... 其他中间件
]

# 配置CORS设置
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

CORS_ALLOW_CREDENTIALS = True
```

### 日志中间件 (core/middleware/logging.py)

**功能**: 记录请求和响应日志

```python
# 自动记录API请求
# 日志文件: logs/django.log
# 包含: 请求方法、URL、响应状态、处理时间
```

## 📝 最佳实践

### 开发流程建议

1. **项目启动**: 使用 `python start.py` 快速配置环境
2. **功能开发**: 使用代码生成工具快速创建模块
3. **数据填充**: 使用种子数据生成器创建测试数据
4. **权限配置**: 使用自动权限生成器配置访问控制
5. **前端开发**: 使用前端生成工具创建Vue组件
6. **服务集成**: 使用核心服务类处理业务逻辑
7. **测试验证**: 运行自动生成的测试用例
8. **部署上线**: 使用Docker容器化部署

### 性能优化建议

1. **定期清理**: 设置定时任务清理过期数据
2. **缓存策略**: 合理使用Redis缓存提高性能
3. **数据库优化**: 定期分析和优化数据库查询
4. **监控告警**: 配置健康检查和日志监控
5. **备份策略**: 定期备份数据库和重要文件
6. **服务解耦**: 使用服务类分离业务逻辑
7. **异步处理**: 使用Celery处理耗时任务

### 安全建议

1. **权限控制**: 使用权限服务严格控制访问
2. **文件安全**: 使用存储服务验证文件类型和大小
3. **认证安全**: 使用认证服务管理令牌和登录限制
4. **配置安全**: 妥善管理环境变量和密钥
5. **日志监控**: 定期检查访问日志和错误日志

---

**文档版本**: v1.0.0 | **最后更新**: 2024-12-07

更多详细信息请参考：
- [快速开始指南](快速开始指南.md)
- [代码生成工具指南](代码生成工具指南.md)
- [API使用指南](API使用指南.md)
- [开发指南](开发指南.md)
