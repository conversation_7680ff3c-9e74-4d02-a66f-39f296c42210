"""
Pydantic schemas for authentication API.
"""
from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class LoginRequest(BaseModel):
    """Schema for login request."""
    email: EmailStr
    password: str = Field(..., min_length=8)
    device_name: Optional[str] = None


class LoginResponse(BaseModel):
    """Schema for login response."""
    access_token: str
    refresh_token: str
    token_type: str = "Bearer"
    expires_in: int
    user: dict


class RefreshTokenRequest(BaseModel):
    """Schema for refresh token request."""
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    """Schema for refresh token response."""
    access_token: str
    token_type: str = "Bearer"
    expires_in: int


class LogoutRequest(BaseModel):
    """Schema for logout request."""
    refresh_token: str


class RegisterRequest(BaseModel):
    """Schema for user registration."""
    email: EmailStr
    password: str = Field(..., min_length=8)
    first_name: str = Field(..., min_length=1, max_length=150)
    last_name: str = Field(..., min_length=1, max_length=150)
    username: str = Field(..., min_length=3, max_length=150)


class RegisterResponse(BaseModel):
    """Schema for registration response."""
    message: str
    user_id: int


class PasswordResetRequest(BaseModel):
    """Schema for password reset request."""
    email: EmailStr


class PasswordResetConfirmRequest(BaseModel):
    """Schema for password reset confirmation."""
    token: str
    new_password: str = Field(..., min_length=8)


class ChangePasswordRequest(BaseModel):
    """Schema for password change."""
    current_password: str
    new_password: str = Field(..., min_length=8)


class EmailVerificationRequest(BaseModel):
    """Schema for email verification."""
    token: str


class UserProfileResponse(BaseModel):
    """Schema for user profile response."""
    id: int
    email: str
    username: str
    first_name: str
    last_name: str
    is_verified: bool
    is_premium: bool
    created_at: str
    
    class Config:
        from_attributes = True
