"""
Pydantic schemas for i18n API.
"""
from typing import Dict, List, Optional
from pydantic import BaseModel


class LanguageResponse(BaseModel):
    """Schema for language response."""
    code: str
    name: str
    native_name: str
    text_direction: str
    is_default: bool
    
    class Config:
        from_attributes = True


class TranslationResponse(BaseModel):
    """Schema for translation response."""
    key: str
    value: str
    context: str
    language_code: str
    is_approved: bool
    
    class Config:
        from_attributes = True


class TranslationRequest(BaseModel):
    """Schema for translation request."""
    key: str
    value: str
    context: str = ""
    language_code: str


class TranslationBatchRequest(BaseModel):
    """Schema for batch translation request."""
    language_code: str
    translations: Dict[str, str]


class TranslationProgressResponse(BaseModel):
    """Schema for translation progress response."""
    language_code: str
    total_count: int
    translated_count: int
    fuzzy_count: int
    missing_count: int
    progress_percentage: float


class LanguageStatsResponse(BaseModel):
    """Schema for language statistics response."""
    languages: List[TranslationProgressResponse]
    total_keys: int
    total_translations: int
