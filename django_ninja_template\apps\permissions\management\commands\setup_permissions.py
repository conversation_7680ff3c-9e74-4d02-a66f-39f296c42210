"""
Management command to set up default permissions and roles.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.permissions.models import Permission, Role


class Command(BaseCommand):
    help = 'Set up default permissions and roles'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all permissions and roles (WARNING: This will delete existing data)',
        )
    
    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write(
                self.style.WARNING('Resetting all permissions and roles...')
            )
            Permission.objects.all().delete()
            Role.objects.all().delete()
        
        with transaction.atomic():
            self.create_permissions()
            self.create_roles()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up permissions and roles')
        )
    
    def create_permissions(self):
        """Create default permissions."""
        permissions_data = [
            # User management
            ('users.view', 'View Users', 'View user profiles and information', 'users', 'view'),
            ('users.create', 'Create Users', 'Create new user accounts', 'users', 'create'),
            ('users.edit', 'Edit Users', 'Edit user profiles and information', 'users', 'edit'),
            ('users.delete', 'Delete Users', 'Delete user accounts', 'users', 'delete'),
            ('users.manage_roles', 'Manage User Roles', 'Assign and remove roles from users', 'users', 'manage_roles'),
            
            # Role management
            ('roles.view', 'View Roles', 'View roles and their permissions', 'roles', 'view'),
            ('roles.create', 'Create Roles', 'Create new roles', 'roles', 'create'),
            ('roles.edit', 'Edit Roles', 'Edit role permissions and details', 'roles', 'edit'),
            ('roles.delete', 'Delete Roles', 'Delete roles', 'roles', 'delete'),
            
            # Permission management
            ('permissions.view', 'View Permissions', 'View available permissions', 'permissions', 'view'),
            ('permissions.manage', 'Manage Permissions', 'Assign and remove permissions', 'permissions', 'manage'),
            
            # System administration
            ('system.admin', 'System Administration', 'Full system administration access', 'system', 'admin'),
            ('system.config', 'System Configuration', 'Manage system configuration', 'system', 'config'),
            ('system.logs', 'View System Logs', 'View system logs and audit trails', 'system', 'logs'),
            
            # API access
            ('api.read', 'API Read Access', 'Read access to API endpoints', 'api', 'read'),
            ('api.write', 'API Write Access', 'Write access to API endpoints', 'api', 'write'),
            ('api.admin', 'API Admin Access', 'Administrative access to API endpoints', 'api', 'admin'),
            
            # Content management
            ('content.view', 'View Content', 'View content and resources', 'content', 'view'),
            ('content.create', 'Create Content', 'Create new content and resources', 'content', 'create'),
            ('content.edit', 'Edit Content', 'Edit existing content and resources', 'content', 'edit'),
            ('content.delete', 'Delete Content', 'Delete content and resources', 'content', 'delete'),
            ('content.publish', 'Publish Content', 'Publish and unpublish content', 'content', 'publish'),
        ]
        
        for codename, name, description, resource, action in permissions_data:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                defaults={
                    'name': name,
                    'description': description,
                    'resource': resource,
                    'action': action,
                }
            )
            
            if created:
                self.stdout.write(f'Created permission: {permission.name}')
            else:
                self.stdout.write(f'Permission already exists: {permission.name}')
    
    def create_roles(self):
        """Create default roles."""
        roles_data = [
            ('Super Admin', 'Full system access with all permissions', True, [
                'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_roles',
                'roles.view', 'roles.create', 'roles.edit', 'roles.delete',
                'permissions.view', 'permissions.manage',
                'system.admin', 'system.config', 'system.logs',
                'api.read', 'api.write', 'api.admin',
                'content.view', 'content.create', 'content.edit', 'content.delete', 'content.publish',
            ]),
            ('Admin', 'Administrative access with most permissions', True, [
                'users.view', 'users.create', 'users.edit', 'users.manage_roles',
                'roles.view', 'roles.create', 'roles.edit',
                'permissions.view',
                'system.config', 'system.logs',
                'api.read', 'api.write',
                'content.view', 'content.create', 'content.edit', 'content.delete', 'content.publish',
            ]),
            ('Manager', 'Management access with content and user permissions', True, [
                'users.view', 'users.edit',
                'roles.view',
                'permissions.view',
                'api.read', 'api.write',
                'content.view', 'content.create', 'content.edit', 'content.publish',
            ]),
            ('Editor', 'Content editing and management permissions', True, [
                'users.view',
                'api.read', 'api.write',
                'content.view', 'content.create', 'content.edit',
            ]),
            ('User', 'Basic user permissions', True, [
                'api.read',
                'content.view',
            ]),
            ('Guest', 'Read-only access for guests', True, [
                'content.view',
            ]),
        ]
        
        for role_name, description, is_system_role, permission_codenames in roles_data:
            role, created = Role.objects.get_or_create(
                name=role_name,
                defaults={
                    'description': description,
                    'is_system_role': is_system_role,
                }
            )
            
            if created:
                self.stdout.write(f'Created role: {role.name}')
            else:
                self.stdout.write(f'Role already exists: {role.name}')
            
            # Add permissions to role
            for permission_codename in permission_codenames:
                try:
                    permission = Permission.objects.get(codename=permission_codename)
                    role.add_permission(permission)
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {permission_codename}')
                    )
