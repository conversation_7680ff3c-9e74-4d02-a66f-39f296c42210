"""
CORS middleware for handling Cross-Origin Resource Sharing.
"""
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings


class CorsMiddleware(MiddlewareMixin):
    """
    Middleware to handle CORS headers.
    """
    
    def process_response(self, request, response):
        """
        Add CORS headers to the response.
        """
        # Get CORS settings
        allow_all_origins = getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', False)
        allowed_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
        allow_credentials = getattr(settings, 'CORS_ALLOW_CREDENTIALS', False)
        
        # Get origin from request
        origin = request.META.get('HTTP_ORIGIN')
        
        # Set Access-Control-Allow-Origin
        if allow_all_origins:
            response['Access-Control-Allow-Origin'] = '*'
        elif origin and origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
        
        # Set other CORS headers
        if allow_credentials:
            response['Access-Control-Allow-Credentials'] = 'true'
        
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = (
            'Accept, Accept-Language, Content-Language, Content-Type, '
            'Authorization, X-Requested-With, X-CSRFToken'
        )
        response['Access-Control-Max-Age'] = '86400'  # 24 hours
        
        return response
    
    def process_request(self, request):
        """
        Handle preflight OPTIONS requests.
        """
        if request.method == 'OPTIONS':
            from django.http import HttpResponse
            response = HttpResponse()
            return self.process_response(request, response)
        
        return None
