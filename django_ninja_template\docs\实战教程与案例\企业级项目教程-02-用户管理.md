# 👤 企业级项目教程 02 - 用户管理系统深度解析

本教程将深入解析项目内置的企业级用户管理系统，从数据库迁移开始，详细说明功能实现、代码结构和扩展方法。

## 🎯 本教程目标

完成本教程后，您将：
- ✅ 理解用户管理系统的数据库设计和迁移过程
- ✅ 掌握 JWT 认证机制的具体实现代码
- ✅ 深入了解 RBAC 权限系统的代码架构
- ✅ 学会扩展和自定义用户管理功能
- ✅ 理解安全机制的代码实现
- ✅ 掌握性能优化的具体方法

## 📋 前置要求

- 完成 [基础项目搭建](企业级项目教程-01-基础搭建.md)
- 熟悉 Django ORM 和数据库操作
- 了解 Python 装饰器和中间件概念
- 理解 HTTP 认证和 JWT 原理

## 🗄️ 第一步：数据库迁移和模型分析

### 1.1 执行数据库迁移

首先，让我们执行数据库迁移来创建用户管理系统的表结构：

```bash
# 确保在项目根目录
cd django_ninja_cms

# 创建迁移文件（如果还没有）
uv run python manage.py makemigrations

# 执行迁移
uv run python manage.py migrate

# 查看迁移状态
uv run python manage.py showmigrations
```

### 1.2 核心数据模型分析

让我们深入分析用户管理系统的核心数据模型：

#### 用户模型 (User)
```python
# apps/users/models.py
from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    """扩展的用户模型"""
    email = models.EmailField(unique=True, verbose_name="邮箱")
    first_name = models.CharField(max_length=30, verbose_name="名")
    last_name = models.CharField(max_length=30, verbose_name="姓")
    is_email_verified = models.BooleanField(default=False, verbose_name="邮箱已验证")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    USERNAME_FIELD = 'email'  # 使用邮箱作为登录字段
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    class Meta:
        verbose_name = "用户"
        verbose_name_plural = "用户"
        db_table = 'users_user'
```

#### 用户资料模型 (UserProfile)
```python
class UserProfile(models.Model):
    """用户详细资料"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(max_length=500, blank=True, verbose_name="个人简介")
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name="头像")
    phone = models.CharField(max_length=20, blank=True, verbose_name="电话")
    location = models.CharField(max_length=100, blank=True, verbose_name="位置")
    website = models.URLField(blank=True, verbose_name="网站")
    birth_date = models.DateField(null=True, blank=True, verbose_name="生日")

    # 偏好设置
    language = models.CharField(max_length=10, default='zh-cn', verbose_name="语言")
    timezone = models.CharField(max_length=50, default='Asia/Shanghai', verbose_name="时区")
    theme = models.CharField(max_length=20, default='light', verbose_name="主题")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "用户资料"
        verbose_name_plural = "用户资料"
```

#### 权限系统模型

```python
# apps/permissions/models.py
class Permission(models.Model):
    """权限模型"""
    codename = models.CharField(max_length=100, unique=True, verbose_name="权限代码")
    name = models.CharField(max_length=255, verbose_name="权限名称")
    description = models.TextField(blank=True, verbose_name="权限描述")
    content_type = models.CharField(max_length=100, verbose_name="内容类型")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "权限"
        verbose_name_plural = "权限"
        ordering = ['content_type', 'codename']

class Role(models.Model):
    """角色模型"""
    name = models.CharField(max_length=100, verbose_name="角色名称")
    description = models.TextField(blank=True, verbose_name="角色描述")
    permissions = models.ManyToManyField(Permission, blank=True, verbose_name="权限")
    is_system_role = models.BooleanField(default=False, verbose_name="系统角色")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "角色"
        verbose_name_plural = "角色"

class UserRole(models.Model):
    """用户角色关联"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name="角色")
    assigned_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True,
        related_name='assigned_roles', verbose_name="分配者"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name="过期时间")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'role']
        verbose_name = "用户角色"
        verbose_name_plural = "用户角色"
```

### 1.3 数据库关系图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    User     │    │ UserProfile │    │  UserRole   │
├─────────────┤    ├─────────────┤    ├─────────────┤
│ id (PK)     │◄──►│ user_id(FK) │    │ user_id(FK) │◄──┐
│ email       │    │ bio         │    │ role_id(FK) │   │
│ username    │    │ avatar      │    │ assigned_by │   │
│ first_name  │    │ phone       │    │ expires_at  │   │
│ last_name   │    │ location    │    │ is_active   │   │
│ is_active   │    │ website     │    └─────────────┘   │
└─────────────┘    │ birth_date  │                      │
                   │ language    │    ┌─────────────┐   │
                   │ timezone    │    │    Role     │   │
                   │ theme       │    ├─────────────┤   │
                   └─────────────┘    │ id (PK)     │◄──┘
                                      │ name        │
┌─────────────┐                      │ description │
│ Permission  │                      │ is_active   │
├─────────────┤                      └─────────────┘
│ id (PK)     │◄─────────────────────────────┐
│ codename    │                              │
│ name        │                              │ M:N
│ description │                              │
│ content_type│                              │
│ is_active   │                              │
└─────────────┘                              ▼
```

## 🔐 第二步：JWT 认证系统实现解析

### 2.1 JWT 认证中间件实现

让我们深入了解 JWT 认证的具体实现：

```python
# apps/authentication/middleware.py
import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from .models import TokenBlacklist

User = get_user_model()

class JWTAuthenticationMiddleware:
    """JWT 认证中间件"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 从请求头获取令牌
        token = self.get_token_from_request(request)

        if token:
            try:
                # 验证令牌
                user, payload = self.verify_token(token)
                request.user = user
                request.auth = payload
            except ValueError as e:
                # 令牌无效，返回错误
                return JsonResponse({'error': str(e)}, status=401)

        response = self.get_response(request)
        return response

    def get_token_from_request(self, request):
        """从请求头提取 JWT 令牌"""
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header.split(' ')[1]
        return None

    def verify_token(self, token):
        """验证 JWT 令牌"""
        # 检查令牌是否在黑名单中
        if TokenBlacklist.objects.filter(token=token).exists():
            raise ValueError("令牌已失效")

        try:
            # 解码令牌
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=['HS256']
            )

            # 获取用户
            user = User.objects.get(id=payload['user_id'])
            if not user.is_active:
                raise ValueError("用户已被禁用")

            return user, payload

        except jwt.ExpiredSignatureError:
            raise ValueError("令牌已过期")
        except jwt.InvalidTokenError:
            raise ValueError("无效令牌")
        except User.DoesNotExist:
            raise ValueError("用户不存在")
```

### 2.2 认证服务层实现

```python
# apps/authentication/services.py
import jwt
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password, check_password
from .models import TokenBlacklist

User = get_user_model()

class AuthService:
    """认证服务类"""

    @staticmethod
    def generate_tokens(user):
        """生成访问令牌和刷新令牌"""
        now = datetime.utcnow()

        # 访问令牌（15分钟有效）
        access_payload = {
            'user_id': user.id,
            'email': user.email,
            'exp': now + timedelta(minutes=15),
            'iat': now,
            'type': 'access'
        }
        access_token = jwt.encode(
            access_payload,
            settings.SECRET_KEY,
            algorithm='HS256'
        )

        # 刷新令牌（7天有效）
        refresh_payload = {
            'user_id': user.id,
            'exp': now + timedelta(days=7),
            'iat': now,
            'type': 'refresh'
        }
        refresh_token = jwt.encode(
            refresh_payload,
            settings.SECRET_KEY,
            algorithm='HS256'
        )

        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'Bearer',
            'expires_in': 900  # 15分钟
        }

    @staticmethod
    def refresh_access_token(refresh_token):
        """使用刷新令牌生成新的访问令牌"""
        try:
            payload = jwt.decode(
                refresh_token,
                settings.SECRET_KEY,
                algorithms=['HS256']
            )

            if payload.get('type') != 'refresh':
                raise ValueError("无效的刷新令牌")

            user = User.objects.get(id=payload['user_id'])
            return AuthService.generate_tokens(user)

        except jwt.ExpiredSignatureError:
            raise ValueError("刷新令牌已过期")
        except jwt.InvalidTokenError:
            raise ValueError("无效的刷新令牌")
        except User.DoesNotExist:
            raise ValueError("用户不存在")

    @staticmethod
    def logout_user(token):
        """用户登出，将令牌加入黑名单"""
        TokenBlacklist.objects.create(token=token)
```

### 2.3 认证 API 实现

```python
# apps/authentication/api.py
from ninja import Router
from django.contrib.auth import authenticate
from django.http import HttpRequest
from .schemas import LoginSchema, RegisterSchema, TokenResponse
from .services import AuthService

auth_router = Router(tags=["认证"])

@auth_router.post("/login", response=TokenResponse)
def login(request: HttpRequest, data: LoginSchema):
    """用户登录"""
    user = authenticate(
        request,
        username=data.email,
        password=data.password
    )

    if user and user.is_active:
        tokens = AuthService.generate_tokens(user)
        return TokenResponse(**tokens)
    else:
        raise ValueError("邮箱或密码错误")

@auth_router.post("/register", response=dict)
def register(request: HttpRequest, data: RegisterSchema):
    """用户注册"""
    # 检查邮箱是否已存在
    if User.objects.filter(email=data.email).exists():
        raise ValueError("邮箱已被注册")

    # 创建用户
    user = User.objects.create_user(
        username=data.username,
        email=data.email,
        password=data.password,
        first_name=data.first_name,
        last_name=data.last_name
    )

    return {"message": "注册成功", "user_id": user.id}

@auth_router.post("/refresh", response=TokenResponse)
def refresh_token(request: HttpRequest, refresh_token: str):
    """刷新访问令牌"""
    tokens = AuthService.refresh_access_token(refresh_token)
    return TokenResponse(**tokens)

@auth_router.post("/logout")
def logout(request: HttpRequest):
    """用户登出"""
    if hasattr(request, 'auth') and request.auth:
        AuthService.logout_user(request.auth.get('token'))
    return {"message": "登出成功"}
```

## 🔒 第三步：权限系统深度实现

### 3.1 权限服务层实现

```python
# apps/permissions/services.py
from django.contrib.auth import get_user_model
from django.core.cache import cache
from .models import Permission, Role, UserRole

User = get_user_model()

class PermissionService:
    """权限服务类"""

    @staticmethod
    def user_has_permission(user, permission_codename):
        """检查用户是否有指定权限"""
        if user.is_superuser:
            return True

        # 使用缓存提高性能
        cache_key = f"user_permissions_{user.id}"
        user_permissions = cache.get(cache_key)

        if user_permissions is None:
            user_permissions = PermissionService._get_user_permissions(user)
            cache.set(cache_key, user_permissions, 300)  # 缓存5分钟

        return permission_codename in user_permissions

    @staticmethod
    def _get_user_permissions(user):
        """获取用户所有权限（内部方法）"""
        # 通过角色获取权限
        user_roles = UserRole.objects.filter(
            user=user,
            role__is_active=True,
            is_active=True
        ).select_related('role').prefetch_related('role__permissions')

        permissions = set()
        for user_role in user_roles:
            # 检查角色是否过期
            if user_role.expires_at and user_role.expires_at < timezone.now():
                continue

            role_permissions = user_role.role.permissions.filter(
                is_active=True
            ).values_list('codename', flat=True)
            permissions.update(role_permissions)

        return list(permissions)

    @staticmethod
    def assign_role_to_user(user, role, assigned_by=None, expires_at=None):
        """为用户分配角色"""
        user_role, created = UserRole.objects.get_or_create(
            user=user,
            role=role,
            defaults={
                'assigned_by': assigned_by,
                'expires_at': expires_at
            }
        )

        # 清除用户权限缓存
        cache_key = f"user_permissions_{user.id}"
        cache.delete(cache_key)

        return user_role
```

### 3.2 权限装饰器实现

```python
# apps/permissions/decorators.py
from functools import wraps
from django.http import JsonResponse
from .services import PermissionService

def require_permissions(*permission_codenames):
    """权限检查装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否已认证
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return JsonResponse(
                    {"error": "需要登录", "code": "AUTHENTICATION_REQUIRED"},
                    status=401
                )

            # 检查权限
            for permission_codename in permission_codenames:
                if not PermissionService.user_has_permission(
                    request.user,
                    permission_codename
                ):
                    return JsonResponse(
                        {
                            "error": f"缺少权限: {permission_codename}",
                            "code": "PERMISSION_DENIED"
                        },
                        status=403
                    )

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_any_permission(*permission_codenames):
    """要求任一权限的装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return JsonResponse(
                    {"error": "需要登录"},
                    status=401
                )

            # 检查是否有任一权限
            has_permission = any(
                PermissionService.user_has_permission(request.user, perm)
                for perm in permission_codenames
            )

            if not has_permission:
                return JsonResponse(
                    {"error": "权限不足"},
                    status=403
                )

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
```

## 👤 第四步：用户管理系统实现

### 4.1 用户服务层实现

```python
# apps/users/services.py
from django.contrib.auth import get_user_model
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from .models import UserProfile
import uuid
import os

User = get_user_model()

class UserService:
    """用户服务类"""

    @staticmethod
    def get_or_create_profile(user):
        """获取或创建用户资料"""
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'bio': '',
                'language': 'zh-cn',
                'timezone': 'Asia/Shanghai',
                'theme': 'light'
            }
        )
        return profile

    @staticmethod
    def update_user_profile(user, profile_data):
        """更新用户资料"""
        # 更新用户基本信息
        user_fields = ['first_name', 'last_name', 'email']
        for field in user_fields:
            if field in profile_data:
                setattr(user, field, profile_data[field])
        user.save()

        # 更新用户详细资料
        profile = UserService.get_or_create_profile(user)
        profile_fields = ['bio', 'phone', 'location', 'website',
                         'birth_date', 'language', 'timezone', 'theme']
        for field in profile_fields:
            if field in profile_data:
                setattr(profile, field, profile_data[field])
        profile.save()

        return user

    @staticmethod
    def upload_avatar(user, avatar_file):
        """上传用户头像"""
        # 生成唯一文件名
        file_extension = os.path.splitext(avatar_file.name)[1]
        filename = f"avatars/{user.id}_{uuid.uuid4()}{file_extension}"

        # 保存文件
        file_path = default_storage.save(filename, ContentFile(avatar_file.read()))

        # 更新用户资料
        profile = UserService.get_or_create_profile(user)

        # 删除旧头像
        if profile.avatar:
            default_storage.delete(profile.avatar.name)

        profile.avatar = file_path
        profile.save()

        return default_storage.url(file_path)
```

### 4.2 用户 API 实现

```python
# apps/users/api.py
from ninja import Router, File, UploadedFile
from django.http import HttpRequest
from django.contrib.auth import get_user_model
from apps.permissions.decorators import require_permissions
from .schemas import UserProfileResponse, UserUpdateSchema
from .services import UserService

User = get_user_model()
users_router = Router(tags=["用户管理"])

@users_router.get("/me", response=UserProfileResponse)
def get_current_user(request: HttpRequest):
    """获取当前用户信息"""
    if not request.user.is_authenticated:
        raise ValueError("需要登录")

    profile = UserService.get_or_create_profile(request.user)
    return {
        "id": request.user.id,
        "email": request.user.email,
        "username": request.user.username,
        "first_name": request.user.first_name,
        "last_name": request.user.last_name,
        "profile": profile,
        "created_at": request.user.date_joined
    }

@users_router.put("/me", response=UserProfileResponse)
def update_profile(request: HttpRequest, data: UserUpdateSchema):
    """更新用户资料"""
    if not request.user.is_authenticated:
        raise ValueError("需要登录")

    user = UserService.update_user_profile(
        request.user,
        data.dict(exclude_unset=True)
    )

    profile = UserService.get_or_create_profile(user)
    return {
        "id": user.id,
        "email": user.email,
        "username": user.username,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "profile": profile,
        "created_at": user.date_joined
    }

@users_router.post("/upload-avatar")
def upload_avatar(request: HttpRequest, avatar: UploadedFile = File(...)):
    """上传用户头像"""
    if not request.user.is_authenticated:
        raise ValueError("需要登录")

    # 验证文件类型
    allowed_types = ['image/jpeg', 'image/png', 'image/gif']
    if avatar.content_type not in allowed_types:
        raise ValueError("只支持 JPEG、PNG、GIF 格式的图片")

    # 验证文件大小（最大5MB）
    if avatar.size > 5 * 1024 * 1024:
        raise ValueError("文件大小不能超过5MB")

    avatar_url = UserService.upload_avatar(request.user, avatar)
    return {"avatar_url": avatar_url}

@users_router.get("/", response=list[UserProfileResponse])
@require_permissions("users.view")
def list_users(request: HttpRequest):
    """获取用户列表（需要权限）"""
    users = User.objects.filter(is_active=True).select_related('profile')

    result = []
    for user in users:
        profile = UserService.get_or_create_profile(user)
        result.append({
            "id": user.id,
            "email": user.email,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "profile": profile,
            "created_at": user.date_joined
        })

    return result
```

## 🔧 第五步：系统扩展和自定义

### 5.1 添加自定义权限

```python
# 在 Django shell 中执行
from apps.permissions.models import Permission

# 创建自定义权限
custom_permissions = [
    {
        'codename': 'reports.view',
        'name': '查看报表',
        'description': '允许查看系统报表',
        'content_type': 'reports'
    },
    {
        'codename': 'reports.export',
        'name': '导出报表',
        'description': '允许导出报表数据',
        'content_type': 'reports'
    },
    {
        'codename': 'analytics.access',
        'name': '访问分析',
        'description': '允许访问数据分析功能',
        'content_type': 'analytics'
    }
]

for perm_data in custom_permissions:
    Permission.objects.get_or_create(**perm_data)
```

### 5.2 创建自定义角色

```python
from apps.permissions.models import Role, Permission

# 创建报表管理员角色
reports_admin_role = Role.objects.create(
    name="报表管理员",
    description="负责系统报表管理的角色",
    is_system_role=False
)

# 分配权限给角色
report_permissions = Permission.objects.filter(
    codename__in=["reports.view", "reports.export", "analytics.access"]
)
reports_admin_role.permissions.set(report_permissions)

# 创建数据分析师角色
analyst_role = Role.objects.create(
    name="数据分析师",
    description="专门进行数据分析的角色",
    is_system_role=False
)

# 分配权限
analyst_permissions = Permission.objects.filter(
    codename__in=["analytics.access", "reports.view", "users.view"]
)
analyst_role.permissions.set(analyst_permissions)
```

### 5.3 扩展用户模型

如果需要为用户添加更多字段：

```python
# apps/users/models.py
class UserProfile(models.Model):
    # 现有字段...

    # 新增业务字段
    employee_id = models.CharField(max_length=20, blank=True, verbose_name="员工编号")
    department = models.CharField(max_length=100, blank=True, verbose_name="部门")
    position = models.CharField(max_length=100, blank=True, verbose_name="职位")
    manager = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='subordinates', verbose_name="直属上级"
    )

    # 扩展偏好设置
    notification_email = models.BooleanField(default=True, verbose_name="邮件通知")
    notification_sms = models.BooleanField(default=False, verbose_name="短信通知")

    # 业务状态
    is_on_leave = models.BooleanField(default=False, verbose_name="是否请假")
    leave_start_date = models.DateField(null=True, blank=True, verbose_name="请假开始日期")
    leave_end_date = models.DateField(null=True, blank=True, verbose_name="请假结束日期")

# 创建迁移文件
# python manage.py makemigrations users
# python manage.py migrate
```

### 5.4 自定义权限检查逻辑

```python
# apps/permissions/services.py
class PermissionService:
    # 现有方法...

    @staticmethod
    def user_can_access_department_data(user, department_name):
        """检查用户是否可以访问特定部门数据"""
        # 超级用户可以访问所有数据
        if user.is_superuser:
            return True

        # 检查用户是否有全局数据访问权限
        if PermissionService.user_has_permission(user, 'data.access_all'):
            return True

        # 检查用户是否属于该部门
        profile = getattr(user, 'profile', None)
        if profile and profile.department == department_name:
            return True

        # 检查用户是否是该部门的管理者
        if PermissionService.user_has_permission(user, 'data.access_managed_departments'):
            # 查找用户管理的部门
            managed_departments = User.objects.filter(
                profile__manager=user
            ).values_list('profile__department', flat=True).distinct()

            if department_name in managed_departments:
                return True

        return False

    @staticmethod
    def get_user_accessible_departments(user):
        """获取用户可访问的部门列表"""
        if user.is_superuser or PermissionService.user_has_permission(user, 'data.access_all'):
            # 返回所有部门
            return UserProfile.objects.values_list('department', flat=True).distinct()

        accessible_departments = set()

        # 用户自己的部门
        profile = getattr(user, 'profile', None)
        if profile and profile.department:
            accessible_departments.add(profile.department)

        # 用户管理的部门
        if PermissionService.user_has_permission(user, 'data.access_managed_departments'):
            managed_departments = User.objects.filter(
                profile__manager=user
            ).values_list('profile__department', flat=True)
            accessible_departments.update(managed_departments)

        return list(accessible_departments)
```

### 5.5 创建自定义 API 端点

```python
# apps/reports/api.py (新建应用)
from ninja import Router
from django.http import HttpRequest
from apps.permissions.decorators import require_permissions
from apps.permissions.services import PermissionService

reports_router = Router(tags=["报表管理"])

@reports_router.get("/department-stats")
@require_permissions("reports.view")
def get_department_stats(request: HttpRequest, department: str = None):
    """获取部门统计数据"""
    # 检查用户是否可以访问指定部门数据
    if department and not PermissionService.user_can_access_department_data(
        request.user, department
    ):
        raise ValueError("无权访问该部门数据")

    # 获取用户可访问的部门
    accessible_departments = PermissionService.get_user_accessible_departments(request.user)

    if department:
        departments = [department] if department in accessible_departments else []
    else:
        departments = accessible_departments

    # 生成统计数据
    stats = []
    for dept in departments:
        dept_users = User.objects.filter(profile__department=dept)
        stats.append({
            "department": dept,
            "total_users": dept_users.count(),
            "active_users": dept_users.filter(is_active=True).count(),
            "on_leave_users": dept_users.filter(profile__is_on_leave=True).count()
        })

    return {"departments": stats}

@reports_router.get("/export-users")
@require_permissions("reports.export")
def export_users(request: HttpRequest, format: str = "csv"):
    """导出用户数据"""
    # 获取用户可访问的部门
    accessible_departments = PermissionService.get_user_accessible_departments(request.user)

    users = User.objects.filter(
        profile__department__in=accessible_departments
    ).select_related('profile')

    if format == "csv":
        # 生成 CSV 数据
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="users.csv"'

        writer = csv.writer(response)
        writer.writerow(['ID', '邮箱', '姓名', '部门', '职位', '状态'])

        for user in users:
            writer.writerow([
                user.id,
                user.email,
                f"{user.last_name}{user.first_name}",
                getattr(user.profile, 'department', ''),
                getattr(user.profile, 'position', ''),
                '激活' if user.is_active else '禁用'
            ])

        return response

    # 返回 JSON 格式
    return {
        "users": [
            {
                "id": user.id,
                "email": user.email,
                "name": f"{user.last_name}{user.first_name}",
                "department": getattr(user.profile, 'department', ''),
                "position": getattr(user.profile, 'position', ''),
                "is_active": user.is_active
            }
            for user in users
        ]
    }
```

## 🚀 第六步：实际部署和测试

### 6.1 执行迁移和初始化数据

```bash
# 1. 执行数据库迁移
uv run python manage.py migrate

# 2. 创建超级用户（如果还没有）
uv run python manage.py createsuperuser

# 3. 初始化权限和角色数据
uv run python manage.py shell
```

```python
# 在 Django shell 中执行
from apps.permissions.models import Permission, Role
from django.contrib.auth import get_user_model

User = get_user_model()

# 创建基础权限
base_permissions = [
    # 用户管理权限
    {'codename': 'users.view', 'name': '查看用户', 'content_type': 'users'},
    {'codename': 'users.create', 'name': '创建用户', 'content_type': 'users'},
    {'codename': 'users.edit', 'name': '编辑用户', 'content_type': 'users'},
    {'codename': 'users.delete', 'name': '删除用户', 'content_type': 'users'},

    # 角色管理权限
    {'codename': 'roles.view', 'name': '查看角色', 'content_type': 'roles'},
    {'codename': 'roles.create', 'name': '创建角色', 'content_type': 'roles'},
    {'codename': 'roles.edit', 'name': '编辑角色', 'content_type': 'roles'},
    {'codename': 'roles.assign', 'name': '分配角色', 'content_type': 'roles'},

    # 系统管理权限
    {'codename': 'system.admin', 'name': '系统管理', 'content_type': 'system'},
    {'codename': 'system.settings', 'name': '系统设置', 'content_type': 'system'},
]

for perm_data in base_permissions:
    Permission.objects.get_or_create(**perm_data)

# 创建角色
admin_role = Role.objects.create(
    name="系统管理员",
    description="拥有所有系统权限",
    is_system_role=True
)
admin_role.permissions.set(Permission.objects.all())

user_role = Role.objects.create(
    name="普通用户",
    description="基础用户权限",
    is_system_role=True
)
user_role.permissions.set(Permission.objects.filter(codename='users.view'))
```

### 6.2 测试 API 功能

```bash
# 启动开发服务器
uv run python manage.py runserver

# 测试用户登录
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "your_password"}'

# 使用返回的令牌测试权限
TOKEN="your_access_token_here"

# 测试获取用户列表
curl -X GET http://localhost:8000/api/users/ \
  -H "Authorization: Bearer $TOKEN"

# 测试权限检查
curl -X GET http://localhost:8000/api/permissions/check-permission/users.view \
  -H "Authorization: Bearer $TOKEN"

# 测试角色分配
curl -X POST http://localhost:8000/api/permissions/users/assign-role \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_id": 2, "role_id": 1}'
```

## 📈 第七步：性能优化和监控

### 7.1 数据库查询优化

```python
# apps/permissions/services.py
class PermissionService:
    @staticmethod
    def get_users_with_permissions(permission_codenames):
        """批量获取具有指定权限的用户"""
        # 使用 prefetch_related 优化查询
        users_with_roles = User.objects.filter(
            userrole__role__permissions__codename__in=permission_codenames,
            userrole__is_active=True,
            userrole__role__is_active=True
        ).prefetch_related(
            'userrole_set__role__permissions'
        ).distinct()

        return users_with_roles

    @staticmethod
    def bulk_assign_roles(user_role_assignments):
        """批量分配角色"""
        # user_role_assignments: [{'user_id': 1, 'role_id': 2}, ...]
        user_roles = []
        for assignment in user_role_assignments:
            user_roles.append(UserRole(
                user_id=assignment['user_id'],
                role_id=assignment['role_id'],
                assigned_by_id=assignment.get('assigned_by_id')
            ))

        # 批量创建，提高性能
        UserRole.objects.bulk_create(
            user_roles,
            ignore_conflicts=True  # 忽略重复的分配
        )

        # 清除相关用户的权限缓存
        user_ids = [ur.user_id for ur in user_roles]
        cache_keys = [f"user_permissions_{uid}" for uid in user_ids]
        cache.delete_many(cache_keys)
```

### 7.2 缓存策略优化

```python
# apps/permissions/cache.py
from django.core.cache import cache
from django.conf import settings
import hashlib

class PermissionCache:
    """权限缓存管理"""

    CACHE_TIMEOUT = getattr(settings, 'PERMISSION_CACHE_TIMEOUT', 300)  # 5分钟

    @staticmethod
    def get_user_permissions_cache_key(user_id):
        """生成用户权限缓存键"""
        return f"user_permissions_{user_id}"

    @staticmethod
    def get_role_permissions_cache_key(role_id):
        """生成角色权限缓存键"""
        return f"role_permissions_{role_id}"

    @staticmethod
    def cache_user_permissions(user_id, permissions):
        """缓存用户权限"""
        cache_key = PermissionCache.get_user_permissions_cache_key(user_id)
        cache.set(cache_key, permissions, PermissionCache.CACHE_TIMEOUT)

    @staticmethod
    def get_cached_user_permissions(user_id):
        """获取缓存的用户权限"""
        cache_key = PermissionCache.get_user_permissions_cache_key(user_id)
        return cache.get(cache_key)

    @staticmethod
    def invalidate_user_permissions(user_id):
        """清除用户权限缓存"""
        cache_key = PermissionCache.get_user_permissions_cache_key(user_id)
        cache.delete(cache_key)

    @staticmethod
    def invalidate_role_permissions(role_id):
        """清除角色权限缓存"""
        cache_key = PermissionCache.get_role_permissions_cache_key(role_id)
        cache.delete(cache_key)

        # 同时清除所有拥有该角色的用户权限缓存
        user_ids = UserRole.objects.filter(
            role_id=role_id, is_active=True
        ).values_list('user_id', flat=True)

        for user_id in user_ids:
            PermissionCache.invalidate_user_permissions(user_id)
```

### 7.3 监控和日志

```python
# apps/core/monitoring.py
import logging
from django.utils import timezone
from django.db import models

logger = logging.getLogger(__name__)

class UserActionLog(models.Model):
    """用户操作日志"""
    user = models.ForeignKey('users.User', on_delete=models.CASCADE)
    action = models.CharField(max_length=100, verbose_name="操作类型")
    resource = models.CharField(max_length=100, verbose_name="资源类型")
    resource_id = models.CharField(max_length=50, blank=True, verbose_name="资源ID")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(verbose_name="用户代理")
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "用户操作日志"
        verbose_name_plural = "用户操作日志"
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
        ]

def log_user_action(user, action, resource, resource_id=None, request=None):
    """记录用户操作"""
    ip_address = None
    user_agent = ""

    if request:
        ip_address = request.META.get('REMOTE_ADDR')
        user_agent = request.META.get('HTTP_USER_AGENT', '')

    UserActionLog.objects.create(
        user=user,
        action=action,
        resource=resource,
        resource_id=str(resource_id) if resource_id else "",
        ip_address=ip_address,
        user_agent=user_agent
    )

    logger.info(f"User {user.email} performed {action} on {resource} {resource_id}")

# 在 API 中使用
@users_router.put("/me")
def update_profile(request: HttpRequest, data: UserUpdateSchema):
    # ... 更新逻辑 ...

    # 记录操作日志
    log_user_action(
        user=request.user,
        action="profile_update",
        resource="user_profile",
        resource_id=request.user.id,
        request=request
    )

    return response
```

## ✅ 完成检查和总结

完成本教程后，您应该已经深入理解：

### 🏗️ 系统架构理解
- [x] **数据库模型设计** - User、UserProfile、Role、Permission 关系
- [x] **JWT 认证机制** - 令牌生成、验证、刷新、黑名单
- [x] **RBAC 权限系统** - 角色权限分配、权限检查、缓存优化
- [x] **服务层架构** - AuthService、PermissionService、UserService
- [x] **API 层设计** - 路由、装饰器、错误处理

### 💻 代码实现掌握
- [x] **模型定义** - 字段设计、关系配置、元数据设置
- [x] **中间件实现** - JWT 认证中间件的具体代码
- [x] **装饰器开发** - 权限检查装饰器的实现原理
- [x] **服务类编写** - 业务逻辑封装和代码复用
- [x] **API 端点开发** - 请求处理、响应格式、错误处理

### 🔧 扩展能力培养
- [x] **自定义权限** - 创建新权限、角色、权限分配
- [x] **模型扩展** - 添加新字段、关系、业务逻辑
- [x] **API 扩展** - 新端点开发、复杂权限检查
- [x] **性能优化** - 数据库查询优化、缓存策略
- [x] **监控日志** - 操作记录、性能监控

### 🚀 生产就绪特性
- [x] **安全机制** - 密码策略、令牌安全、权限控制
- [x] **性能优化** - 查询优化、缓存机制、批量操作
- [x] **可扩展性** - 模块化设计、服务分层、接口抽象
- [x] **监控日志** - 操作审计、错误跟踪、性能监控

## 🎯 下一步学习路径

基于本教程的深度理解，您可以继续学习：

1. **[内容管理系统](企业级项目教程-03-内容管理.md)** - 应用权限系统到内容管理
2. **[文件管理系统](企业级项目教程-04-文件管理.md)** - 文件权限和安全上传
3. **[API 网关设计](企业级项目教程-05-API网关.md)** - 微服务架构和权限传递
4. **[缓存架构设计](企业级项目教程-06-缓存架构.md)** - 分布式缓存和性能优化
5. **[生产部署实战](企业级项目教程-07-生产部署.md)** - Docker、K8s、监控部署

## 📚 核心技术掌握

### 设计模式应用
- **服务层模式** - 业务逻辑封装
- **装饰器模式** - 权限检查、日志记录
- **策略模式** - 权限验证策略
- **观察者模式** - 权限变更通知

### 性能优化技术
- **数据库优化** - 索引设计、查询优化、连接池
- **缓存策略** - 多级缓存、缓存失效、缓存预热
- **异步处理** - 任务队列、异步权限检查
- **批量操作** - 批量权限分配、批量数据处理

---

**🎉 恭喜！您已经深度掌握了企业级用户管理系统的设计与实现！**

通过本教程，您不仅了解了系统的使用方法，更重要的是理解了其内部实现原理、设计思路和扩展方法。这为您开发更复杂的企业级应用奠定了坚实的基础。
