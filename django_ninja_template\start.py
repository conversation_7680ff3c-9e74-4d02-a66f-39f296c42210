#!/usr/bin/env python
"""
Django Ninja 模板项目启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """主启动函数"""
    print("🚀 Django Ninja 模板项目启动脚本")
    print("=" * 50)
    
    # 设置环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
    
    # 检查是否在虚拟环境中
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  警告：建议在虚拟环境中运行此项目")
        print("   创建虚拟环境：python -m venv .venv")
        print("   激活虚拟环境：.venv\\Scripts\\activate (Windows) 或 source .venv/bin/activate (Linux/Mac)")
        print()
    
    # 检查依赖
    try:
        import django
        print(f"✅ Django {django.get_version()} 已安装")
    except ImportError:
        print("❌ Django 未安装，请运行：pip install -r requirements.txt")
        return
    
    try:
        import ninja
        print("✅ Django Ninja 已安装")
    except ImportError:
        print("❌ Django Ninja 未安装，请运行：pip install -r requirements.txt")
        return
    
    # 检查环境配置文件
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠️  .env 文件不存在，正在从模板创建...")
        env_example = Path('.env.example')
        if env_example.exists():
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ 已创建 .env 文件，请根据需要修改配置")
        else:
            print("❌ .env.example 文件不存在")
    else:
        print("✅ .env 配置文件存在")
    
    print("\n📋 可用命令：")
    print("1. 数据库迁移：python manage.py migrate")
    print("2. 创建超级用户：python manage.py createsuperuser")
    print("3. 初始化权限：python manage.py setup_permissions")
    print("4. 启动开发服务器：python manage.py runserver")
    print("5. 启动 Celery Worker：celery -A core worker -l info")
    print("6. 启动 Celery Beat：celery -A core beat -l info")
    
    print("\n🌐 服务地址：")
    print("- 应用首页：http://localhost:8000/")
    print("- API 文档：http://localhost:8000/api/docs/")
    print("- 管理后台：http://localhost:8000/admin/")
    print("- 健康检查：http://localhost:8000/health/")
    
    print("\n🔧 快速启动：")
    choice = input("是否执行数据库迁移？(y/n): ").lower().strip()
    if choice == 'y':
        print("正在执行数据库迁移...")
        subprocess.run([sys.executable, 'manage.py', 'migrate'])
        
        choice = input("是否创建超级用户？(y/n): ").lower().strip()
        if choice == 'y':
            subprocess.run([sys.executable, 'manage.py', 'createsuperuser'])
        
        choice = input("是否初始化权限和角色？(y/n): ").lower().strip()
        if choice == 'y':
            subprocess.run([sys.executable, 'manage.py', 'setup_permissions'])
        
        choice = input("是否启动开发服务器？(y/n): ").lower().strip()
        if choice == 'y':
            print("正在启动开发服务器...")
            print("按 Ctrl+C 停止服务器")
            subprocess.run([sys.executable, 'manage.py', 'runserver'])
    
    print("\n🎉 项目设置完成！")
    print("📚 更多信息请查看：")
    print("- README.md - 项目介绍")
    print("- docs/INSTALLATION.md - 安装指南")
    print("- docs/API_GUIDE.md - API 使用指南")

if __name__ == '__main__':
    main()
