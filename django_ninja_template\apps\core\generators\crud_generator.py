"""
自动 CRUD 生成器
基于 Django 模型自动生成标准的 CRUD API 接口
"""
from typing import Type, Dict, Any, List, Optional, Union
from django.db import models
from django.shortcuts import get_object_or_404
from django.db.models import Q
from ninja import Router, Query, Schema
from ninja.pagination import paginate, PageNumberPagination
from pydantic import BaseModel, Field, create_model
from apps.authentication.auth import JWTAuth
from apps.permissions.decorators import require_permission
import inspect
from datetime import datetime


class CRUDConfig:
    """CRUD 配置类"""
    
    def __init__(
        self,
        model: Type[models.Model],
        permissions_prefix: str,
        list_fields: Optional[List[str]] = None,
        search_fields: Optional[List[str]] = None,
        filter_fields: Optional[List[str]] = None,
        ordering: Optional[List[str]] = None,
        page_size: int = 20,
        enable_soft_delete: bool = False,
        enable_bulk_operations: bool = False,
        auto_permissions: bool = True,
        exclude_fields: Optional[List[str]] = None,
        readonly_fields: Optional[List[str]] = None,
    ):
        self.model = model
        self.permissions_prefix = permissions_prefix
        self.list_fields = list_fields or self._get_default_list_fields()
        self.search_fields = search_fields or self._get_default_search_fields()
        self.filter_fields = filter_fields or self._get_default_filter_fields()
        self.ordering = ordering or ['-created_at', '-id']
        self.page_size = page_size
        self.enable_soft_delete = enable_soft_delete
        self.enable_bulk_operations = enable_bulk_operations
        self.auto_permissions = auto_permissions
        self.exclude_fields = exclude_fields or ['password', 'created_at', 'updated_at']
        self.readonly_fields = readonly_fields or ['id', 'created_at', 'updated_at']
    
    def _get_default_list_fields(self) -> List[str]:
        """获取默认列表字段"""
        fields = []
        for field in self.model._meta.fields:
            if not field.name.endswith('_id') and field.name not in self.exclude_fields:
                fields.append(field.name)
                if len(fields) >= 8:  # 限制默认显示字段数量
                    break
        return fields
    
    def _get_default_search_fields(self) -> List[str]:
        """获取默认搜索字段"""
        search_fields = []
        for field in self.model._meta.fields:
            if isinstance(field, (models.CharField, models.TextField)):
                if field.name not in ['password', 'token']:
                    search_fields.append(field.name)
        return search_fields
    
    def _get_default_filter_fields(self) -> List[str]:
        """获取默认过滤字段"""
        filter_fields = []
        for field in self.model._meta.fields:
            if isinstance(field, (models.BooleanField, models.DateTimeField, models.ForeignKey)):
                filter_fields.append(field.name)
        return filter_fields


class SchemaGenerator:
    """Pydantic 模式生成器"""
    
    def __init__(self, model: Type[models.Model], config: CRUDConfig):
        self.model = model
        self.config = config
        self.model_name = model.__name__
    
    def generate_schemas(self) -> Dict[str, Type[BaseModel]]:
        """生成所有需要的 Pydantic 模式"""
        return {
            'create': self._generate_create_schema(),
            'update': self._generate_update_schema(),
            'output': self._generate_output_schema(),
            'filter': self._generate_filter_schema(),
        }
    
    def _generate_create_schema(self) -> Type[BaseModel]:
        """生成创建模式"""
        fields = {}
        
        for field in self.model._meta.fields:
            if self._should_include_in_create(field):
                field_type, field_info = self._get_pydantic_field(field, required=not field.null)
                fields[field.name] = (field_type, field_info)
        
        # 处理多对多字段
        for field in self.model._meta.many_to_many:
            if field.name not in self.config.exclude_fields:
                fields[f'{field.name}_ids'] = (List[int], Field(default=[], description=f'{field.verbose_name} IDs'))
        
        schema_name = f'{self.model_name}Create'
        return create_model(schema_name, **fields)
    
    def _generate_update_schema(self) -> Type[BaseModel]:
        """生成更新模式"""
        fields = {}
        
        for field in self.model._meta.fields:
            if self._should_include_in_update(field):
                field_type, field_info = self._get_pydantic_field(field, required=False)
                fields[field.name] = (Optional[field_type], field_info)
        
        # 处理多对多字段
        for field in self.model._meta.many_to_many:
            if field.name not in self.config.exclude_fields:
                fields[f'{field.name}_ids'] = (Optional[List[int]], Field(default=None, description=f'{field.verbose_name} IDs'))
        
        schema_name = f'{self.model_name}Update'
        return create_model(schema_name, **fields)
    
    def _generate_output_schema(self) -> Type[BaseModel]:
        """生成输出模式"""
        fields = {}
        
        for field in self.model._meta.fields:
            if field.name not in self.config.exclude_fields:
                field_type, field_info = self._get_pydantic_field(field, required=True)
                fields[field.name] = (field_type, field_info)
        
        schema_name = f'{self.model_name}Out'
        schema = create_model(schema_name, **fields)
        
        # 添加 Config
        class Config:
            from_attributes = True
        
        schema.Config = Config
        return schema
    
    def _generate_filter_schema(self) -> Type[BaseModel]:
        """生成过滤模式"""
        fields = {}
        
        # 添加搜索字段
        if self.config.search_fields:
            fields['search'] = (Optional[str], Field(None, description='搜索关键词'))
        
        # 添加过滤字段
        for field_name in self.config.filter_fields:
            try:
                field = self.model._meta.get_field(field_name)
                field_type, field_info = self._get_pydantic_field(field, required=False)
                fields[field_name] = (Optional[field_type], field_info)
            except:
                continue
        
        # 添加分页参数
        fields['page'] = (Optional[int], Field(1, ge=1, description='页码'))
        fields['per_page'] = (Optional[int], Field(self.config.page_size, ge=1, le=100, description='每页数量'))
        
        schema_name = f'{self.model_name}Filter'
        return create_model(schema_name, **fields)
    
    def _should_include_in_create(self, field) -> bool:
        """判断字段是否应该包含在创建模式中"""
        if field.name in self.config.exclude_fields:
            return False
        if field.name in self.config.readonly_fields:
            return False
        if isinstance(field, models.AutoField):
            return False
        if field.auto_now_add or field.auto_now:
            return False
        return True
    
    def _should_include_in_update(self, field) -> bool:
        """判断字段是否应该包含在更新模式中"""
        if field.name in self.config.exclude_fields:
            return False
        if field.name in self.config.readonly_fields:
            return False
        if isinstance(field, models.AutoField):
            return False
        if field.auto_now_add:
            return False
        return True
    
    def _get_pydantic_field(self, field, required: bool = True):
        """获取 Pydantic 字段类型和信息"""
        field_info = Field(description=field.verbose_name or field.name)
        
        if isinstance(field, models.CharField):
            if field.max_length:
                field_info = Field(max_length=field.max_length, description=field.verbose_name or field.name)
            return str, field_info
        elif isinstance(field, models.TextField):
            return str, field_info
        elif isinstance(field, models.IntegerField):
            return int, field_info
        elif isinstance(field, models.FloatField):
            return float, field_info
        elif isinstance(field, models.BooleanField):
            return bool, field_info
        elif isinstance(field, models.DateTimeField):
            return datetime, field_info
        elif isinstance(field, models.EmailField):
            return str, Field(pattern=r'^[^@]+@[^@]+\.[^@]+$', description=field.verbose_name or field.name)
        elif isinstance(field, models.URLField):
            return str, Field(pattern=r'^https?://.+', description=field.verbose_name or field.name)
        elif isinstance(field, models.ForeignKey):
            return int, Field(description=f'{field.verbose_name or field.name} ID')
        elif isinstance(field, models.JSONField):
            return dict, field_info
        else:
            return str, field_info


class CRUDGenerator:
    """自动 CRUD 生成器"""
    
    def __init__(self, model: Type[models.Model], config: Union[CRUDConfig, Dict[str, Any]]):
        self.model = model
        
        if isinstance(config, dict):
            self.config = CRUDConfig(model, **config)
        else:
            self.config = config
        
        self.schema_generator = SchemaGenerator(model, self.config)
        self.schemas = self.schema_generator.generate_schemas()
        self.router = Router(tags=[f"{model.__name__} Management"])
    
    def generate_crud_router(self) -> Router:
        """生成完整的 CRUD 路由"""
        self._generate_list_endpoint()
        self._generate_create_endpoint()
        self._generate_detail_endpoint()
        self._generate_update_endpoint()
        self._generate_delete_endpoint()
        
        if self.config.enable_bulk_operations:
            self._generate_bulk_endpoints()
        
        return self.router
    
    def _generate_list_endpoint(self):
        """生成列表接口"""
        model = self.model
        config = self.config
        filter_schema = self.schemas['filter']
        output_schema = self.schemas['output']
        
        @self.router.get("/", response=List[output_schema], auth=JWTAuth())
        @paginate(PageNumberPagination, page_size=config.page_size)
        @require_permission(f'{config.permissions_prefix}.view')
        def list_items(request, filters: filter_schema = Query(...)):
            """获取项目列表"""
            queryset = model.objects.all()
            
            # 应用搜索
            if hasattr(filters, 'search') and filters.search:
                search_q = Q()
                for field in config.search_fields:
                    search_q |= Q(**{f'{field}__icontains': filters.search})
                queryset = queryset.filter(search_q)
            
            # 应用过滤
            for field_name in config.filter_fields:
                if hasattr(filters, field_name):
                    value = getattr(filters, field_name)
                    if value is not None:
                        queryset = queryset.filter(**{field_name: value})
            
            # 应用排序
            if config.ordering:
                queryset = queryset.order_by(*config.ordering)
            
            # 优化查询
            queryset = self._optimize_queryset(queryset)
            
            return queryset
    
    def _generate_create_endpoint(self):
        """生成创建接口"""
        model = self.model
        config = self.config
        create_schema = self.schemas['create']
        output_schema = self.schemas['output']
        
        @self.router.post("/", response=output_schema, auth=JWTAuth())
        @require_permission(f'{config.permissions_prefix}.create')
        def create_item(request, payload: create_schema):
            """创建项目"""
            data = payload.dict()
            
            # 处理多对多字段
            m2m_data = {}
            for field in model._meta.many_to_many:
                field_key = f'{field.name}_ids'
                if field_key in data:
                    m2m_data[field.name] = data.pop(field_key)
            
            # 创建对象
            instance = model.objects.create(**data)
            
            # 设置多对多关系
            for field_name, ids in m2m_data.items():
                if ids:
                    getattr(instance, field_name).set(ids)
            
            return instance
    
    def _generate_detail_endpoint(self):
        """生成详情接口"""
        model = self.model
        config = self.config
        output_schema = self.schemas['output']
        
        @self.router.get("/{item_id}", response=output_schema, auth=JWTAuth())
        @require_permission(f'{config.permissions_prefix}.view')
        def get_item(request, item_id: int):
            """获取项目详情"""
            queryset = model.objects.all()
            queryset = self._optimize_queryset(queryset)
            return get_object_or_404(queryset, id=item_id)
    
    def _generate_update_endpoint(self):
        """生成更新接口"""
        model = self.model
        config = self.config
        update_schema = self.schemas['update']
        output_schema = self.schemas['output']
        
        @self.router.put("/{item_id}", response=output_schema, auth=JWTAuth())
        @require_permission(f'{config.permissions_prefix}.edit')
        def update_item(request, item_id: int, payload: update_schema):
            """更新项目"""
            instance = get_object_or_404(model, id=item_id)
            data = payload.dict(exclude_unset=True)
            
            # 处理多对多字段
            m2m_data = {}
            for field in model._meta.many_to_many:
                field_key = f'{field.name}_ids'
                if field_key in data:
                    m2m_data[field.name] = data.pop(field_key)
            
            # 更新字段
            for field, value in data.items():
                setattr(instance, field, value)
            instance.save()
            
            # 更新多对多关系
            for field_name, ids in m2m_data.items():
                if ids is not None:
                    getattr(instance, field_name).set(ids)
            
            return instance
    
    def _generate_delete_endpoint(self):
        """生成删除接口"""
        model = self.model
        config = self.config
        
        @self.router.delete("/{item_id}", auth=JWTAuth())
        @require_permission(f'{config.permissions_prefix}.delete')
        def delete_item(request, item_id: int):
            """删除项目"""
            instance = get_object_or_404(model, id=item_id)
            
            if config.enable_soft_delete and hasattr(instance, 'is_deleted'):
                instance.is_deleted = True
                instance.save()
            else:
                instance.delete()
            
            return {"message": "删除成功"}
    
    def _generate_bulk_endpoints(self):
        """生成批量操作接口"""
        model = self.model
        config = self.config
        
        @self.router.post("/bulk-delete", auth=JWTAuth())
        @require_permission(f'{config.permissions_prefix}.delete')
        def bulk_delete(request, ids: List[int]):
            """批量删除"""
            queryset = model.objects.filter(id__in=ids)
            
            if config.enable_soft_delete:
                queryset.update(is_deleted=True)
            else:
                count = queryset.count()
                queryset.delete()
                return {"message": f"成功删除 {count} 个项目"}
            
            return {"message": f"成功删除 {len(ids)} 个项目"}
    
    def _optimize_queryset(self, queryset):
        """优化查询集"""
        # 自动添加 select_related 和 prefetch_related
        select_related_fields = []
        prefetch_related_fields = []
        
        for field in self.model._meta.fields:
            if isinstance(field, models.ForeignKey):
                select_related_fields.append(field.name)
        
        for field in self.model._meta.many_to_many:
            prefetch_related_fields.append(field.name)
        
        if select_related_fields:
            queryset = queryset.select_related(*select_related_fields)
        
        if prefetch_related_fields:
            queryset = queryset.prefetch_related(*prefetch_related_fields)
        
        return queryset


# 便捷函数
def create_crud_router(model: Type[models.Model], permissions_prefix: str, **kwargs) -> Router:
    """创建 CRUD 路由的便捷函数"""
    config = CRUDConfig(model, permissions_prefix, **kwargs)
    generator = CRUDGenerator(model, config)
    return generator.generate_crud_router()


def create_crud_from_config(model: Type[models.Model], config_dict: Dict[str, Any]) -> Router:
    """从配置字典创建 CRUD 路由"""
    generator = CRUDGenerator(model, config_dict)
    return generator.generate_crud_router()
