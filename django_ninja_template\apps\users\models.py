"""
应用程序的用户模型。
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    扩展 Django AbstractUser 的自定义用户模型。
    """
    email = models.EmailField(_('email address'), unique=True)
    first_name = models.CharField(_('first name'), max_length=150)
    last_name = models.CharField(_('last name'), max_length=150)
    phone = models.CharField(_('phone number'), max_length=20, blank=True)
    avatar = models.ImageField(_('avatar'), upload_to='avatars/', blank=True, null=True)
    bio = models.TextField(_('biography'), max_length=500, blank=True)
    birth_date = models.DateField(_('birth date'), blank=True, null=True)
    
    # 账户状态
    is_verified = models.BooleanField(_('verified'), default=False)
    is_premium = models.<PERSON><PERSON>anField(_('premium user'), default=False)

    # 时间戳
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    last_login_ip = models.GenericIPAddressField(_('last login IP'), blank=True, null=True)

    # 设置
    timezone = models.CharField(_('timezone'), max_length=50, default='UTC')
    language = models.CharField(_('language'), max_length=10, default='en')
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        db_table = 'users'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['username']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"
    
    def get_full_name(self):
        """
        返回名字加姓氏，中间用空格分隔。
        """
        full_name = f"{self.first_name} {self.last_name}"
        return full_name.strip()

    def get_short_name(self):
        """
        返回用户的简短名称。
        """
        return self.first_name


class UserProfile(models.Model):
    """
    扩展的用户资料信息。
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile'
    )
    
    # 社交链接
    website = models.URLField(_('website'), blank=True)
    github = models.CharField(_('GitHub username'), max_length=100, blank=True)
    linkedin = models.CharField(_('LinkedIn username'), max_length=100, blank=True)
    twitter = models.CharField(_('Twitter username'), max_length=100, blank=True)

    # 职业信息
    company = models.CharField(_('company'), max_length=200, blank=True)
    job_title = models.CharField(_('job title'), max_length=200, blank=True)
    location = models.CharField(_('location'), max_length=200, blank=True)

    # 偏好设置
    email_notifications = models.BooleanField(_('email notifications'), default=True)
    push_notifications = models.BooleanField(_('push notifications'), default=True)
    marketing_emails = models.BooleanField(_('marketing emails'), default=False)

    # 隐私设置
    profile_visibility = models.CharField(
        _('profile visibility'),
        max_length=20,
        choices=[
            ('public', _('Public')),
            ('private', _('Private')),
            ('friends', _('Friends Only')),
        ],
        default='public'
    )
    
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('User Profile')
        verbose_name_plural = _('User Profiles')
        db_table = 'user_profiles'
    
    def __str__(self):
        return f"{self.user.get_full_name()}'s Profile"
