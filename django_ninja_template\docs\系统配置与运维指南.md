# 🔧 系统配置与运维指南

本文档涵盖了Django Ninja 脚手架项目的系统配置、运维管理和高级功能的详细操作说明。

## 📋 目录

- [Celery异步任务配置](#celery异步任务配置)
- [Nginx反向代理配置](#nginx反向代理配置)
- [环境变量管理](#环境变量管理)
- [数据库配置与优化](#数据库配置与优化)
- [缓存配置与管理](#缓存配置与管理)
- [日志配置与监控](#日志配置与监控)
- [安全配置](#安全配置)
- [性能监控](#性能监控)

## 🔄 Celery异步任务配置

### 基础配置 (core/celery.py)

**任务队列配置**:
```python
# 任务路由配置
task_routes = {
    'apps.core.tasks.*': {'queue': 'core'},
    'apps.users.tasks.*': {'queue': 'users'},
    'apps.authentication.tasks.*': {'queue': 'auth'},
    'apps.storage.tasks.*': {'queue': 'storage'},
}

# 任务执行配置
task_soft_time_limit = 300  # 5分钟软限制
task_time_limit = 600       # 10分钟硬限制
worker_prefetch_multiplier = 1
worker_max_tasks_per_child = 1000
```

### 启动Celery服务

**开发环境**:
```bash
# 启动Worker（处理任务）
celery -A core worker -l info

# 启动Beat（定时任务调度器）
celery -A core beat -l info

# 启动Flower（监控界面）
celery -A core flower --port=5555

# 同时启动多个队列
celery -A core worker -Q core,users,auth -l info
```

**生产环境**:
```bash
# 使用systemd管理Celery服务
sudo systemctl start celery-worker
sudo systemctl start celery-beat
sudo systemctl enable celery-worker
sudo systemctl enable celery-beat

# 查看服务状态
sudo systemctl status celery-worker
sudo systemctl status celery-beat
```

### 定时任务管理

**查看定时任务**:
```bash
# 列出所有定时任务
celery -A core inspect scheduled

# 查看活跃任务
celery -A core inspect active

# 查看已注册任务
celery -A core inspect registered
```

**手动执行任务**:
```bash
# 执行清理过期令牌任务
celery -A core call apps.authentication.tasks.cleanup_expired_tokens

# 执行清理过期文件任务
celery -A core call apps.storage.tasks.cleanup_expired_files

# 执行清理旧日志任务
celery -A core call apps.core.tasks.cleanup_old_logs
```

## 🌐 Nginx反向代理配置

### 基础配置 (nginx.conf)

**性能优化配置**:
```nginx
# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript 
           application/javascript application/xml+rss application/json;

# 连接配置
worker_connections 1024;
keepalive_timeout 65;
client_max_body_size 100M;
```

**安全配置**:
```nginx
# 安全头
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# 速率限制
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
```

**静态文件配置**:
```nginx
# 静态文件缓存
location /static/ {
    alias /app/staticfiles/;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 媒体文件配置
location /media/ {
    alias /app/media/;
    expires 1y;
    add_header Cache-Control "public";
}
```

### SSL/HTTPS配置

**Let's Encrypt证书**:
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d yourdomain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

**Nginx HTTPS配置**:
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
}
```

## 🔐 环境变量管理

### 开发环境 (.env)

```bash
# 基础配置
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# Redis配置
REDIS_URL=redis://localhost:6379/1
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True

# 文件存储配置
DEFAULT_FILE_STORAGE=django.core.files.storage.FileSystemStorage
MEDIA_ROOT=/path/to/media
MEDIA_URL=/media/

# 云存储配置（可选）
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# 第三方服务配置
SENTRY_DSN=https://your-sentry-dsn
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
```

### 生产环境配置

```bash
# 安全配置
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# 数据库配置（生产）
DATABASE_URL=*****************************************/production_db

# 缓存配置
REDIS_URL=redis://redis-server:6379/1

# 邮件配置（生产）
EMAIL_HOST=smtp.mailgun.org
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-mailgun-password

# 监控配置
SENTRY_DSN=https://your-production-sentry-dsn
SENTRY_ENVIRONMENT=production

# 安全配置
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
```

## 🗄️ 数据库配置与优化

### PostgreSQL配置

**连接池配置**:
```python
# settings/production.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT', '5432'),
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        },
        'CONN_MAX_AGE': 600,
    }
}
```

**数据库优化命令**:
```bash
# 分析数据库性能
python manage.py dbshell
EXPLAIN ANALYZE SELECT * FROM your_table WHERE condition;

# 重建索引
REINDEX DATABASE your_database;

# 更新统计信息
ANALYZE;

# 清理无用数据
VACUUM FULL;
```

### 数据库备份与恢复

**自动备份脚本**:
```bash
#!/bin/bash
# backup_db.sh

DB_NAME="your_database"
DB_USER="your_user"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份
pg_dump -U $DB_USER -h localhost $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: backup_$DATE.sql.gz"
```

**恢复数据库**:
```bash
# 从备份恢复
gunzip backup_20241207_120000.sql.gz
psql -U your_user -d your_database < backup_20241207_120000.sql

# 使用Django命令恢复
python manage.py loaddata backup.json
```

## 🚀 缓存配置与管理

### Redis配置

**Redis配置文件** (redis.conf):
```conf
# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
bind 127.0.0.1
port 6379
timeout 300

# 安全配置
requirepass your-redis-password
```

### Django缓存配置

```python
# settings/base.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://127.0.0.1:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            },
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
        },
        'KEY_PREFIX': 'django_ninja_cms',
        'TIMEOUT': 300,
    }
}

# 会话缓存
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

### 缓存管理命令

```bash
# 清理所有缓存
python manage.py clear_cache

# 查看缓存统计
redis-cli info memory
redis-cli info stats

# 监控Redis
redis-cli monitor

# 查看特定键
redis-cli keys "django_ninja_cms:*"

# 删除特定模式的键
redis-cli --scan --pattern "django_ninja_cms:user:*" | xargs redis-cli del
```

## 📊 日志配置与监控

### Django日志配置

```python
# settings/base.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/error.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'apps': {
            'handlers': ['file', 'error_file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

### 日志监控脚本

```bash
#!/bin/bash
# monitor_logs.sh

LOG_FILE="/path/to/logs/django.log"
ERROR_LOG="/path/to/logs/error.log"

# 监控错误日志
tail -f $ERROR_LOG | while read line; do
    echo "ERROR: $line"
    # 发送告警邮件或通知
    echo "$line" | mail -s "Django Error Alert" <EMAIL>
done &

# 监控访问日志
tail -f $LOG_FILE | grep "ERROR\|CRITICAL" | while read line; do
    echo "CRITICAL: $line"
    # 发送紧急通知
done
```

## 🔒 安全配置

### Django安全设置

```python
# settings/production.py

# HTTPS配置
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Cookie安全
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# 内容安全策略
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# 其他安全设置
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
ALLOWED_HOSTS = ['yourdomain.com', 'www.yourdomain.com']
```

### 防火墙配置

```bash
# UFW防火墙配置
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 允许特定IP访问数据库
sudo ufw allow from ************* to any port 5432

# 查看防火墙状态
sudo ufw status verbose
```

## 📈 性能监控

### 系统监控脚本

```bash
#!/bin/bash
# system_monitor.sh

# CPU使用率
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')

# 内存使用率
MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f"), $3/$2 * 100.0}')

# 磁盘使用率
DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}')

# 数据库连接数
DB_CONNECTIONS=$(psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;" -t)

echo "CPU: ${CPU_USAGE}%, Memory: ${MEM_USAGE}%, Disk: ${DISK_USAGE}, DB Connections: ${DB_CONNECTIONS}"

# 如果使用率过高，发送告警
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "High CPU usage: ${CPU_USAGE}%" | mail -s "CPU Alert" <EMAIL>
fi
```

### Django性能监控

```python
# 自定义中间件监控响应时间
class PerformanceMonitoringMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()
        response = self.get_response(request)
        end_time = time.time()
        
        duration = end_time - start_time
        
        # 记录慢请求
        if duration > 1.0:  # 超过1秒的请求
            logger.warning(f"Slow request: {request.path} took {duration:.2f}s")
        
        return response
```

---

**文档版本**: v1.0.0 | **最后更新**: 2024-12-07

更多详细信息请参考：
- [Core应用脚本功能指南](Core应用脚本功能指南.md)
- [开发指南](开发指南.md)
- [API使用指南](API使用指南.md)
