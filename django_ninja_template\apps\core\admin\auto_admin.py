"""
智能 Admin 配置生成器
基于模型字段自动生成 Django Admin 配置
"""

from django.contrib import admin
from django.db import models
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from typing import Type, List, Dict, Any, Optional, Callable
import inspect


class AdminConfig:
    """Admin 配置类"""

    def __init__(
        self,
        list_display: Optional[List[str]] = None,
        list_filter: Optional[List[str]] = None,
        search_fields: Optional[List[str]] = None,
        readonly_fields: Optional[List[str]] = None,
        list_per_page: int = 25,
        ordering: Optional[List[str]] = None,
        enable_actions: bool = True,
        enable_export: bool = True,
        enable_import: bool = False,
        date_hierarchy: Optional[str] = None,
        list_editable: Optional[List[str]] = None,
        exclude_fields: Optional[List[str]] = None,
        fieldsets: Optional[List[tuple]] = None,
        inlines: Optional[List] = None,
        custom_actions: Optional[List[Callable]] = None,
    ):
        self.list_display = list_display
        self.list_filter = list_filter
        self.search_fields = search_fields
        self.readonly_fields = readonly_fields
        self.list_per_page = list_per_page
        self.ordering = ordering
        self.enable_actions = enable_actions
        self.enable_export = enable_export
        self.enable_import = enable_import
        self.date_hierarchy = date_hierarchy
        self.list_editable = list_editable
        self.exclude_fields = exclude_fields or []
        self.fieldsets = fieldsets
        self.inlines = inlines or []
        self.custom_actions = custom_actions or []


class AutoAdminGenerator:
    """自动 Admin 配置生成器"""

    @staticmethod
    def register_model(model: Type[models.Model], config: Optional[AdminConfig] = None):
        """自动注册模型到 Admin"""
        if config is None:
            config = AdminConfig()

        # 生成 Admin 类
        admin_class = AutoAdminGenerator._create_admin_class(model, config)

        # 注册到 admin
        try:
            admin.site.register(model, admin_class)
        except admin.sites.AlreadyRegistered:
            admin.site.unregister(model)
            admin.site.register(model, admin_class)

    @staticmethod
    def _create_admin_class(
        model: Type[models.Model], config: AdminConfig
    ) -> Type[admin.ModelAdmin]:
        """创建 Admin 类"""
        model_name = model.__name__
        admin_class_name = f"{model_name}Admin"

        # 生成基础配置
        admin_attrs = AutoAdminGenerator._generate_admin_config(model, config)

        # 添加自定义方法
        admin_attrs.update(AutoAdminGenerator._generate_admin_methods(model, config))

        # 创建 Admin 类
        admin_class = type(admin_class_name, (admin.ModelAdmin,), admin_attrs)

        return admin_class

    @staticmethod
    def _generate_admin_config(
        model: Type[models.Model], config: AdminConfig
    ) -> Dict[str, Any]:
        """生成 Admin 配置"""
        model_fields = [f.name for f in model._meta.fields]

        admin_config = {
            "list_display": config.list_display
            or AutoAdminGenerator._get_list_display(model),
            "list_filter": config.list_filter
            or AutoAdminGenerator._get_list_filter(model),
            "search_fields": config.search_fields
            or AutoAdminGenerator._get_search_fields(model),
            "readonly_fields": config.readonly_fields
            or AutoAdminGenerator._get_readonly_fields(model),
            "list_per_page": config.list_per_page,
            "ordering": config.ordering or AutoAdminGenerator._get_ordering(model),
        }

        # 可选配置
        if config.date_hierarchy:
            admin_config["date_hierarchy"] = config.date_hierarchy
        elif "created_at" in model_fields:
            admin_config["date_hierarchy"] = "created_at"

        if config.list_editable:
            admin_config["list_editable"] = config.list_editable

        if config.fieldsets:
            admin_config["fieldsets"] = config.fieldsets
        else:
            admin_config["fieldsets"] = AutoAdminGenerator._generate_fieldsets(
                model, config
            )

        if config.inlines:
            admin_config["inlines"] = config.inlines

        # 添加动作
        if config.enable_actions:
            actions = ["make_active", "make_inactive"]
            if config.enable_export:
                actions.append("export_as_csv")
            if config.custom_actions:
                actions.extend([action.__name__ for action in config.custom_actions])
            admin_config["actions"] = actions

        return admin_config

    @staticmethod
    def _generate_admin_methods(
        model: Type[models.Model], config: AdminConfig
    ) -> Dict[str, Any]:
        """生成 Admin 方法"""
        methods = {}

        # 添加标准动作
        if config.enable_actions:
            methods.update(
                {
                    "make_active": AutoAdminGenerator._make_active,
                    "make_inactive": AutoAdminGenerator._make_inactive,
                }
            )

            if config.enable_export:
                methods["export_as_csv"] = AutoAdminGenerator._export_as_csv

        # 添加自定义动作
        for action in config.custom_actions:
            methods[action.__name__] = action

        # 添加显示方法
        methods.update(AutoAdminGenerator._generate_display_methods(model))

        return methods

    @staticmethod
    def _get_list_display(model: Type[models.Model]) -> List[str]:
        """获取列表显示字段"""
        fields = []
        model_fields = model._meta.fields

        # 优先显示的字段类型
        priority_fields = []
        normal_fields = []

        for field in model_fields:
            if field.name in ["id", "name", "title", "username", "email"]:
                priority_fields.append(field.name)
            elif not field.name.endswith("_id") and not isinstance(
                field, (models.TextField,)
            ):
                normal_fields.append(field.name)

        # 组合字段，限制数量
        fields.extend(priority_fields)
        fields.extend(normal_fields[: 6 - len(priority_fields)])

        # 添加状态字段
        if hasattr(model, "is_active"):
            fields.append("is_active_display")

        # 添加时间字段
        if hasattr(model, "created_at"):
            fields.append("created_at")

        return fields[:8]  # 限制最多8个字段

    @staticmethod
    def _get_list_filter(model: Type[models.Model]) -> List[str]:
        """获取过滤字段"""
        filter_fields = []

        for field in model._meta.fields:
            if isinstance(field, models.BooleanField):
                filter_fields.append(field.name)
            elif isinstance(field, models.DateTimeField) and field.name in [
                "created_at",
                "updated_at",
            ]:
                filter_fields.append(field.name)
            elif isinstance(field, models.ForeignKey):
                filter_fields.append(field.name)
            elif isinstance(field, models.CharField) and field.choices:
                filter_fields.append(field.name)

        return filter_fields[:5]  # 限制过滤字段数量

    @staticmethod
    def _get_search_fields(model: Type[models.Model]) -> List[str]:
        """获取搜索字段"""
        search_fields = []

        for field in model._meta.fields:
            if isinstance(field, models.CharField) and field.name not in [
                "password",
                "token",
            ]:
                if field.name in ["name", "title", "username", "email"]:
                    search_fields.append(field.name)
                elif len(search_fields) < 3:
                    search_fields.append(field.name)

        return search_fields

    @staticmethod
    def _get_readonly_fields(model: Type[models.Model]) -> List[str]:
        """获取只读字段"""
        readonly_fields = []

        for field in model._meta.fields:
            if field.name in ["id", "created_at", "updated_at"]:
                readonly_fields.append(field.name)
            elif hasattr(field, "auto_now") and (field.auto_now or field.auto_now_add):
                readonly_fields.append(field.name)

        return readonly_fields

    @staticmethod
    def _get_ordering(model: Type[models.Model]) -> List[str]:
        """获取排序字段"""
        if hasattr(model, "created_at"):
            return ["-created_at"]
        elif hasattr(model, "id"):
            return ["-id"]
        else:
            return []

    @staticmethod
    def _generate_fieldsets(
        model: Type[models.Model], config: AdminConfig
    ) -> List[tuple]:
        """生成字段集"""
        all_fields = [
            f.name for f in model._meta.fields if f.name not in config.exclude_fields
        ]
        readonly_fields = (
            config.readonly_fields or AutoAdminGenerator._get_readonly_fields(model)
        )

        # 基础信息字段
        basic_fields = []
        for field_name in ["name", "title", "username", "email", "description"]:
            if field_name in all_fields:
                basic_fields.append(field_name)

        # 其他字段
        other_fields = [
            f for f in all_fields if f not in basic_fields and f not in readonly_fields
        ]

        fieldsets = []

        if basic_fields:
            fieldsets.append(("基础信息", {"fields": basic_fields}))

        if other_fields:
            fieldsets.append(("详细信息", {"fields": other_fields}))

        if readonly_fields:
            fieldsets.append(
                ("系统信息", {"fields": readonly_fields, "classes": ("collapse",)})
            )

        return fieldsets

    @staticmethod
    def _generate_display_methods(model: Type[models.Model]) -> Dict[str, Any]:
        """生成显示方法"""
        methods = {}

        # 布尔字段显示方法
        for field in model._meta.fields:
            if isinstance(field, models.BooleanField):
                method_name = f"{field.name}_display"
                methods[method_name] = AutoAdminGenerator._create_boolean_display(
                    field.name
                )

        # 外键字段显示方法
        for field in model._meta.fields:
            if isinstance(field, models.ForeignKey):
                method_name = f"{field.name}_link"
                methods[method_name] = AutoAdminGenerator._create_foreign_key_link(
                    field.name
                )

        return methods

    @staticmethod
    def _create_boolean_display(field_name: str):
        """创建布尔字段显示方法"""

        def boolean_display(self, obj):
            value = getattr(obj, field_name)
            if value:
                return format_html('<span style="color: green;">✓</span>')
            else:
                return format_html('<span style="color: red;">✗</span>')

        boolean_display.short_description = field_name.replace("_", " ").title()
        boolean_display.admin_order_field = field_name
        return boolean_display

    @staticmethod
    def _create_foreign_key_link(field_name: str):
        """创建外键链接显示方法"""

        def foreign_key_link(self, obj):
            related_obj = getattr(obj, field_name)
            if related_obj:
                url = reverse(
                    f"admin:{related_obj._meta.app_label}_{related_obj._meta.model_name}_change",
                    args=[related_obj.pk],
                )
                return format_html('<a href="{}">{}</a>', url, str(related_obj))
            return "-"

        foreign_key_link.short_description = field_name.replace("_", " ").title()
        foreign_key_link.admin_order_field = field_name
        return foreign_key_link

    # 标准动作方法
    @staticmethod
    def _make_active(modeladmin, request, queryset):
        """批量激活"""
        updated = queryset.update(is_active=True)
        modeladmin.message_user(request, f"成功激活 {updated} 个项目。")

    _make_active.short_description = "激活选中项"

    @staticmethod
    def _make_inactive(modeladmin, request, queryset):
        """批量停用"""
        updated = queryset.update(is_active=False)
        modeladmin.message_user(request, f"成功停用 {updated} 个项目。")

    _make_inactive.short_description = "停用选中项"

    @staticmethod
    def _export_as_csv(modeladmin, request, queryset):
        """导出为 CSV"""
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = (
            f'attachment; filename="{modeladmin.model._meta.model_name}.csv"'
        )

        writer = csv.writer(response)

        # 写入表头
        field_names = [field.name for field in modeladmin.model._meta.fields]
        writer.writerow(field_names)

        # 写入数据
        for obj in queryset:
            row = []
            for field_name in field_names:
                value = getattr(obj, field_name)
                row.append(str(value) if value is not None else "")
            writer.writerow(row)

        return response

    _export_as_csv.short_description = "导出选中项为 CSV"


# 便捷函数
def register_auto_admin(
    model: Type[models.Model], config: Optional[AdminConfig] = None
):
    """注册自动 Admin 的便捷函数"""
    AutoAdminGenerator.register_model(model, config)


def register_simple_admin(model: Type[models.Model], **kwargs):
    """注册简单 Admin 的便捷函数"""
    config = AdminConfig(**kwargs)
    AutoAdminGenerator.register_model(model, config)
