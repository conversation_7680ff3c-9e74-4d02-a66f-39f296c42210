"""
使用 Django Ninja 的权限 API 端点。
"""

from typing import List
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from ninja import Router
from ninja.errors import HttpError
from ninja_jwt.authentication import J<PERSON><PERSON><PERSON>

from .models import Permission, Role, UserRole, UserPermission
from .schemas import (
    PermissionResponse,
    RoleResponse,
    RoleCreateRequest,
    RoleUpdateRequest,
    UserRoleResponse,
    AssignRoleRequest,
    UserPermissionResponse,
    GrantPermissionRequest,
    UserPermissionsResponse,
)
from .services import PermissionService
from .decorators import require_permissions, require_superuser

User = get_user_model()
permissions_router = Router(auth=JWTAuth())


@permissions_router.get("/my-permissions", response=UserPermissionsResponse)
def get_my_permissions(request: HttpRequest):
    """
    获取当前用户的所有权限列表。
    """
    user = request.auth
    permissions = PermissionService.get_user_permissions(user)
    roles = PermissionService.get_user_roles(user)
    
    return UserPermissionsResponse(
        user_id=user.id,
        permissions=permissions,
        roles=[role.name for role in roles]
    )


@permissions_router.get("/permissions", response=List[PermissionResponse])
@require_permissions("permissions.view")
def list_permissions(request: HttpRequest):
    """
    List all available permissions.
    """
    permissions = Permission.objects.all().order_by("resource", "action")
    return [PermissionResponse.from_orm(perm) for perm in permissions]


@permissions_router.get("/roles", response=List[RoleResponse])
@require_permissions("roles.view")
def list_roles(request: HttpRequest):
    """
    List all roles.
    """
    roles = Role.objects.prefetch_related("permissions").filter(is_active=True)
    result = []
    for role in roles:
        role_data = {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "is_system_role": role.is_system_role,
            "is_active": role.is_active,
            "permissions": [
                PermissionResponse.from_orm(perm) for perm in role.permissions.all()
            ],
            "created_at": role.created_at,
        }
        result.append(RoleResponse(**role_data))
    return result


@permissions_router.post("/roles", response=RoleResponse)
@require_permissions("roles.create")
def create_role(request: HttpRequest, data: RoleCreateRequest):
    """
    Create a new role.
    """
    # Check if role name already exists
    if Role.objects.filter(name=data.name).exists():
        raise HttpError(400, "Role with this name already exists")

    # Create role
    role = Role.objects.create(name=data.name, description=data.description)

    # Add permissions to role
    if data.permission_ids:
        permissions = Permission.objects.filter(id__in=data.permission_ids)
        for permission in permissions:
            role.add_permission(permission, granted_by=request.auth)

    # Reload role with permissions
    role = Role.objects.prefetch_related("permissions").get(id=role.id)
    return RoleResponse.from_orm(role)


@permissions_router.put("/roles/{role_id}", response=RoleResponse)
@require_permissions("roles.edit")
def update_role(request: HttpRequest, role_id: int, data: RoleUpdateRequest):
    """
    Update a role.
    """
    try:
        role = Role.objects.get(id=role_id)
    except Role.DoesNotExist:
        raise HttpError(404, "Role not found")

    # Check if it's a system role
    if role.is_system_role and not request.auth.is_superuser:
        raise HttpError(403, "Cannot modify system roles")

    # Update role fields
    if data.name is not None:
        if Role.objects.filter(name=data.name).exclude(id=role_id).exists():
            raise HttpError(400, "Role with this name already exists")
        role.name = data.name

    if data.description is not None:
        role.description = data.description

    if data.is_active is not None:
        role.is_active = data.is_active

    role.save()

    # Update permissions if provided
    if data.permission_ids is not None:
        # Remove all current permissions
        role.permissions.clear()

        # Add new permissions
        permissions = Permission.objects.filter(id__in=data.permission_ids)
        for permission in permissions:
            role.add_permission(permission, granted_by=request.auth)

    # Reload role with permissions
    role = Role.objects.prefetch_related("permissions").get(id=role.id)
    return RoleResponse.from_orm(role)


@permissions_router.delete("/roles/{role_id}")
@require_permissions("roles.delete")
def delete_role(request: HttpRequest, role_id: int):
    """
    Delete a role.
    """
    try:
        role = Role.objects.get(id=role_id)
    except Role.DoesNotExist:
        raise HttpError(404, "Role not found")

    # Check if it's a system role
    if role.is_system_role and not request.auth.is_superuser:
        raise HttpError(403, "Cannot delete system roles")

    role.delete()
    return {"message": "Role deleted successfully"}


@permissions_router.post("/users/assign-role", response=UserRoleResponse)
@require_permissions("users.manage_roles")
def assign_role_to_user(request: HttpRequest, data: AssignRoleRequest):
    """
    Assign a role to a user.
    """
    try:
        user = User.objects.get(id=data.user_id)
        role = Role.objects.get(id=data.role_id)
    except User.DoesNotExist:
        raise HttpError(404, "User not found")
    except Role.DoesNotExist:
        raise HttpError(404, "Role not found")

    user_role = PermissionService.assign_role_to_user(
        user=user, role=role, assigned_by=request.auth, expires_at=data.expires_at
    )

    # Manually construct the response data
    role_data = {
        "id": role.id,
        "name": role.name,
        "description": role.description,
        "is_system_role": role.is_system_role,
        "is_active": role.is_active,
        "permissions": [
            PermissionResponse.from_orm(perm) for perm in role.permissions.all()
        ],
        "created_at": role.created_at,
    }

    user_role_data = {
        "id": user_role.id,
        "user_id": user_role.user_id,
        "role": RoleResponse(**role_data),
        "assigned_by_id": user_role.assigned_by_id,
        "is_active": user_role.is_active,
        "expires_at": user_role.expires_at,
        "created_at": user_role.created_at,
    }

    return UserRoleResponse(**user_role_data)


@permissions_router.delete("/users/{user_id}/roles/{role_id}")
@require_permissions("users.manage_roles")
def remove_role_from_user(request: HttpRequest, user_id: int, role_id: int):
    """
    Remove a role from a user.
    """
    try:
        user = User.objects.get(id=user_id)
        role = Role.objects.get(id=role_id)
    except User.DoesNotExist:
        raise HttpError(404, "User not found")
    except Role.DoesNotExist:
        raise HttpError(404, "Role not found")

    success = PermissionService.remove_role_from_user(user, role)

    if not success:
        raise HttpError(404, "User does not have this role")

    return {"message": "Role removed from user successfully"}


@permissions_router.post("/users/grant-permission", response=UserPermissionResponse)
@require_permissions("permissions.manage")
def grant_permission_to_user(request: HttpRequest, data: GrantPermissionRequest):
    """
    Grant or deny a direct permission to a user.
    """
    try:
        user = User.objects.get(id=data.user_id)
        permission = Permission.objects.get(id=data.permission_id)
    except User.DoesNotExist:
        raise HttpError(404, "User not found")
    except Permission.DoesNotExist:
        raise HttpError(404, "Permission not found")

    if data.granted:
        user_permission = PermissionService.grant_permission_to_user(
            user=user,
            permission=permission,
            granted_by=request.auth,
            expires_at=data.expires_at,
        )
    else:
        user_permission = PermissionService.deny_permission_to_user(
            user=user,
            permission=permission,
            granted_by=request.auth,
            expires_at=data.expires_at,
        )

    return UserPermissionResponse.from_orm(user_permission)


@permissions_router.get(
    "/users/{user_id}/permissions", response=UserPermissionsResponse
)
@require_permissions("users.view")
def get_user_permissions(request: HttpRequest, user_id: int):
    """
    Get all permissions for a user.
    """
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        raise HttpError(404, "User not found")

    # Get user permissions
    permissions = PermissionService.get_user_permissions(user)

    # Get user roles
    user_roles = UserRole.objects.filter(
        user=user, is_active=True, role__is_active=True
    ).select_related("role")

    roles = [
        user_role.role.name for user_role in user_roles if not user_role.is_expired()
    ]

    return UserPermissionsResponse(
        user_id=user_id, permissions=list(permissions), roles=roles
    )


@permissions_router.get("/check-permission/{permission_codename}")
def check_user_permission(request: HttpRequest, permission_codename: str):
    """
    Check if current user has a specific permission.
    """
    has_permission = PermissionService.user_has_permission(
        request.auth, permission_codename
    )

    return {
        "user_id": request.auth.id,
        "permission": permission_codename,
        "has_permission": has_permission,
    }
