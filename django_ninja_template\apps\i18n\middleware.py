"""
Internationalization middleware.
"""
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import activate
from django.conf import settings
from .services import I18nService


class LanguageMiddleware(MiddlewareMixin):
    """
    Middleware to handle language detection and activation.
    """
    
    def process_request(self, request):
        """
        Detect and activate language for the request.
        """
        language_code = self._get_language_from_request(request)
        
        # Validate language code
        supported_languages = I18nService.get_supported_languages()
        supported_codes = [lang['code'] for lang in supported_languages]
        
        if language_code not in supported_codes:
            language_code = I18nService.get_default_language()
        
        # Activate language
        activate(language_code)
        
        # Store language in request for later use
        request.language_code = language_code
        
        return None
    
    def _get_language_from_request(self, request):
        """
        Get language code from request in order of priority:
        1. URL parameter (?lang=en)
        2. HTTP Accept-Language header
        3. User preference (if authenticated)
        4. Session
        5. Cookie
        6. Default language
        """
        # 1. URL parameter
        lang_param = request.GET.get('lang')
        if lang_param:
            return lang_param
        
        # 2. User preference (if authenticated)
        if hasattr(request, 'user') and request.user.is_authenticated:
            if hasattr(request.user, 'language') and request.user.language:
                return request.user.language
        
        # 3. Session
        session_lang = request.session.get('language_code')
        if session_lang:
            return session_lang
        
        # 4. Cookie
        cookie_lang = request.COOKIES.get('language_code')
        if cookie_lang:
            return cookie_lang
        
        # 5. HTTP Accept-Language header
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE')
        if accept_language:
            # Parse Accept-Language header
            languages = []
            for lang_range in accept_language.split(','):
                lang_range = lang_range.strip()
                if ';' in lang_range:
                    lang, quality = lang_range.split(';', 1)
                    try:
                        quality = float(quality.split('=')[1])
                    except (ValueError, IndexError):
                        quality = 1.0
                else:
                    lang, quality = lang_range, 1.0
                
                # Normalize language code
                lang = lang.strip().lower()
                if '-' in lang:
                    lang = lang.split('-')[0]  # Take only the language part
                
                languages.append((lang, quality))
            
            # Sort by quality and return the best match
            languages.sort(key=lambda x: x[1], reverse=True)
            for lang, _ in languages:
                return lang
        
        # 6. Default language
        return I18nService.get_default_language()


class TimezoneMiddleware(MiddlewareMixin):
    """
    Middleware to handle timezone detection and activation.
    """
    
    def process_request(self, request):
        """
        Detect and activate timezone for the request.
        """
        import pytz
        from django.utils import timezone
        
        timezone_name = self._get_timezone_from_request(request)
        
        # Validate timezone
        try:
            user_timezone = pytz.timezone(timezone_name)
            timezone.activate(user_timezone)
            request.timezone = user_timezone
        except pytz.exceptions.UnknownTimeZoneError:
            # Use default timezone
            request.timezone = pytz.timezone(settings.TIME_ZONE)
        
        return None
    
    def _get_timezone_from_request(self, request):
        """
        Get timezone from request in order of priority:
        1. URL parameter (?tz=America/New_York)
        2. User preference (if authenticated)
        3. Session
        4. Cookie
        5. Default timezone
        """
        # 1. URL parameter
        tz_param = request.GET.get('tz')
        if tz_param:
            return tz_param
        
        # 2. User preference (if authenticated)
        if hasattr(request, 'user') and request.user.is_authenticated:
            if hasattr(request.user, 'timezone') and request.user.timezone:
                return request.user.timezone
        
        # 3. Session
        session_tz = request.session.get('timezone')
        if session_tz:
            return session_tz
        
        # 4. Cookie
        cookie_tz = request.COOKIES.get('timezone')
        if cookie_tz:
            return cookie_tz
        
        # 5. Default timezone
        return settings.TIME_ZONE
