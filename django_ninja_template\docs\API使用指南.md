# 📡 API 使用指南

本文档详细介绍了 Django Ninja 脚手架项目的 API 使用方法。

## 📚 API 概览

### 基础信息

- **基础 URL**: `http://localhost:8000/api/`
- **API 版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **文档地址**: `http://localhost:8000/api/docs/`

### API 模块

| 模块 | 路径 | 说明 |
|------|------|------|
| 认证 | `/api/auth/` | 用户认证相关 |
| 用户 | `/api/users/` | 用户管理 |
| 权限 | `/api/permissions/` | 权限管理 |
| 存储 | `/api/storage/` | 文件存储 |
| 国际化 | `/api/i18n/` | 多语言支持 |
| 核心 | `/api/core/` | 系统核心功能 |

## 🔐 认证系统

### 用户注册

```http
POST /api/auth/register
Content-Type: application/json

{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "securepassword123",
    "first_name": "张",
    "last_name": "三"
}
```

**响应:**
```json
{
    "message": "用户注册成功，请检查邮箱进行验证",
    "user_id": 1
}
```

### 用户登录

```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "securepassword123"
}
```

**响应:**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "first_name": "张",
        "last_name": "三"
    }
}
```

### 刷新令牌

```http
POST /api/auth/refresh
Content-Type: application/json

{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 用户登出

```http
POST /api/auth/logout
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 👤 用户管理

### 获取当前用户信息

```http
GET /api/users/me
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 更新用户资料

```http
PUT /api/users/me
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
    "first_name": "李",
    "last_name": "四",
    "bio": "这是我的个人简介",
    "avatar": "http://example.com/avatar.jpg"
}
```

### 修改密码

```http
POST /api/users/change-password
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
    "old_password": "oldpassword123",
    "new_password": "newpassword456"
}
```

## 🔒 权限管理

### 检查权限

```http
GET /api/permissions/check-permission/users.view
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**响应:**
```json
{
    "has_permission": true,
    "permission": "users.view",
    "message": "用户拥有此权限"
}
```

### 获取用户权限列表

```http
GET /api/permissions/user-permissions
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 获取用户角色

```http
GET /api/permissions/user-roles
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 📁 文件存储

### 上传文件

```http
POST /api/storage/upload
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: multipart/form-data

file: [文件数据]
description: "文件描述"
```

**响应:**
```json
{
    "id": 1,
    "filename": "example.jpg",
    "original_name": "my-photo.jpg",
    "file_size": 1024000,
    "content_type": "image/jpeg",
    "url": "http://localhost:8000/media/uploads/example.jpg",
    "created_at": "2024-01-01T12:00:00Z"
}
```

### 获取文件列表

```http
GET /api/storage/files
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 删除文件

```http
DELETE /api/storage/files/1
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 🌍 国际化支持

### 获取支持的语言

```http
GET /api/i18n/languages
```

### 获取翻译

```http
GET /api/i18n/translations?lang=zh-cn
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 更新翻译

```http
PUT /api/i18n/translations
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
    "language": "zh-cn",
    "key": "welcome_message",
    "value": "欢迎使用系统"
}
```

## 🔧 系统核心功能

### 系统信息

```http
GET /api/core/info
```

**响应:**
```json
{
    "name": "Django Ninja CMS",
    "version": "1.0.0",
    "environment": "development",
    "database": "postgresql",
    "cache": "redis",
    "timezone": "Asia/Shanghai"
}
```

### 健康检查

```http
GET /health/
```

**响应:**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "services": {
        "database": "ok",
        "cache": "ok",
        "storage": "ok"
    }
}
```

## 📊 分页和过滤

### 分页参数

所有列表 API 都支持分页：

```http
GET /api/users/?page=1&page_size=20
```

### 过滤参数

```http
# 按字段过滤
GET /api/users/?is_active=true&created_after=2024-01-01

# 搜索
GET /api/users/?search=张三

# 排序
GET /api/users/?ordering=-created_at
```

## 🚨 错误处理

### 错误响应格式

```json
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "请求数据验证失败",
        "details": {
            "email": ["邮箱格式不正确"],
            "password": ["密码长度至少8位"]
        }
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 常见错误码

| 状态码 | 错误码 | 说明 |
|--------|--------|------|
| 400 | VALIDATION_ERROR | 请求数据验证失败 |
| 401 | AUTHENTICATION_REQUIRED | 需要身份认证 |
| 403 | PERMISSION_DENIED | 权限不足 |
| 404 | NOT_FOUND | 资源不存在 |
| 429 | RATE_LIMIT_EXCEEDED | 请求频率超限 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

## 🔧 使用示例

### Python 示例

```python
import requests

# 登录获取令牌
response = requests.post('http://localhost:8000/api/auth/login', json={
    'email': '<EMAIL>',
    'password': 'password123'
})
token = response.json()['access_token']

# 使用令牌访问 API
headers = {'Authorization': f'Bearer {token}'}
response = requests.get('http://localhost:8000/api/users/me', headers=headers)
user_info = response.json()
```

### JavaScript 示例

```javascript
// 登录获取令牌
const loginResponse = await fetch('http://localhost:8000/api/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
    })
});
const { access_token } = await loginResponse.json();

// 使用令牌访问 API
const userResponse = await fetch('http://localhost:8000/api/users/me', {
    headers: {
        'Authorization': `Bearer ${access_token}`
    }
});
const userInfo = await userResponse.json();
```

### cURL 示例

```bash
# 登录
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# 使用令牌访问 API
curl -X GET http://localhost:8000/api/users/me \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

---

**提示**：更多详细的 API 文档请访问 http://localhost:8000/api/docs/ 查看交互式文档。
