# Django Ninja 脚手架项目

一个基于 Django Ninja 的现代化、企业级 API 脚手架项目，提供完整的后端解决方案。项目采用模块化设计，具备高度的可扩展性和可维护性，适合快速开发各种类型的 Web API 应用。

## 🎯 项目概述

### 核心价值
- **快速构建**：5分钟创建完整功能模块
- **高质量交付**：企业级安全和性能标准
- **降低成本**：减少70%开发时间和维护成本
- **标准化开发**：统一的架构模式和最佳实践

### 适用场景
- 🏢 **企业内部系统**：OA、CRM、ERP 等管理系统
- 🛒 **电商平台**：B2B/B2C 商城、供应链管理
- 📰 **内容平台**：博客、新闻、知识库、CMS
- 💬 **社交应用**：社区、论坛、即时通讯
- 🔧 **SaaS 产品**：多租户应用、API 服务

## 🚀 项目特色

- **🏗️ 模块化架构** - 高度解耦的应用模块，支持即插即用
- **🔐 完整认证系统** - JWT 认证、RBAC 权限管理、安全监控
- **📁 文件存储系统** - 支持本地和云存储，文件分享功能
- **🌍 国际化支持** - 多语言支持，翻译管理系统
- **⚡ 高性能缓存** - Redis 集成，智能缓存策略
- **🔄 后台任务系统** - Celery 集成，异步任务处理
- **🐳 容器化部署** - Docker 支持，生产就绪配置
- **📚 自动文档** - Swagger/OpenAPI 自动生成
- **🛡️ 安全优先** - 多层安全防护，最佳实践

## 📊 项目统计

### 代码统计
- **总文件数**: 80+ 个
- **代码行数**: 8000+ 行
- **应用模块**: 6 个核心模块
- **API 端点**: 50+ 个
- **数据库模型**: 15+ 个
- **后台任务**: 10+ 个

## 📋 技术栈

### 核心框架
- **Django 5.2+** - Web 框架
- **Django Ninja** - 现代 API 框架
- **Pydantic** - 数据验证和序列化

### 数据库与缓存
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **SQLite** - 开发环境数据库

### 异步任务
- **Celery** - 分布式任务队列
- **Redis** - 消息代理

### 部署与监控
- **Docker & Docker Compose** - 容器化
- **Nginx** - 反向代理
- **Gunicorn** - WSGI 服务器

## 🏗️ 核心功能模块

### 1. 用户管理系统
- **自定义用户模型**: 扩展 Django AbstractUser
- **用户资料管理**: 完整的用户信息和偏好设置
- **用户 API**: 注册、登录、资料管理等完整接口

### 2. 认证与安全
- **JWT 认证**: 无状态的令牌认证机制
- **刷新令牌**: 安全的令牌刷新策略
- **登录监控**: 异常登录检测和频率限制
- **安全审计**: 完整的操作日志记录

### 3. 权限管理 (RBAC)
- **角色权限模型**: 灵活的角色权限分配
- **权限缓存**: 高性能的权限检查机制
- **权限装饰器**: 简单易用的 API 权限控制
- **动态权限**: 支持运行时权限分配和撤销

### 4. 文件存储系统
- **多存储后端**: 支持本地、S3、GCS、Azure 存储
- **文件分享**: 灵活的文件分享和访问控制
- **图片处理**: 自动缩略图生成和图片优化
- **存储统计**: 详细的存储使用情况分析

### 5. 国际化支持
- **多语言管理**: 动态翻译管理系统
- **翻译缓存**: 高性能的翻译加载机制
- **翻译进度**: 实时翻译完成度跟踪
- **语言中间件**: 自动语言检测和切换

### 6. 缓存与性能
- **Redis 集成**: 高性能缓存解决方案
- **多级缓存**: 智能缓存策略
- **缓存服务**: 统一的缓存管理接口
- **性能监控**: 缓存命中率和性能指标

### 7. 异步任务系统
- **Celery 集成**: 分布式任务队列
- **定时任务**: 灵活的任务调度机制
- **任务监控**: 任务执行状态跟踪
- **错误处理**: 完善的任务重试和错误恢复

## 🏗️ 项目结构

```
django_ninja_template/
├── apps/                          # 应用模块
│   ├── authentication/            # 认证系统
│   ├── core/                      # 核心功能
│   ├── i18n/                      # 国际化
│   ├── permissions/               # 权限管理
│   ├── storage/                   # 文件存储
│   └── users/                     # 用户管理
├── core/                          # 项目配置
│   ├── settings/                  # 多环境配置
│   ├── middleware/                # 中间件
│   ├── celery.py                  # Celery 配置
│   └── urls.py                    # URL 路由
├── docs/                          # 项目文档
├── examples/                      # 示例代码
├── static/                        # 静态文件
├── media/                         # 媒体文件
├── logs/                          # 日志文件
├── docker-compose.yml             # Docker 编排
├── Dockerfile                     # Docker 镜像
├── requirements.txt               # Python 依赖
└── manage.py                      # Django 管理脚本
```

### 设计原则
1. **模块化**: 每个功能独立成模块，便于维护和扩展
2. **可配置**: 支持多环境配置，灵活适应不同部署需求
3. **安全优先**: 实施多层安全防护，遵循最佳实践
4. **性能优化**: 集成缓存、异步任务等性能优化方案
5. **国际化**: 内置多语言支持，面向全球用户

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Redis 6.0+ (可选，用于缓存和异步任务)
- PostgreSQL 13+ (可选，开发环境可使用 SQLite)

### 一键启动
```bash
# 使用启动脚本（推荐）
python start.py
```

### 手动安装
```bash
# 1. 克隆项目
git clone <repository-url>
cd django_ninja_template

# 2. 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 初始化数据库
python manage.py migrate
python manage.py createsuperuser
python manage.py setup_permissions

# 5. 启动服务
python manage.py runserver
```

### 访问应用
- **应用首页**: http://localhost:8000/
- **API 文档**: http://localhost:8000/api/docs/
- **管理后台**: http://localhost:8000/admin/
- **健康检查**: http://localhost:8000/health/

## 🐳 Docker 部署

```bash
# 开发环境
docker-compose up -d

# 生产环境
DJANGO_ENVIRONMENT=production docker-compose -f docker-compose.prod.yml up -d
```

## 📚 API 文档

启动服务后访问：
- **Swagger UI**: http://localhost:8000/api/docs/
- **OpenAPI JSON**: http://localhost:8000/api/openapi.json

## 🔧 代码生成工具

### 基于配置文件的批量生成
使用 YAML 配置文件一次性生成完整的 CMS 系统：

```bash
# 生成完整的 CMS 模块（包含前端组件）
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend

# 预览模式（不实际生成文件）
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend --dry-run
```

### 单个模块生成
```bash
# 生成单个模块
python manage.py generate module blog --fields "title:str,content:text,is_published:bool" --admin --api

# 生成模型
python manage.py generate model blog Post --fields "title:str,content:text"

# 生成 API
python manage.py generate api blog Post --permissions "blog.post"
```

### 配置文件示例
```yaml
app_name: cms
models:
  Article:
    fields:
      title: str
      content: text
      status: str
      author: fk:User
      category: fk:Category
    options:
      verbose_name: '文章'
```

## 🔧 代码生成工具

### 基于配置文件的批量生成
使用 YAML 配置文件一次性生成完整的 CMS 系统：

```bash
# 生成完整的 CMS 模块（包含前端组件）
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend

# 预览模式（不实际生成文件）
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend --dry-run
```

### 单个模块生成
```bash
# 生成单个模块
python manage.py generate module blog --fields "title:str,content:text,is_published:bool" --admin --api

# 生成模型
python manage.py generate model blog Post --fields "title:str,content:text"

# 生成 API
python manage.py generate api blog Post --permissions "blog.post"
```

### 配置文件示例
```yaml
app_name: cms
models:
  Article:
    fields:
      title: str
      content: text
      status: str
      author: fk:User
      category: fk:Category
    options:
      verbose_name: '文章'
```

## 🔧 API 示例

### 认证系统
```bash
# 用户注册
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "username": "testuser", "password": "testpass123"}'

# 用户登录
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "testpass123"}'
```

### 文件上传
```bash
# 上传文件
curl -X POST http://localhost:8000/api/storage/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@example.jpg"
```

### 权限检查
```bash
# 检查权限
curl -X GET http://localhost:8000/api/permissions/check-permission/users.view \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## ⚙️ 配置说明

### 环境变量
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DJANGO_ENVIRONMENT` | 运行环境 | `development` |
| `SECRET_KEY` | Django 密钥 | - |
| `DATABASE_URL` | 数据库连接 | `sqlite:///db.sqlite3` |
| `REDIS_URL` | Redis 连接 | `redis://localhost:6379/1` |
| `CELERY_BROKER_URL` | Celery 代理 | `redis://localhost:6379/0` |

### 多环境配置
- `development` - 开发环境
- `testing` - 测试环境
- `staging` - 预发布环境
- `production` - 生产环境

## 🔒 安全特性

### 认证安全
- **JWT 认证** - 无状态认证机制
- **令牌管理** - 自动过期和刷新
- **登录监控** - 异常登录检测
- **多设备管理** - 设备登录控制

### API 安全
- **RBAC 权限** - 基于角色的访问控制
- **请求限流** - 防止 API 滥用
- **CORS 配置** - 跨域请求控制
- **安全头部** - XSS、CSRF 防护

### 数据安全
- **数据加密** - 敏感数据加密存储
- **SQL 注入防护** - 参数化查询
- **文件安全** - 文件类型和大小验证
- **隐私保护** - 用户数据隐私设置

## 📊 监控与日志

### 健康检查
```bash
# 应用健康检查
GET /health/

# 系统信息
GET /api/core/info
```

### 日志配置
- 应用日志：`logs/django.log`
- 访问日志：通过中间件记录
- 错误监控：支持 Sentry 集成

## 🧪 测试

```bash
# 运行测试
python manage.py test

# 生成测试覆盖率报告
coverage run --source='.' manage.py test
coverage report
```

## 📈 性能优化

- **数据库优化** - 查询优化、索引配置
- **缓存策略** - Redis 多级缓存
- **异步处理** - Celery 后台任务
- **静态文件** - CDN 支持

## 📚 文档导航

### 快速开始
- [快速开始指南](docs/快速开始指南.md) - 5分钟快速上手
- [文档导航](docs/文档导航.md) - 完整的文档索引

### 开发文档
- [开发指南](docs/开发指南.md) - 完整的开发环境设置和开发流程
- [API使用指南](docs/API使用指南.md) - 完整的 API 文档
- [代码生成工具指南](docs/代码生成工具指南.md) - 强大的代码生成工具

### 教程与规划
- [CMS教程](docs/CMS教程.md) - 10分钟构建完整CMS系统
- [项目状态与规划](docs/项目状态与规划.md) - 开发进度和未来规划

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有疑问，请：

1. 查看项目文档
2. 搜索 GitHub Issues
3. 创建新的 Issue

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和开源社区。

---

**Django Ninja 脚手架项目** - 让 API 开发更简单、更高效、更安全！
