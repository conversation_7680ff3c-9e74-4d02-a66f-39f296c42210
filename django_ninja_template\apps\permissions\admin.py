"""
Admin configuration for permissions app.
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from .models import Permission, Role, RolePermission, UserRole, UserPermission


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    """
    Admin configuration for Permission model.
    """
    list_display = ('name', 'codename', 'resource', 'action', 'created_at')
    list_filter = ('resource', 'action', 'created_at')
    search_fields = ('name', 'codename', 'description')
    ordering = ('resource', 'action', 'name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Permission Info'), {'fields': ('name', 'codename', 'description')}),
        (_('Resource'), {'fields': ('resource', 'action')}),
        (_('Timestamps'), {'fields': ('created_at', 'updated_at')}),
    )


class RolePermissionInline(admin.TabularInline):
    """
    Inline admin for RolePermission.
    """
    model = RolePermission
    extra = 0
    readonly_fields = ('granted_by', 'created_at')


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """
    Admin configuration for Role model.
    """
    list_display = ('name', 'is_system_role', 'is_active', 'permission_count', 'created_at')
    list_filter = ('is_system_role', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ('created_at', 'updated_at')
    inlines = [RolePermissionInline]
    
    fieldsets = (
        (_('Role Info'), {'fields': ('name', 'description')}),
        (_('Status'), {'fields': ('is_system_role', 'is_active')}),
        (_('Timestamps'), {'fields': ('created_at', 'updated_at')}),
    )
    
    def permission_count(self, obj):
        """Show number of permissions for this role."""
        return obj.permissions.count()
    permission_count.short_description = _('Permissions Count')


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """
    Admin configuration for UserRole model.
    """
    list_display = ('user', 'role', 'assigned_by', 'is_active', 'expires_at', 'created_at')
    list_filter = ('is_active', 'role', 'created_at', 'expires_at')
    search_fields = ('user__email', 'user__username', 'role__name')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Assignment'), {'fields': ('user', 'role', 'assigned_by')}),
        (_('Status'), {'fields': ('is_active', 'expires_at')}),
        (_('Timestamps'), {'fields': ('created_at', 'updated_at')}),
    )


@admin.register(UserPermission)
class UserPermissionAdmin(admin.ModelAdmin):
    """
    Admin configuration for UserPermission model.
    """
    list_display = ('user', 'permission', 'granted', 'granted_by', 'expires_at', 'created_at')
    list_filter = ('granted', 'permission__resource', 'created_at', 'expires_at')
    search_fields = ('user__email', 'user__username', 'permission__name', 'permission__codename')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Permission Assignment'), {'fields': ('user', 'permission', 'granted', 'granted_by')}),
        (_('Expiration'), {'fields': ('expires_at',)}),
        (_('Timestamps'), {'fields': ('created_at', 'updated_at')}),
    )
