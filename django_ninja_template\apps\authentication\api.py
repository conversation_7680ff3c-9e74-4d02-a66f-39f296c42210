"""
使用 Django Ninja 的认证 API 端点。
"""
from typing import Dict, Any
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from ninja import Router
from ninja.errors import HttpError
from ninja_jwt.authentication import J<PERSON>TAuth

from .schemas import (
    LoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse,
    LogoutRequest, RegisterRequest, RegisterResponse, UserProfileResponse
)
from .services import AuthService

User = get_user_model()
auth_router = Router()


@auth_router.post("/login", response=LoginResponse, auth=None)
def login(request: HttpRequest, data: LoginRequest):
    """
    Authenticate user and return JWT tokens.
    """
    # Get client information
    ip_address = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    # Authenticate user
    user = AuthService.authenticate_user(
        email=data.email,
        password=data.password,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    if not user:
        raise HttpError(401, "Invalid credentials or account locked")
    
    # Generate tokens
    tokens = AuthService.generate_tokens(
        user=user,
        device_name=data.device_name,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    # Prepare user data
    user_data = {
        "id": user.id,
        "email": user.email,
        "username": user.username,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "is_verified": user.is_verified,
        "is_premium": user.is_premium,
    }
    
    return LoginResponse(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"],
        expires_in=tokens["expires_in"],
        user=user_data
    )


@auth_router.post("/refresh", response=RefreshTokenResponse, auth=None)
def refresh_token(request: HttpRequest, data: RefreshTokenRequest):
    """
    Refresh access token using refresh token.
    """
    tokens = AuthService.refresh_access_token(data.refresh_token)
    
    if not tokens:
        raise HttpError(401, "Invalid or expired refresh token")
    
    return RefreshTokenResponse(
        access_token=tokens["access_token"],
        token_type=tokens["token_type"],
        expires_in=tokens["expires_in"]
    )


@auth_router.post("/logout", auth=None)
def logout(request: HttpRequest, data: LogoutRequest):
    """
    Logout user by revoking refresh token.
    """
    success = AuthService.revoke_refresh_token(data.refresh_token)
    
    if not success:
        raise HttpError(400, "Invalid refresh token")
    
    return {"message": "Successfully logged out"}


@auth_router.post("/register", response=RegisterResponse, auth=None)
def register(request: HttpRequest, data: RegisterRequest):
    """
    Register a new user.
    """
    # Check if user already exists
    if User.objects.filter(email=data.email).exists():
        raise HttpError(400, "User with this email already exists")
    
    if User.objects.filter(username=data.username).exists():
        raise HttpError(400, "User with this username already exists")
    
    # Create user
    user = User.objects.create_user(
        email=data.email,
        username=data.username,
        password=data.password,
        first_name=data.first_name,
        last_name=data.last_name
    )
    
    # TODO: Send email verification
    
    return RegisterResponse(
        message="User registered successfully. Please check your email for verification.",
        user_id=user.id
    )


@auth_router.get("/me", response=UserProfileResponse, auth=JWTAuth())
def get_current_user(request: HttpRequest):
    """
    获取当前认证用户的详细信息。
    """
    user = request.auth
    return UserProfileResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        is_verified=user.is_verified,
        is_premium=user.is_premium,
        created_at=user.created_at.isoformat()
    )


@auth_router.get("/verify-token", auth=None)
def verify_token(request: HttpRequest, token: str):
    """
    Verify if a token is valid.
    """
    user = AuthService.authenticate_token(token)
    
    if not user:
        raise HttpError(401, "Invalid or expired token")
    
    return {
        "valid": True,
        "user_id": user.id,
        "email": user.email
    }


def get_client_ip(request: HttpRequest) -> str:
    """
    Get the client IP address from the request.
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip
