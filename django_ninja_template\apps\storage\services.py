"""
文件存储服务。
"""
import os
import mimetypes
import secrets
from typing import Optional, Dict, Any
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from PIL import Image
from .models import StorageFile, FileShare, FileCategory

User = get_user_model()


class StorageService:
    """
    文件存储操作的服务类。
    """

    # 按类型允许的文件扩展名
    ALLOWED_EXTENSIONS = {
        'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
        'document': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'],
        'video': ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'],
        'audio': ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'],
        'archive': ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'],
    }
    
    # 按类型的最大文件大小（字节）
    MAX_FILE_SIZES = {
        'image': 10 * 1024 * 1024,      # 10MB
        'document': 50 * 1024 * 1024,   # 50MB
        'video': 500 * 1024 * 1024,     # 500MB
        'audio': 100 * 1024 * 1024,     # 100MB
        'archive': 100 * 1024 * 1024,   # 100MB
        'other': 10 * 1024 * 1024,      # 10MB
    }
    
    @classmethod
    def upload_file(cls, user: User, uploaded_file: UploadedFile, **kwargs) -> StorageFile:
        """
        上传文件并创建 StorageFile 记录。
        """
        # Validate file
        cls._validate_file(uploaded_file)
        
        # 简化文件类型判断
        file_ext = os.path.splitext(uploaded_file.name)[1].lower()
        if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            file_type = 'image'
        else:
            file_type = 'document'
        
        # Get MIME type
        mime_type, _ = mimetypes.guess_type(uploaded_file.name)
        mime_type = mime_type or 'application/octet-stream'
        
        # Create StorageFile instance
        storage_file = StorageFile(
            uploaded_by=user,
            original_name=uploaded_file.name,
            file_size=uploaded_file.size,
            content_type=mime_type,
            category=kwargs.get('category'),
            is_public=kwargs.get('is_public', False),
            status=StorageFile.STATUS_UPLOADING,
            metadata=kwargs.get('metadata', {})
        )
        
        # Save file
        storage_file.file.save(uploaded_file.name, uploaded_file, save=False)
        
        # Process image files (简化处理)
        if storage_file.is_image:
            try:
                cls._process_image(storage_file)
            except Exception:
                # 如果图片处理失败，继续保存文件
                pass
        
        # Update status to completed
        storage_file.status = StorageFile.STATUS_COMPLETED
        storage_file.save()
        return storage_file
    
    @classmethod
    def create_file_share(cls, storage_file: StorageFile, shared_by: User, **kwargs) -> FileShare:
        """
        Create a file share.
        """
        import uuid
        share_token = uuid.uuid4()
        
        file_share = FileShare(
            file=storage_file,
            shared_by=shared_by,
            share_token=share_token,
            expires_at=kwargs.get('expires_at'),
            download_limit=kwargs.get('download_limit'),
            is_active=True
        )
        
        # Set password if provided
        if kwargs.get('password'):
            file_share.password_hash = make_password(kwargs['password'])
            file_share.password_protected = True
        
        file_share.save()
        return file_share
    
    @classmethod
    def get_download_url(cls, storage_file: StorageFile) -> str:
        """
        Get download URL for a file.
        """
        # 简化为本地存储
        return storage_file.file.url
    
    @classmethod
    def delete_file(cls, storage_file: StorageFile) -> bool:
        """
        Delete a file from storage.
        """
        try:
            # Delete local file
            if storage_file.file:
                storage_file.file.delete(save=False)
            
            # Delete database record
            storage_file.delete()
            return True
        except Exception:
            return False
    
    @classmethod
    def _validate_file(cls, uploaded_file: UploadedFile):
        """
        Validate uploaded file.
        """
        # 简化验证 - 只检查基本的文件大小限制
        max_size = 100 * 1024 * 1024  # 100MB
        
        if uploaded_file.size > max_size:
            raise ValueError(f"File size exceeds maximum allowed size of {max_size} bytes")
        
        # 允许所有文件类型（简化处理）
        if not uploaded_file.name:
            raise ValueError("File name is required")
    
    @classmethod
    def _get_file_type(cls, filename: str) -> str:
        """
        Determine file type based on extension.
        """
        file_ext = os.path.splitext(filename)[1].lower().lstrip('.')
        
        for file_type, extensions in cls.ALLOWED_EXTENSIONS.items():
            if file_ext in extensions:
                return file_type
        
        return 'other'
    
    @classmethod
    def _process_image(cls, storage_file: StorageFile):
        """
        Process image files to extract dimensions.
        """
        try:
            with Image.open(storage_file.file.path) as img:
                # Add image metadata
                storage_file.metadata.update({
                    'width': img.size[0],
                    'height': img.size[1],
                    'format': img.format,
                    'mode': img.mode,
                    'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                })
        except Exception:
            # If image processing fails, continue without dimensions
            pass
    
    # 移除云存储相关方法，简化为本地存储
