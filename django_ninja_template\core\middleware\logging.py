"""
Logging middleware for request/response logging.
"""
import logging
import time
import uuid
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class LoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log HTTP requests and responses.
    """
    
    def process_request(self, request):
        """
        Process incoming request and add logging context.
        """
        # Generate unique request ID
        request.request_id = str(uuid.uuid4())
        request.start_time = time.time()
        
        # Log request details
        logger.info(
            f"Request started - ID: {request.request_id} | "
            f"Method: {request.method} | "
            f"Path: {request.path} | "
            f"User: {getattr(request.user, 'username', 'Anonymous')} | "
            f"IP: {self.get_client_ip(request)}"
        )
        
        return None
    
    def process_response(self, request, response):
        """
        Process outgoing response and log completion.
        """
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            logger.info(
                f"Request completed - ID: {getattr(request, 'request_id', 'unknown')} | "
                f"Status: {response.status_code} | "
                f"Duration: {duration:.3f}s"
            )
        
        return response
    
    def process_exception(self, request, exception):
        """
        Process exceptions and log them.
        """
        logger.error(
            f"Request failed - ID: {getattr(request, 'request_id', 'unknown')} | "
            f"Exception: {type(exception).__name__}: {str(exception)}",
            exc_info=True
        )
        
        return None
    
    @staticmethod
    def get_client_ip(request):
        """
        Get the client IP address from the request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
