"""
Pydantic schemas for permissions API.
"""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class PermissionResponse(BaseModel):
    """Schema for permission response."""
    id: int
    name: str
    codename: str
    description: str
    resource: str
    action: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class RoleResponse(BaseModel):
    """Schema for role response."""
    id: int
    name: str
    description: str
    is_system_role: bool
    is_active: bool
    permissions: List[PermissionResponse]
    created_at: datetime
    
    class Config:
        from_attributes = True


class RoleCreateRequest(BaseModel):
    """Schema for role creation."""
    name: str = Field(..., min_length=1, max_length=255)
    description: str = ""
    permission_ids: List[int] = []


class RoleUpdateRequest(BaseModel):
    """Schema for role update."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    is_active: Optional[bool] = None
    permission_ids: Optional[List[int]] = None


class UserRoleResponse(BaseModel):
    """Schema for user role response."""
    id: int
    user_id: int
    role: RoleResponse
    assigned_by_id: Optional[int] = None
    is_active: bool
    expires_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class AssignRoleRequest(BaseModel):
    """Schema for assigning role to user."""
    user_id: int
    role_id: int
    expires_at: Optional[datetime] = None


class UserPermissionResponse(BaseModel):
    """Schema for user permission response."""
    id: int
    user_id: int
    permission: PermissionResponse
    granted: bool
    granted_by_id: Optional[int] = None
    expires_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class GrantPermissionRequest(BaseModel):
    """Schema for granting permission to user."""
    user_id: int
    permission_id: int
    granted: bool = True
    expires_at: Optional[datetime] = None


class UserPermissionsResponse(BaseModel):
    """Schema for user's all permissions."""
    user_id: int
    permissions: List[str]
    roles: List[str]
