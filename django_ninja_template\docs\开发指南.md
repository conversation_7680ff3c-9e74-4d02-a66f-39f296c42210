# 🛠️ Django Ninja 脚手架项目开发指南

本指南涵盖了从安装配置到高效开发的完整流程。

## 📋 目录

- [环境安装](#环境安装)
- [开发流程](#开发流程)
- [创建新功能](#创建新功能)
- [API 开发](#api-开发)
- [数据库操作](#数据库操作)
- [测试指南](#测试指南)
- [部署配置](#部署配置)
- [常见问题](#常见问题)

## 🚀 环境安装

### 系统要求

**最低要求：**
- Python 3.11+
- 内存 2GB+
- 存储 5GB+

**推荐配置：**
- Python 3.11+
- 内存 4GB+
- 存储 20GB+ SSD
- Redis 6.0+
- PostgreSQL 13+

### 开发环境安装

```bash
# 1. 克隆项目
git clone <repository-url>
cd django_ninja_cms

# 2. 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 环境配置
cp .env.example .env
# 编辑 .env 文件配置数据库等信息

# 5. 初始化数据库
python manage.py migrate
python manage.py createsuperuser
python manage.py setup_permissions

# 6. 启动开发服务器
python manage.py runserver
```

### Docker 环境

```bash
# 开发环境
docker-compose up -d

# 生产环境
DJANGO_ENVIRONMENT=production docker-compose -f docker-compose.prod.yml up -d
```

## 🎯 开发流程

### 高效开发流程

1. **功能规划** → 2. **自动生成代码** → 3. **业务逻辑定制** → 4. **测试验证** → 5. **部署上线**

### 开发时间估算

- **简单 CRUD 功能**: 30分钟-1小时
- **复杂业务逻辑**: 半天-1天
- **完整功能模块**: 2-3天

### 效率工具优先级

1. **代码生成工具** - 快速创建模块结构
2. **自动 CRUD 生成器** - 生成标准 API 接口
3. **智能 Admin 配置** - 自动配置管理界面
4. **权限自动化工具** - 简化权限配置
5. **数据工厂** - 快速生成测试数据

## 🏗️ 创建新功能

### 方式一：使用代码生成工具（推荐）

```bash
# 生成完整模块
python manage.py generate module blog \
  --fields "title:str,content:text,is_published:bool,author:fk:User" \
  --admin --api

# 基于配置文件批量生成
python manage.py generate_from_config --config blog_config.yaml --full --with-frontend
```

### 方式二：手动创建

```bash
# 1. 创建应用目录
mkdir apps/blog
cd apps/blog

# 2. 创建必要文件
touch __init__.py models.py schemas.py api.py admin.py

# 3. 定义模型
# 编辑 models.py

# 4. 创建 API
# 编辑 api.py

# 5. 配置 Admin
# 编辑 admin.py
```

## 🔌 API 开发

### 基本 API 结构

```python
# api.py
from ninja import Router
from typing import List
from .models import Post
from .schemas import PostSchema, PostCreateSchema

router = Router(tags=["Blog"])

@router.get("/posts/", response=List[PostSchema])
def list_posts(request):
    return Post.objects.all()

@router.post("/posts/", response=PostSchema)
def create_post(request, data: PostCreateSchema):
    return Post.objects.create(**data.dict())
```

### 权限控制

```python
from apps.permissions.decorators import require_permission

@router.get("/posts/")
@require_permission('blog.post.view')
def list_posts(request):
    return Post.objects.filter(owner=request.user)
```

### 数据验证

```python
# schemas.py
from pydantic import BaseModel, validator
from typing import Optional

class PostCreateSchema(BaseModel):
    title: str
    content: str
    is_published: bool = False
    
    @validator('title')
    def title_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('标题不能为空')
        return v
```

## 🗄️ 数据库操作

### 模型定义

```python
# models.py
from django.db import models
from apps.core.models import BaseModel

class Post(BaseModel):
    title = models.CharField(max_length=200)
    content = models.TextField()
    is_published = models.BooleanField(default=False)
    
    class Meta:
        verbose_name = "文章"
        ordering = ['-created_at']
```

### 迁移操作

```bash
# 创建迁移文件
python manage.py makemigrations

# 查看迁移 SQL
python manage.py sqlmigrate app_name 0001

# 执行迁移
python manage.py migrate

# 回滚迁移
python manage.py migrate app_name 0001
```

### 数据库查询优化

```python
# 使用 select_related 优化外键查询
posts = Post.objects.select_related('author').all()

# 使用 prefetch_related 优化多对多查询
posts = Post.objects.prefetch_related('tags').all()

# 使用 only 限制查询字段
posts = Post.objects.only('title', 'created_at').all()
```

## 🧪 测试指南

### 测试结构

```python
# tests.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from .models import Post

User = get_user_model()

class PostTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
    
    def test_create_post(self):
        post = Post.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user
        )
        self.assertEqual(post.title, 'Test Post')
```

### API 测试

```python
from rest_framework.test import APITestCase
from rest_framework import status

class PostAPITestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_create_post(self):
        data = {
            'title': 'Test Post',
            'content': 'Test content'
        }
        response = self.client.post('/api/posts/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
```

### 运行测试

```bash
# 运行所有测试
python manage.py test

# 运行特定应用测试
python manage.py test apps.blog

# 生成覆盖率报告
coverage run --source='.' manage.py test
coverage report
```

## 🚀 部署配置

### 环境变量配置

```env
# .env
DJANGO_ENVIRONMENT=production
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# 数据库
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# Redis
REDIS_URL=redis://localhost:6379/1

# 邮件
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-password
```

### 静态文件配置

```bash
# 收集静态文件
python manage.py collectstatic --noinput

# 配置 Nginx
# 编辑 nginx.conf
```

### 生产环境检查

```bash
# 系统检查
python manage.py check --deploy

# 安全检查
python manage.py check --tag security
```

## ❓ 常见问题

### 1. 数据库连接问题

```bash
# 检查数据库连接
python manage.py dbshell

# 重置数据库
python manage.py flush
```

### 2. 静态文件问题

```bash
# 清理静态文件
python manage.py collectstatic --clear

# 查找静态文件
python manage.py findstatic filename.css
```

### 3. 权限问题

```bash
# 重新生成权限
python manage.py auto_generate_permissions app_name --create-roles

# 检查用户权限
python manage.py shell
>>> from django.contrib.auth.models import User
>>> user = User.objects.get(username='testuser')
>>> user.user_permissions.all()
```

### 4. 缓存问题

```bash
# 清理缓存
python manage.py shell
>>> from django.core.cache import cache
>>> cache.clear()
```

### 5. 日志调试

```python
# 在代码中添加日志
import logging
logger = logging.getLogger(__name__)

def my_view(request):
    logger.info('处理请求: %s', request.path)
    logger.error('发生错误: %s', error_message)
```

查看日志文件：`logs/django.log`

---

**提示**：开发过程中遇到问题，首先查看日志文件，然后检查配置，最后考虑重启服务。
