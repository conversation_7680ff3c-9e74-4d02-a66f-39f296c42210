# 🏗️ 企业级项目教程 01 - 基础项目搭建

欢迎来到企业级项目开发教程的第一部分！本教程将指导您从零开始使用 Django Ninja 脚手架项目搭建一个企业级应用。

## 🎯 本教程目标

完成本教程后，您将：
- ✅ 理解企业级项目的基本架构
- ✅ 掌握 Django Ninja 框架的核心概念
- ✅ 建立完整的开发环境
- ✅ 创建项目的基础结构
- ✅ 配置数据库和基本设置

## 📋 前置要求

### 必备知识
- Python 基础语法（变量、函数、类）
- 基本的命令行操作
- 了解 HTTP 协议基础概念

### 环境要求
- Python 3.11+
- 操作系统：Windows/macOS/Linux
- 内存：至少 4GB
- 硬盘：至少 2GB 可用空间

## 🚀 第一步：环境准备

### 1.1 检查 Python 版本

```bash
# 检查 Python 版本
python --version
# 应该显示 Python 3.11.x 或更高版本

# 如果版本过低，请访问 https://python.org 下载最新版本
```

### 1.2 创建项目目录

```bash
# 创建项目根目录
mkdir my-enterprise-project
cd my-enterprise-project

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

# 验证虚拟环境
which python  # 应该指向虚拟环境中的 python
```

### 1.3 克隆基础项目

```bash
# 克隆 Django Ninja 脚手架项目作为基础
git clone https://github.com/your-repo/django-ninja-cms.git .

# 或者下载并解压项目文件
```

## 🔧 第二步：项目配置

### 2.1 安装依赖包

```bash
# 安装项目依赖
pip install -r requirements.txt

# 验证安装
pip list | grep django
pip list | grep ninja
```

**依赖包说明**：
- `Django 5.2+` - Web 框架核心
- `django-ninja 1.4+` - API 框架
- `pydantic 2.11+` - 数据验证
- `psycopg2-binary` - PostgreSQL 驱动
- `redis` - 缓存和会话存储
- `celery` - 异步任务队列

### 2.2 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
# Windows: notepad .env
# macOS/Linux: nano .env
```

**`.env` 文件配置**：
```env
# 基本配置
DJANGO_ENVIRONMENT=development
SECRET_KEY=your-super-secret-key-change-this-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置（开发环境使用 SQLite）
DATABASE_URL=sqlite:///db.sqlite3

# Redis 配置（可选，用于缓存）
REDIS_URL=redis://localhost:6379/1

# 邮件配置（开发环境使用控制台输出）
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# 文件存储配置
MEDIA_ROOT=media/
STATIC_ROOT=static/
```

### 2.3 数据库初始化

```bash
# 检查项目配置
python manage.py check

# 创建数据库迁移
python manage.py makemigrations

# 执行数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
# 按提示输入用户名、邮箱和密码
```

## 🏗️ 第三步：理解项目结构

### 3.1 项目目录结构

```
my-enterprise-project/
├── 📁 apps/                    # 应用模块目录
│   ├── 📁 authentication/     # 认证系统
│   ├── 📁 core/               # 核心功能
│   ├── 📁 users/              # 用户管理
│   ├── 📁 permissions/        # 权限管理
│   ├── 📁 storage/            # 文件存储
│   └── 📁 i18n/               # 国际化
├── 📁 core/                   # 项目核心配置
│   ├── 📁 settings/           # 多环境配置
│   ├── 📄 urls.py             # 主路由
│   ├── 📄 wsgi.py             # WSGI 配置
│   └── 📄 asgi.py             # ASGI 配置
├── 📁 docs/                   # 项目文档
├── 📁 frontend/               # 前端代码
├── 📁 static/                 # 静态文件
├── 📁 media/                  # 媒体文件
├── 📄 manage.py               # Django 管理脚本
├── 📄 requirements.txt        # Python 依赖
└── 📄 .env                    # 环境变量
```

### 3.2 核心概念解释

**🔹 应用模块化设计**
- 每个功能模块独立成一个 Django 应用
- 模块间通过明确的接口进行交互
- 便于维护、测试和扩展

**🔹 多环境配置**
- `development` - 开发环境
- `testing` - 测试环境
- `staging` - 预发布环境
- `production` - 生产环境

**🔹 API 优先设计**
- 使用 Django Ninja 构建 RESTful API
- 前后端分离架构
- 自动生成 API 文档

## 🧪 第四步：验证安装

### 4.1 启动开发服务器

```bash
# 启动 Django 开发服务器
python manage.py runserver

# 服务器启动后，您应该看到类似输出：
# Watching for file changes with StatReloader
# Performing system checks...
# System check identified no issues (0 silenced).
# Django version 5.2.4, using settings 'core.settings.development'
# Starting development server at http://127.0.0.1:8000/
# Quit the server with CONTROL-C.
```

### 4.2 访问应用

打开浏览器，访问以下地址：

1. **应用首页**: http://localhost:8000/
   - 应该显示欢迎页面

2. **API 文档**: http://localhost:8000/api/docs/
   - 显示自动生成的 API 文档

3. **管理后台**: http://localhost:8000/admin/
   - 使用之前创建的超级用户登录

4. **健康检查**: http://localhost:8000/health/
   - 显示系统状态信息

### 4.3 测试基本功能

```bash
# 在新的终端窗口中测试 API
curl http://localhost:8000/api/core/info

# 应该返回类似以下的 JSON 响应：
# {
#   "name": "Django Ninja CMS",
#   "version": "1.0.0",
#   "environment": "development"
# }
```

## 📚 第五步：理解核心组件

### 5.1 Django Ninja 框架介绍

Django Ninja 是一个现代的 Django API 框架，具有以下特点：

**🔹 类型提示支持**
```python
from ninja import Router
from pydantic import BaseModel

router = Router()

class UserSchema(BaseModel):
    name: str
    email: str
    age: int

@router.post("/users/")
def create_user(request, user: UserSchema):
    # 自动验证输入数据
    return {"message": f"User {user.name} created"}
```

**🔹 自动文档生成**
- 基于代码自动生成 OpenAPI 文档
- 提供交互式 API 测试界面
- 支持多种文档主题

**🔹 高性能**
- 基于 Pydantic 进行数据验证
- 异步支持
- 比传统 Django REST Framework 更快

### 5.2 项目配置系统

**多环境配置结构**：
```python
# core/settings/base.py - 基础配置
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    # ... 其他应用
    'apps.core',
    'apps.users',
    'apps.authentication',
]

# core/settings/development.py - 开发环境
from .base import *
DEBUG = True
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# core/settings/production.py - 生产环境
from .base import *
DEBUG = False
DATABASES = {
    'default': dj_database_url.parse(os.environ.get('DATABASE_URL'))
}
```

## 🎯 第六步：创建第一个功能

### 6.1 使用代码生成工具

Django Ninja 脚手架项目提供了强大的代码生成工具。我们先创建一个简单的博客模块来演示工具的使用：

```bash
# 生成一个简单的博客模块（作为代码生成工具演示）
python manage.py generate module blog \
  --fields "title:str,content:text,is_published:bool" \
  --admin --api

# 这将创建：
# - apps/blog/models.py (数据模型)
# - apps/blog/api.py (API 接口)
# - apps/blog/schemas.py (数据验证)
# - apps/blog/admin.py (管理界面)
```

> **📝 说明**：这里的blog模块仅作为代码生成工具的演示。在后续的教程中，我们将构建完整的CMS系统，包括文章管理、分类标签、用户权限等企业级功能。blog模块可以作为学习参考，真正的CMS功能将在[内容管理系统教程](企业级项目教程-04-内容管理.md)中详细实现。

### 6.2 注册新模块

```python
# 编辑 core/settings/base.py
INSTALLED_APPS = [
    # ... 现有应用
    'apps.blog',  # 添加新模块
]
```

```bash
# 生成和执行迁移
python manage.py makemigrations blog
python manage.py migrate
```

### 6.3 添加路由

```python
# 编辑 core/urls.py
from apps.blog.api import router as blog_router

# 在 API 路由中添加
api.add_router("/blog/", blog_router)
```

## ✅ 完成检查

完成本教程后，您应该能够：

- [ ] 成功启动开发服务器
- [ ] 访问 API 文档页面
- [ ] 登录管理后台
- [ ] 使用代码生成工具创建新模块
- [ ] 理解项目的基本结构

## 🔍 故障排除

### 常见问题

**1. 虚拟环境激活失败**
```bash
# Windows PowerShell 执行策略问题
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**2. 数据库迁移错误**
```bash
# 重置数据库（开发环境）
rm db.sqlite3
python manage.py migrate
```

**3. 端口被占用**
```bash
# 使用其他端口
python manage.py runserver 8001
```

## 📖 下一步

恭喜！您已经成功搭建了企业级项目的基础架构。

**接下来学习**：
- [用户管理系统](企业级项目教程-02-用户管理.md) - 实现完整的用户认证和权限管理

**相关文档**：
- [项目结构说明](项目结构说明.md) - 详细的项目结构解释
- [开发指南](开发指南.md) - 开发最佳实践
- [代码生成工具指南](代码生成工具指南.md) - 代码生成工具详解

---

**🎉 基础搭建完成！** 您现在拥有了一个功能完整的企业级项目基础架构。
