# Python模型文件代码生成指南

## 📖 概述

基于Python模型文件的代码生成工具，可以从标准的Django模型文件自动生成完整的应用代码，包括模型、Admin配置、Views等。

## 🚀 快速开始

### 1. 准备模型文件

创建一个包含Django模型的Python文件，例如 `blog_models.py`：

```python
"""
博客应用模型
应用名称: blog
"""

from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Category(models.Model):
    """文章分类"""
    name = models.CharField(max_length=100, verbose_name="分类名称")
    slug = models.SlugField(max_length=100, unique=True, verbose_name="URL别名")
    description = models.TextField(blank=True, verbose_name="分类描述")
    
    class Meta:
        verbose_name = "文章分类"
        verbose_name_plural = "文章分类"

class Post(models.Model):
    """博客文章"""
    title = models.CharField(max_length=200, verbose_name="标题")
    slug = models.SlugField(max_length=200, unique=True, verbose_name="URL别名")
    content = models.TextField(verbose_name="内容")
    excerpt = models.TextField(max_length=500, blank=True, verbose_name="摘要")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="分类")
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="作者")
    is_published = models.BooleanField(default=False, verbose_name="是否发布")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "博客文章"
        verbose_name_plural = "博客文章"
        ordering = ['-created_at']
```

### 2. 生成代码

```bash
# 生成完整应用到 apps 目录
uv run python manage.py generate_from_config --models blog_models.py --full --output-dir apps/blog

# 仅生成模型文件到 generated 目录
uv run python manage.py generate_from_config --models blog_models.py --models-only --output-dir generated/blog

# 仅生成Admin配置
uv run python manage.py generate_from_config --models blog_models.py --admin-only --output-dir generated/blog

# 仅生成Views文件
uv run python manage.py generate_from_config --models blog_models.py --views-only --output-dir generated/blog
```

## 📋 命令参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--models` | Python模型文件路径 | `--models blog_models.py` |
| `--full` | 生成完整应用 | `--full` |
| `--models-only` | 仅生成模型文件 | `--models-only` |
| `--admin-only` | 仅生成Admin文件 | `--admin-only` |
| `--views-only` | 仅生成Views文件 | `--views-only` |
| `--output-dir` | 输出目录 | `--output-dir apps/blog` |
| `--dry-run` | 预览模式，不实际生成文件 | `--dry-run` |

## 🎯 生成内容

### 完整应用 (`--full`)
- `__init__.py` - 应用初始化文件
- `apps.py` - 应用配置
- `models.py` - 数据模型
- `admin.py` - Admin配置
- `views.py` - 视图函数
- `migrations/__init__.py` - 迁移文件目录

### 单独文件
- `--models-only`: 仅生成 `models.py`
- `--admin-only`: 仅生成 `admin.py`
- `--views-only`: 仅生成 `views.py`

## 🔧 支持的字段类型

工具会自动解析以下Django字段类型：

| Django字段 | 内部类型 | 说明 |
|------------|----------|------|
| `CharField` | `str` | 字符串字段 |
| `TextField` | `text` | 文本字段 |
| `IntegerField` | `int` | 整数字段 |
| `FloatField` | `float` | 浮点数字段 |
| `BooleanField` | `bool` | 布尔字段 |
| `DateField` | `date` | 日期字段 |
| `DateTimeField` | `datetime` | 日期时间字段 |
| `EmailField` | `email` | 邮箱字段 |
| `URLField` | `url` | URL字段 |
| `SlugField` | `slug` | Slug字段 |
| `JSONField` | `json` | JSON字段 |
| `ForeignKey` | `fk` | 外键字段 |
| `ManyToManyField` | `m2m` | 多对多字段 |
| `OneToOneField` | `o2o` | 一对一字段 |

## 📝 使用示例

### 示例1：电商系统

```python
"""
电商系统模型
应用名称: shop
"""

from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Product(models.Model):
    """商品"""
    name = models.CharField(max_length=200, verbose_name="商品名称")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="价格")
    stock = models.IntegerField(default=0, verbose_name="库存")
    description = models.TextField(verbose_name="商品描述")
    image = models.ImageField(upload_to='products/', verbose_name="商品图片")
    is_active = models.BooleanField(default=True, verbose_name="是否上架")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

class Order(models.Model):
    """订单"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    products = models.ManyToManyField(Product, verbose_name="商品")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="总金额")
    status = models.CharField(max_length=20, default='pending', verbose_name="订单状态")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
```

生成命令：
```bash
uv run python manage.py generate_from_config --models shop_models.py --full --output-dir apps/shop
```

### 示例2：仅生成Admin配置

```bash
uv run python manage.py generate_from_config --models shop_models.py --admin-only --output-dir generated/shop
```

## 🔄 后续步骤

生成代码后，需要进行以下步骤：

1. **添加到INSTALLED_APPS**
```python
# settings.py
INSTALLED_APPS = [
    # ...
    'apps.blog',  # 添加生成的应用
]
```

2. **生成和应用迁移**
```bash
uv run python manage.py makemigrations blog
uv run python manage.py migrate
```

3. **添加URL路由**
```python
# core/urls.py
urlpatterns = [
    # ...
    path('blog/', include('apps.blog.urls')),
]
```

4. **创建URL配置文件**
```python
# apps/blog/urls.py
from django.urls import path
from . import views

app_name = 'blog'

urlpatterns = [
    path('', views.post_list, name='post_list'),
    path('<int:pk>/', views.post_detail, name='post_detail'),
    path('create/', views.post_create, name='post_create'),
    path('<int:pk>/edit/', views.post_update, name='post_update'),
    path('<int:pk>/delete/', views.post_delete, name='post_delete'),
]
```

## 🎨 自定义和扩展

生成的代码是基础模板，您可以根据需要进行自定义：

1. **修改模型字段**：在生成的 `models.py` 中调整字段定义
2. **扩展Admin功能**：在 `admin.py` 中添加更多配置
3. **自定义视图逻辑**：在 `views.py` 中实现具体的业务逻辑
4. **添加模板文件**：创建对应的HTML模板文件

## ⚠️ 注意事项

1. **备份现有代码**：生成前请备份现有文件，避免覆盖
2. **检查字段类型**：确保模型文件中的字段类型正确
3. **验证生成结果**：生成后检查代码是否符合预期
4. **测试功能**：运行测试确保生成的代码正常工作

## 🔍 故障排除

### 常见问题

1. **模型文件解析失败**
   - 检查Python语法是否正确
   - 确保模型类继承自 `models.Model`

2. **字段类型不支持**
   - 查看支持的字段类型列表
   - 使用标准Django字段类型

3. **生成文件为空**
   - 检查模型文件是否包含有效的模型类
   - 确保字段定义正确

4. **权限错误**
   - 检查输出目录的写入权限
   - 使用 `--dry-run` 预览生成内容

## 📚 相关文档

- [Django模型文档](https://docs.djangoproject.com/en/stable/topics/db/models/)
- [Django Admin文档](https://docs.djangoproject.com/en/stable/ref/contrib/admin/)
- [Django视图文档](https://docs.djangoproject.com/en/stable/topics/http/views/)