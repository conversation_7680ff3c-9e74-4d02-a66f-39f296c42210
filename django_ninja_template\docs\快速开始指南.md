# 🚀 Django Ninja 脚手架项目快速开始指南

欢迎使用 Django Ninja 脚手架项目！本指南将帮助您在 5 分钟内快速上手。

## 🚀 一键启动

> **环境要求**: Python 3.11+，详细安装配置请参考 [开发指南](开发指南.md#环境安装)

### 使用启动脚本（推荐）

```bash
# 使用启动脚本，自动完成所有配置
python start.py
```

### 手动启动

```bash
# 快速启动（假设已配置环境）
python manage.py runserver
```

## 🌐 访问应用

启动成功后，您可以访问：

- **应用首页**: http://localhost:8000/
- **API 文档**: http://localhost:8000/api/docs/
- **管理后台**: http://localhost:8000/admin/
- **健康检查**: http://localhost:8000/health/

## 🔧 快速生成 CMS 模块

使用内置的代码生成工具快速创建完整的 CMS 系统：

```bash
# 生成完整的 CMS 模块（包含前端组件）
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend

# 预览生成内容（不实际创建文件）
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend --dry-run
```

生成后需要执行：

```bash
# 1. 添加应用到 INSTALLED_APPS（手动编辑 core/settings/base.py）
# 2. 生成迁移文件
python manage.py makemigrations cms

# 3. 执行迁移
python manage.py migrate

# 4. 生成权限
python manage.py auto_generate_permissions cms --create-roles

# 5. 添加路由（手动编辑 core/urls.py）
```

## 🎯 核心功能体验

### 1. 用户认证

```bash
# 注册新用户
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "username": "testuser", "password": "testpass123"}'

# 用户登录
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "testpass123"}'
```

### 2. 文件上传

```bash
# 上传文件
curl -X POST http://localhost:8000/api/storage/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@example.jpg"
```

### 3. 权限检查

```bash
# 检查权限
curl -X GET http://localhost:8000/api/permissions/check-permission/users.view \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🐳 Docker 部署

### 开发环境

```bash
# 启动开发环境
docker-compose up -d
```

### 生产环境

```bash
# 启动生产环境
DJANGO_ENVIRONMENT=production docker-compose -f docker-compose.prod.yml up -d
```

## ⚙️ 环境配置

### 环境变量

创建 `.env` 文件：

```env
# 基本配置
DJANGO_ENVIRONMENT=development
SECRET_KEY=your-secret-key-here
DEBUG=True

# 数据库配置
DATABASE_URL=sqlite:///db.sqlite3
# DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# Redis 配置
REDIS_URL=redis://localhost:6379/1

# Celery 配置
CELERY_BROKER_URL=redis://localhost:6379/0

# 邮件配置
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-password
```

### 多环境支持

项目支持多种环境配置：

- `development` - 开发环境（默认）
- `testing` - 测试环境
- `staging` - 预发布环境
- `production` - 生产环境

通过设置 `DJANGO_ENVIRONMENT` 环境变量来切换。

## 🧪 运行测试

```bash
# 运行所有测试
python manage.py test

# 运行特定应用的测试
python manage.py test apps.users

# 生成测试覆盖率报告
coverage run --source='.' manage.py test
coverage report
coverage html  # 生成 HTML 报告
```

## 📚 下一步

现在您已经成功启动了 Django Ninja 脚手架项目，建议您：

1. **阅读完整文档**：查看 [项目文档导航](文档导航.md)
2. **学习代码生成**：阅读 [代码生成工具指南](代码生成工具指南.md)
3. **了解开发流程**：查看 [开发指南](开发指南.md)
4. **体验 CMS 功能**：尝试 [CMS 教程](CMS教程.md)

## 🆘 遇到问题？

如果您遇到问题：

1. 查看 [常见问题解答](开发指南.md#常见问题)
2. 检查日志文件：`logs/django.log`
3. 访问健康检查：http://localhost:8000/health/
4. 在 GitHub 上创建 Issue

---

**恭喜！** 您已经成功启动了 Django Ninja 脚手架项目。开始您的开发之旅吧！ 🎉
