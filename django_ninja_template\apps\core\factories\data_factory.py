"""
数据工厂和种子数据生成器
自动生成测试数据和初始数据，支持复杂关联关系
"""
import factory
from factory.django import DjangoModelFactory
from django.contrib.auth import get_user_model
from django.db import models
from django.apps import apps
from typing import Type, Dict, Any, List, Optional, Union
import random
import logging
from datetime import datetime, timedelta
from decimal import Decimal

logger = logging.getLogger(__name__)
User = get_user_model()


class BaseFactory(DjangoModelFactory):
    """基础工厂类"""
    
    class Meta:
        abstract = True
    
    @classmethod
    def create_batch_with_relations(cls, size: int, **kwargs) -> List:
        """批量创建带关联关系的对象"""
        return [cls.create(**kwargs) for _ in range(size)]


class UserFactory(BaseFactory):
    """用户工厂"""
    
    class Meta:
        model = User
    
    email = factory.Sequence(lambda n: f'user{n}@example.com')
    username = factory.Sequence(lambda n: f'user{n}')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_verified = True
    is_active = True


class AutoFactoryGenerator:
    """自动工厂生成器"""
    
    def __init__(self):
        self.factories = {}
        self.field_generators = {
            models.CharField: self._generate_char_field,
            models.TextField: self._generate_text_field,
            models.IntegerField: self._generate_int_field,
            models.FloatField: self._generate_float_field,
            models.DecimalField: self._generate_decimal_field,
            models.BooleanField: self._generate_bool_field,
            models.DateField: self._generate_date_field,
            models.DateTimeField: self._generate_datetime_field,
            models.EmailField: self._generate_email_field,
            models.URLField: self._generate_url_field,
            models.ForeignKey: self._generate_foreign_key_field,
            models.JSONField: self._generate_json_field,
        }
    
    def generate_factory_for_model(self, model: Type[models.Model]) -> Type[DjangoModelFactory]:
        """为模型生成工厂类"""
        if model in self.factories:
            return self.factories[model]
        
        factory_name = f'{model.__name__}Factory'
        factory_attrs = {'Meta': type('Meta', (), {'model': model})}
        
        # 生成字段
        for field in model._meta.fields:
            if self._should_generate_field(field):
                field_generator = self._get_field_generator(field)
                if field_generator:
                    factory_attrs[field.name] = field_generator
        
        # 创建工厂类
        factory_class = type(factory_name, (BaseFactory,), factory_attrs)
        self.factories[model] = factory_class
        
        return factory_class
    
    def _should_generate_field(self, field) -> bool:
        """判断是否应该为字段生成数据"""
        # 跳过自动字段
        if isinstance(field, models.AutoField):
            return False
        
        # 跳过自动时间字段
        if field.auto_now or field.auto_now_add:
            return False
        
        # 跳过密码字段
        if field.name in ['password', 'token']:
            return False
        
        return True
    
    def _get_field_generator(self, field):
        """获取字段生成器"""
        field_type = type(field)
        generator_func = self.field_generators.get(field_type)
        
        if generator_func:
            return generator_func(field)
        
        # 默认生成器
        return factory.Faker('word')
    
    def _generate_char_field(self, field):
        """生成字符字段"""
        if field.name in ['name', 'title']:
            return factory.Faker('company')
        elif field.name in ['first_name']:
            return factory.Faker('first_name')
        elif field.name in ['last_name']:
            return factory.Faker('last_name')
        elif field.name in ['username']:
            return factory.Sequence(lambda n: f'user{n}')
        elif field.name in ['phone']:
            return factory.Faker('phone_number')
        elif field.name in ['address']:
            return factory.Faker('address')
        elif field.name in ['city']:
            return factory.Faker('city')
        elif field.name in ['country']:
            return factory.Faker('country')
        elif field.name in ['status']:
            if field.choices:
                return factory.Faker('random_element', elements=[choice[0] for choice in field.choices])
            return factory.Faker('word')
        elif field.name in ['slug']:
            return factory.Faker('slug')
        elif field.name in ['color']:
            return factory.Faker('hex_color')
        else:
            if field.max_length and field.max_length <= 50:
                return factory.Faker('word')
            else:
                return factory.Faker('sentence', nb_words=4)
    
    def _generate_text_field(self, field):
        """生成文本字段"""
        if field.name in ['description', 'content', 'summary']:
            return factory.Faker('text', max_nb_chars=500)
        elif field.name in ['bio']:
            return factory.Faker('paragraph', nb_sentences=3)
        else:
            return factory.Faker('text')
    
    def _generate_int_field(self, field):
        """生成整数字段"""
        if field.name in ['age']:
            return factory.Faker('random_int', min=18, max=80)
        elif field.name in ['price', 'amount']:
            return factory.Faker('random_int', min=100, max=10000)
        elif field.name in ['quantity', 'count']:
            return factory.Faker('random_int', min=1, max=100)
        elif field.name in ['rating']:
            return factory.Faker('random_int', min=1, max=5)
        else:
            return factory.Faker('random_int', min=1, max=1000)
    
    def _generate_float_field(self, field):
        """生成浮点字段"""
        if field.name in ['price', 'amount']:
            return factory.Faker('pyfloat', left_digits=4, right_digits=2, positive=True)
        elif field.name in ['rating']:
            return factory.Faker('pyfloat', left_digits=1, right_digits=1, min_value=1, max_value=5)
        else:
            return factory.Faker('pyfloat', left_digits=3, right_digits=2, positive=True)
    
    def _generate_decimal_field(self, field):
        """生成十进制字段"""
        max_digits = getattr(field, 'max_digits', 10)
        decimal_places = getattr(field, 'decimal_places', 2)
        
        return factory.LazyFunction(
            lambda: Decimal(str(random.uniform(1, 10**(max_digits-decimal_places))))
        )
    
    def _generate_bool_field(self, field):
        """生成布尔字段"""
        if field.name in ['is_active', 'is_published', 'is_verified']:
            return factory.Faker('boolean', chance_of_getting_true=80)
        else:
            return factory.Faker('boolean')
    
    def _generate_date_field(self, field):
        """生成日期字段"""
        if field.name in ['birth_date']:
            return factory.Faker('date_of_birth', minimum_age=18, maximum_age=80)
        elif field.name in ['start_date']:
            return factory.Faker('date_between', start_date='-1y', end_date='today')
        elif field.name in ['end_date']:
            return factory.Faker('date_between', start_date='today', end_date='+1y')
        else:
            return factory.Faker('date_between', start_date='-1y', end_date='+1y')
    
    def _generate_datetime_field(self, field):
        """生成日期时间字段"""
        if field.name in ['published_at']:
            return factory.Faker('date_time_between', start_date='-1y', end_date='now')
        else:
            return factory.Faker('date_time_between', start_date='-1y', end_date='+1y')
    
    def _generate_email_field(self, field):
        """生成邮箱字段"""
        return factory.Faker('email')
    
    def _generate_url_field(self, field):
        """生成 URL 字段"""
        if field.name in ['website']:
            return factory.Faker('url')
        elif field.name in ['avatar', 'image']:
            return factory.Faker('image_url')
        else:
            return factory.Faker('url')
    
    def _generate_foreign_key_field(self, field):
        """生成外键字段"""
        related_model = field.related_model
        
        # 如果是用户外键，使用 UserFactory
        if related_model == User:
            return factory.SubFactory(UserFactory)
        
        # 为相关模型生成工厂
        related_factory = self.generate_factory_for_model(related_model)
        return factory.SubFactory(related_factory)
    
    def _generate_json_field(self, field):
        """生成 JSON 字段"""
        if field.name in ['metadata', 'settings']:
            return factory.LazyFunction(
                lambda: {
                    'key1': factory.Faker('word').generate(),
                    'key2': factory.Faker('random_int', min=1, max=100).generate(),
                    'key3': factory.Faker('boolean').generate(),
                }
            )
        else:
            return factory.LazyFunction(lambda: {})


class DataSeeder:
    """数据种子生成器"""
    
    def __init__(self):
        self.factory_generator = AutoFactoryGenerator()
        self.created_objects = {}
        self.stats = {}
    
    def register_factory(self, model: Type[models.Model], factory_class: Type[DjangoModelFactory]):
        """注册模型工厂"""
        self.factory_generator.factories[model] = factory_class
    
    def seed_model(self, model: Type[models.Model], count: int = 10, **kwargs) -> List:
        """为模型生成种子数据"""
        factory_class = self.factory_generator.generate_factory_for_model(model)
        
        logger.info(f'正在为 {model.__name__} 生成 {count} 条数据...')
        
        objects = []
        for i in range(count):
            try:
                obj = factory_class.create(**kwargs)
                objects.append(obj)
            except Exception as e:
                logger.error(f'创建 {model.__name__} 对象失败: {e}')
                continue
        
        self.created_objects[model] = objects
        self.stats[model.__name__] = len(objects)
        
        logger.info(f'成功为 {model.__name__} 生成了 {len(objects)} 条数据')
        return objects
    
    def seed_app(self, app_name: str, config: Dict[str, Dict[str, Any]]):
        """为整个应用生成种子数据"""
        try:
            app_config = apps.get_app_config(app_name)
        except LookupError:
            logger.error(f"应用 {app_name} 不存在")
            return
        
        logger.info(f'开始为应用 {app_name} 生成种子数据...')
        
        # 按依赖关系排序模型
        models_to_seed = self._sort_models_by_dependencies(app_config.get_models())
        
        for model in models_to_seed:
            model_name = model.__name__
            model_config = config.get(model_name, {})
            
            count = model_config.get('count', 10)
            kwargs = model_config.get('kwargs', {})
            
            self.seed_model(model, count, **kwargs)
    
    def _sort_models_by_dependencies(self, models: List[Type[models.Model]]) -> List[Type[models.Model]]:
        """按依赖关系排序模型"""
        # 简单的依赖排序：先创建没有外键的模型，再创建有外键的模型
        no_fk_models = []
        fk_models = []
        
        for model in models:
            has_fk = any(isinstance(field, models.ForeignKey) for field in model._meta.fields)
            if has_fk:
                fk_models.append(model)
            else:
                no_fk_models.append(model)
        
        return no_fk_models + fk_models
    
    def clear_model_data(self, model: Type[models.Model]):
        """清除模型数据"""
        if model in self.created_objects:
            objects = self.created_objects[model]
            model.objects.filter(id__in=[obj.id for obj in objects]).delete()
            del self.created_objects[model]
            logger.info(f'清除了 {model.__name__} 的 {len(objects)} 条数据')
    
    def clear_all_data(self):
        """清除所有生成的数据"""
        for model in self.created_objects.keys():
            self.clear_model_data(model)
        self.created_objects.clear()
        self.stats.clear()
    
    def get_stats(self) -> Dict[str, int]:
        """获取生成统计"""
        return self.stats.copy()


# 便捷函数
def create_test_data(app_name: str, config: Optional[Dict[str, Dict[str, Any]]] = None) -> DataSeeder:
    """创建测试数据的便捷函数"""
    seeder = DataSeeder()
    
    if config is None:
        config = {}
    
    seeder.seed_app(app_name, config)
    return seeder


def create_demo_users(count: int = 10) -> List[User]:
    """创建演示用户"""
    users = []
    for i in range(count):
        user = UserFactory.create()
        users.append(user)
    
    logger.info(f'创建了 {count} 个演示用户')
    return users
