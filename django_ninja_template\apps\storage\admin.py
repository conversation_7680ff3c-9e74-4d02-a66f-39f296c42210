"""
Admin configuration for storage app.
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import StorageFile, FileShare, FileCategory, ImageThumbnail, UploadSession


@admin.register(StorageFile)
class StorageFileAdmin(admin.ModelAdmin):
    """
    Admin configuration for StorageFile model.
    """
    list_display = (
        'original_name', 'uploaded_by', 'content_type', 'file_size_display',
        'status', 'is_public', 'download_count', 'created_at'
    )
    list_filter = (
        'status', 'is_public', 'category',
        'created_at', 'updated_at'
    )
    search_fields = ('original_name', 'uploaded_by__email', 'uploaded_by__username')
    ordering = ('-created_at',)
    readonly_fields = (
        'id', 'file_size', 'content_type', 'file_hash',
        'download_count', 'created_at', 'updated_at',
        'file_preview', 'download_link'
    )
    
    fieldsets = (
        (_('File Info'), {
            'fields': ('id', 'uploaded_by', 'original_name', 'file', 'file_preview')
        }),
        (_('File Details'), {
            'fields': ('file_size', 'content_type', 'file_hash', 'category')
        }),
        (_('Access Control'), {
            'fields': ('is_public', 'status')
        }),
        (_('Usage Stats'), {
            'fields': ('download_count', 'download_link')
        }),
        (_('Metadata'), {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def file_size_display(self, obj):
        """Display file size in human readable format."""
        size = obj.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    file_size_display.short_description = _('File Size')
    
    def file_preview(self, obj):
        """Show file preview for images."""
        if obj.is_image and obj.file:
            return format_html(
                '<img src="{}" style="max-width: 200px; max-height: 200px;" />',
                obj.file.url
            )
        return _('No preview available')
    file_preview.short_description = _('Preview')
    
    def download_link(self, obj):
        """Show download link."""
        if obj.file:
            return format_html(
                '<a href="/api/storage/files/{}/download" target="_blank">Download</a>',
                obj.id
            )
        return _('No file')
    download_link.short_description = _('Download')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('uploaded_by', 'category')


@admin.register(FileShare)
class FileShareAdmin(admin.ModelAdmin):
    """
    Admin configuration for FileShare model.
    """
    list_display = (
        'file_name', 'shared_by', 'is_active', 'download_count',
        'expires_at', 'created_at'
    )
    list_filter = (
        'is_active', 'created_at', 'expires_at'
    )
    search_fields = (
        'file__original_name', 'shared_by__email', 'share_token'
    )
    ordering = ('-created_at',)
    readonly_fields = (
        'share_token', 'download_count', 'created_at', 'share_link'
    )
    
    fieldsets = (
        (_('Share Info'), {
            'fields': ('file', 'shared_by', 'share_token')
        }),
        (_('Settings'), {
            'fields': ('is_active', 'expires_at', 'download_limit')
        }),
        (_('Usage Stats'), {
            'fields': ('download_count', 'share_link')
        }),
        (_('Timestamps'), {
            'fields': ('created_at',)
        }),
    )
    
    def file_name(self, obj):
        """Display file name."""
        return obj.file.original_name
    file_name.short_description = _('File Name')
    
    def share_link(self, obj):
        """Show share link."""
        share_url = f"/api/storage/share/{obj.share_token}"
        return format_html(
            '<a href="{}" target="_blank">{}</a>',
            share_url,
            share_url
        )
    share_link.short_description = _('Share Link')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'file', 'shared_by'
        )


@admin.register(FileCategory)
class FileCategoryAdmin(admin.ModelAdmin):
    """Admin configuration for FileCategory model."""
    list_display = ('name', 'description', 'max_file_size_display')
    search_fields = ('name', 'description')
    
    def max_file_size_display(self, obj):
        """Display max file size in human readable format."""
        size = obj.max_file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    max_file_size_display.short_description = _('Max File Size')


@admin.register(ImageThumbnail)
class ImageThumbnailAdmin(admin.ModelAdmin):
    """Admin configuration for ImageThumbnail model."""
    list_display = ('original_file_name', 'size', 'created_at')
    list_filter = ('size', 'created_at')
    search_fields = ('original_file__original_name',)
    
    def original_file_name(self, obj):
        return obj.original_file.original_name
    original_file_name.short_description = _('Original File')


@admin.register(UploadSession)
class UploadSessionAdmin(admin.ModelAdmin):
    """Admin configuration for UploadSession model."""
    list_display = ('filename', 'user', 'upload_progress_display', 'is_completed', 'created_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('filename', 'user__email')
    readonly_fields = ('session_id', 'upload_progress_display', 'is_completed')
    
    def upload_progress_display(self, obj):
        return f"{obj.upload_progress:.1f}%"
    upload_progress_display.short_description = _('Progress')
