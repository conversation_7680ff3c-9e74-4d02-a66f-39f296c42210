"""
Celery configuration for django_ninja_template project.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

app = Celery('django_ninja_template')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery configuration
app.conf.update(
    # Task routing
    task_routes={
        'apps.core.tasks.*': {'queue': 'core'},
        'apps.users.tasks.*': {'queue': 'users'},
        'apps.authentication.tasks.*': {'queue': 'auth'},
    },
    
    # Task execution
    task_always_eager=False,
    task_eager_propagates=True,
    task_ignore_result=False,
    task_store_eager_result=True,
    
    # Task time limits
    task_soft_time_limit=300,  # 5 minutes
    task_time_limit=600,       # 10 minutes
    
    # Worker configuration
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    
    # Result backend configuration
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Security
    task_reject_on_worker_lost=True,
    task_acks_late=True,
    
    # Beat schedule (for periodic tasks)
    beat_schedule={
        'cleanup-expired-tokens': {
            'task': 'apps.authentication.tasks.cleanup_expired_tokens',
            'schedule': 3600.0,  # Run every hour
        },
        'cleanup-old-logs': {
            'task': 'apps.core.tasks.cleanup_old_logs',
            'schedule': 86400.0,  # Run daily
        },
    },
    timezone=settings.TIME_ZONE,
)


@app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup."""
    print(f'Request: {self.request!r}')
