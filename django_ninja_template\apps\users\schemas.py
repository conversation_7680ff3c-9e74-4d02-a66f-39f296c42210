"""
Pydantic schemas for users API.
"""
from typing import Optional
from datetime import date, datetime
from pydantic import BaseModel, EmailStr, Field


class UserResponse(BaseModel):
    """Schema for user response."""
    id: int
    email: str
    username: str
    first_name: str
    last_name: str
    phone: Optional[str] = None
    bio: Optional[str] = None
    birth_date: Optional[date] = None
    is_verified: bool
    is_premium: bool
    timezone: str
    language: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserUpdateRequest(BaseModel):
    """Schema for user update request."""
    first_name: Optional[str] = Field(None, min_length=1, max_length=150)
    last_name: Optional[str] = Field(None, min_length=1, max_length=150)
    phone: Optional[str] = Field(None, max_length=20)
    bio: Optional[str] = Field(None, max_length=500)
    birth_date: Optional[date] = None
    timezone: Optional[str] = Field(None, max_length=50)
    language: Optional[str] = Field(None, max_length=10)


class UserProfileResponse(BaseModel):
    """Schema for user profile response."""
    user: UserResponse
    profile: dict
    
    class Config:
        from_attributes = True


class UserProfileUpdateRequest(BaseModel):
    """Schema for user profile update request."""
    website: Optional[str] = None
    github: Optional[str] = Field(None, max_length=100)
    linkedin: Optional[str] = Field(None, max_length=100)
    twitter: Optional[str] = Field(None, max_length=100)
    company: Optional[str] = Field(None, max_length=200)
    job_title: Optional[str] = Field(None, max_length=200)
    location: Optional[str] = Field(None, max_length=200)
    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    marketing_emails: Optional[bool] = None
    profile_visibility: Optional[str] = Field(None, pattern="^(public|private|friends)$")


class UserListResponse(BaseModel):
    """Schema for user list response."""
    users: list[UserResponse]
    total: int
    page: int
    per_page: int
    pages: int
