"""
Core models for the application.
"""

import json
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class BaseModel(models.Model):
    """
    Abstract base model with common fields.
    """

    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        abstract = True


class SystemConfiguration(BaseModel):
    """
    Model to store system-wide configuration settings.
    """

    key = models.CharField(_("key"), max_length=255, unique=True)
    value = models.TextField(_("value"))
    description = models.TextField(_("description"), blank=True)
    is_active = models.BooleanField(_("is active"), default=True)

    class Meta:
        verbose_name = _("System Configuration")
        verbose_name_plural = _("System Configurations")
        db_table = "core_system_configurations"

    def __str__(self):
        return f"{self.key}: {self.value[:50]}"


class AuditLog(BaseModel):
    """
    Model to store audit logs for important actions.
    """

    user = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="audit_logs",
    )
    action = models.CharField(_("action"), max_length=100)
    resource_type = models.CharField(_("resource type"), max_length=100)
    resource_id = models.CharField(_("resource ID"), max_length=100, blank=True)
    details = models.JSONField(_("details"), default=dict)
    ip_address = models.GenericIPAddressField(_("IP address"), blank=True, null=True)
    user_agent = models.TextField(_("user agent"), blank=True)

    class Meta:
        verbose_name = _("Audit Log")
        verbose_name_plural = _("Audit Logs")
        db_table = "core_audit_logs"
        indexes = [
            models.Index(fields=["user", "created_at"]),
            models.Index(fields=["action", "created_at"]),
            models.Index(fields=["resource_type", "resource_id"]),
        ]

    def __str__(self):
        user_str = self.user.email if self.user else "Anonymous"
        return f"{user_str} - {self.action} on {self.resource_type}"
