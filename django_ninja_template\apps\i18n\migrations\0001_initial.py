# Generated by Django 5.2.4 on 2025-07-14 08:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Language",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="language code"
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="language name"),
                ),
                (
                    "native_name",
                    models.CharField(max_length=100, verbose_name="native name"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="is active"),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="is default"),
                ),
                (
                    "text_direction",
                    models.Char<PERSON>ield(
                        choices=[("ltr", "Left to Right"), ("rtl", "Right to Left")],
                        default="ltr",
                        max_length=3,
                        verbose_name="text direction",
                    ),
                ),
            ],
            options={
                "verbose_name": "Language",
                "verbose_name_plural": "Languages",
                "db_table": "i18n_languages",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="TranslationRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "key",
                    models.CharField(max_length=255, verbose_name="translation key"),
                ),
                ("source_text", models.TextField(verbose_name="source text")),
                (
                    "translated_text",
                    models.TextField(blank=True, verbose_name="translated text"),
                ),
                (
                    "context",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="context"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                ("priority", models.IntegerField(default=1, verbose_name="priority")),
                (
                    "deadline",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="deadline"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="notes")),
            ],
            options={
                "verbose_name": "Translation Request",
                "verbose_name_plural": "Translation Requests",
                "db_table": "i18n_translation_requests",
            },
        ),
        migrations.CreateModel(
            name="Translation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "key",
                    models.CharField(max_length=255, verbose_name="translation key"),
                ),
                ("value", models.TextField(verbose_name="translated value")),
                (
                    "context",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="context"
                    ),
                ),
                (
                    "is_fuzzy",
                    models.BooleanField(default=False, verbose_name="is fuzzy"),
                ),
                (
                    "is_approved",
                    models.BooleanField(default=False, verbose_name="is approved"),
                ),
                (
                    "language",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="translations",
                        to="i18n.language",
                    ),
                ),
            ],
            options={
                "verbose_name": "Translation",
                "verbose_name_plural": "Translations",
                "db_table": "i18n_translations",
            },
        ),
    ]
