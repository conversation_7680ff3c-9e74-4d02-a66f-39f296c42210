"""
Internationalization services.
"""
from typing import Dict, Optional, List
from django.utils.translation import get_language, activate
from django.conf import settings
from apps.core.services import CacheService
from .models import Language, Translation


class I18nService:
    """
    Service class for internationalization operations.
    """
    
    CACHE_TIMEOUT = 3600  # 1 hour
    
    @classmethod
    def get_supported_languages(cls) -> List[Dict]:
        """
        Get list of supported languages.
        """
        cache_key = "supported_languages"
        languages = CacheService.get(cache_key)
        
        if languages is None:
            languages = []
            for lang in Language.objects.filter(is_active=True).order_by('name'):
                languages.append({
                    'code': lang.code,
                    'name': lang.name,
                    'native_name': lang.native_name,
                    'text_direction': lang.text_direction,
                    'is_default': lang.is_default,
                })
            
            CacheService.set(cache_key, languages, cls.CACHE_TIMEOUT)
        
        return languages
    
    @classmethod
    def get_default_language(cls) -> Optional[str]:
        """
        Get default language code.
        """
        cache_key = "default_language"
        default_lang = CacheService.get(cache_key)
        
        if default_lang is None:
            try:
                lang = Language.objects.get(is_default=True, is_active=True)
                default_lang = lang.code
            except Language.DoesNotExist:
                default_lang = settings.LANGUAGE_CODE
            
            CacheService.set(cache_key, default_lang, cls.CACHE_TIMEOUT)
        
        return default_lang
    
    @classmethod
    def get_translation(cls, key: str, language_code: str = None, context: str = "", default: str = None) -> str:
        """
        Get translation for a key in specified language.
        """
        if language_code is None:
            language_code = get_language() or cls.get_default_language()
        
        # Try to get from cache first
        cache_key = f"translation_{language_code}_{key}_{context}"
        translation = CacheService.get(cache_key)
        
        if translation is None:
            try:
                language = Language.objects.get(code=language_code, is_active=True)
                translation_obj = Translation.objects.get(
                    key=key,
                    language=language,
                    context=context,
                    is_approved=True
                )
                translation = translation_obj.value
            except (Language.DoesNotExist, Translation.DoesNotExist):
                # Fallback to default language
                default_lang_code = cls.get_default_language()
                if language_code != default_lang_code:
                    try:
                        default_language = Language.objects.get(code=default_lang_code, is_active=True)
                        translation_obj = Translation.objects.get(
                            key=key,
                            language=default_language,
                            context=context,
                            is_approved=True
                        )
                        translation = translation_obj.value
                    except (Language.DoesNotExist, Translation.DoesNotExist):
                        translation = default or key
                else:
                    translation = default or key
            
            # Cache the result
            CacheService.set(cache_key, translation, cls.CACHE_TIMEOUT)
        
        return translation
    
    @classmethod
    def set_translation(cls, key: str, language_code: str, value: str, context: str = "", is_approved: bool = False) -> bool:
        """
        Set translation for a key.
        """
        try:
            language = Language.objects.get(code=language_code, is_active=True)
            
            translation, created = Translation.objects.update_or_create(
                key=key,
                language=language,
                context=context,
                defaults={
                    'value': value,
                    'is_approved': is_approved,
                    'is_fuzzy': not is_approved
                }
            )
            
            # Clear cache
            cache_key = f"translation_{language_code}_{key}_{context}"
            CacheService.delete(cache_key)
            
            return True
        except Language.DoesNotExist:
            return False
    
    @classmethod
    def get_translations_for_language(cls, language_code: str, approved_only: bool = True) -> Dict[str, str]:
        """
        Get all translations for a language.
        """
        cache_key = f"all_translations_{language_code}_{approved_only}"
        translations = CacheService.get(cache_key)
        
        if translations is None:
            try:
                language = Language.objects.get(code=language_code, is_active=True)
                queryset = Translation.objects.filter(language=language)
                
                if approved_only:
                    queryset = queryset.filter(is_approved=True)
                
                translations = {}
                for translation in queryset:
                    key = translation.key
                    if translation.context:
                        key = f"{key}:{translation.context}"
                    translations[key] = translation.value
                
                CacheService.set(cache_key, translations, cls.CACHE_TIMEOUT)
            except Language.DoesNotExist:
                translations = {}
        
        return translations
    
    @classmethod
    def get_missing_translations(cls, language_code: str) -> List[str]:
        """
        Get list of missing translation keys for a language.
        """
        try:
            language = Language.objects.get(code=language_code, is_active=True)
            default_language = Language.objects.get(is_default=True, is_active=True)
            
            # Get all keys from default language
            default_keys = set(
                Translation.objects.filter(
                    language=default_language,
                    is_approved=True
                ).values_list('key', flat=True)
            )
            
            # Get existing keys for target language
            existing_keys = set(
                Translation.objects.filter(
                    language=language,
                    is_approved=True
                ).values_list('key', flat=True)
            )
            
            # Return missing keys
            return list(default_keys - existing_keys)
            
        except Language.DoesNotExist:
            return []
    
    @classmethod
    def get_translation_progress(cls, language_code: str) -> Dict:
        """
        Get translation progress for a language.
        """
        try:
            language = Language.objects.get(code=language_code, is_active=True)
            default_language = Language.objects.get(is_default=True, is_active=True)
            
            # Count total translations in default language
            total_count = Translation.objects.filter(
                language=default_language,
                is_approved=True
            ).count()
            
            # Count translated items
            translated_count = Translation.objects.filter(
                language=language,
                is_approved=True
            ).count()
            
            # Count fuzzy translations
            fuzzy_count = Translation.objects.filter(
                language=language,
                is_fuzzy=True
            ).count()
            
            # Calculate progress
            progress = (translated_count / total_count * 100) if total_count > 0 else 0
            
            return {
                'language_code': language_code,
                'total_count': total_count,
                'translated_count': translated_count,
                'fuzzy_count': fuzzy_count,
                'missing_count': total_count - translated_count,
                'progress_percentage': round(progress, 2)
            }
            
        except Language.DoesNotExist:
            return {}
    
    @classmethod
    def clear_translation_cache(cls, language_code: str = None):
        """
        Clear translation cache for a language or all languages.
        """
        if language_code:
            # Clear cache for specific language
            cache_patterns = [
                f"translation_{language_code}_*",
                f"all_translations_{language_code}_*"
            ]
        else:
            # Clear all translation cache
            cache_patterns = [
                "translation_*",
                "all_translations_*",
                "supported_languages",
                "default_language"
            ]
        
        # Note: This is a simplified cache clearing
        # In production, you might want to use Redis pattern matching
        for pattern in cache_patterns:
            if '*' not in pattern:
                CacheService.delete(pattern)
    
    @classmethod
    def activate_language(cls, language_code: str) -> bool:
        """
        Activate a language for the current thread.
        """
        try:
            language = Language.objects.get(code=language_code, is_active=True)
            activate(language_code)
            return True
        except Language.DoesNotExist:
            return False
