# Generated by Django 5.2.4 on 2025-07-14 08:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("i18n", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="translationrequest",
            name="assigned_to",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="assigned_translations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="translationrequest",
            name="source_language",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="source_translation_requests",
                to="i18n.language",
            ),
        ),
        migrations.AddField(
            model_name="translationrequest",
            name="target_language",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="target_translation_requests",
                to="i18n.language",
            ),
        ),
        migrations.AddIndex(
            model_name="translation",
            index=models.Index(
                fields=["key", "language"], name="i18n_transl_key_50febf_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="translation",
            index=models.Index(
                fields=["language", "is_approved"],
                name="i18n_transl_languag_c96189_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="translation",
            unique_together={("key", "language", "context")},
        ),
        migrations.AddIndex(
            model_name="translationrequest",
            index=models.Index(
                fields=["status", "priority"], name="i18n_transl_status_1b550b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="translationrequest",
            index=models.Index(
                fields=["target_language", "status"],
                name="i18n_transl_target__93099d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="translationrequest",
            index=models.Index(
                fields=["assigned_to", "status"], name="i18n_transl_assigne_c8fbbd_idx"
            ),
        ),
    ]
