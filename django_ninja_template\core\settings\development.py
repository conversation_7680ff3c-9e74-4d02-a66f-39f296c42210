"""
Django Ninja 模板项目的开发环境配置。
"""
from .base import *

# 安全警告：不要在生产环境中开启调试模式！
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# 开发环境数据库
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# 开发环境缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# 开发环境邮件后端
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# 开发环境特定中间件
MIDDLEWARE += [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
]

# 开发环境特定应用
INSTALLED_APPS += [
    'debug_toolbar',
]

# 调试工具栏配置
INTERNAL_IPS = [
    '127.0.0.1',
]

# 开发环境日志配置
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['loggers']['django']['level'] = 'DEBUG'

# 开发环境 CORS 设置
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# 开发环境安全设置（宽松）
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
