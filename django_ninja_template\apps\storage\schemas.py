"""
Pydantic schemas for storage API.
"""
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID


class StorageFileResponse(BaseModel):
    """Schema for storage file response."""
    id: UUID
    original_name: str
    file_size: int
    content_type: str
    file_hash: str
    is_public: bool
    status: str
    download_count: int
    created_at: datetime
    updated_at: datetime
    download_url: str
    
    class Config:
        from_attributes = True


class FileListResponse(BaseModel):
    """Schema for file list response."""
    files: list[StorageFileResponse]
    total: int
    page: int
    per_page: int
    pages: int


class FileShareResponse(BaseModel):
    """Schema for file share response."""
    share_token: UUID
    expires_at: Optional[datetime] = None
    download_limit: Optional[int] = None
    download_count: int
    is_active: bool
    created_at: datetime
    share_url: str
    
    class Config:
        from_attributes = True


class CreateFileShareRequest(BaseModel):
    """Schema for creating file share."""
    expires_at: Optional[datetime] = None
    download_limit: Optional[int] = None


class FileMetadataResponse(BaseModel):
    """Schema for file metadata response."""
    id: UUID
    original_name: str
    file_size: int
    file_type: str
    mime_type: str
    width: Optional[int] = None
    height: Optional[int] = None
    metadata: Dict[str, Any]
    created_at: datetime
    last_accessed: Optional[datetime] = None
    download_count: int
    
    class Config:
        from_attributes = True


class FileStatsResponse(BaseModel):
    """Schema for file statistics response."""
    total_files: int
    total_size: int
    files_by_type: Dict[str, int]
    storage_usage: Dict[str, Any]


class FileCategorySchema(BaseModel):
    """Schema for file category."""
    id: int
    name: str
    description: str
    allowed_extensions: list[str]
    max_file_size: int
    
    class Config:
        from_attributes = True


class UploadSessionSchema(BaseModel):
    """Schema for upload session."""
    session_id: UUID
    filename: str
    file_size: int
    chunk_size: int
    total_chunks: int
    upload_progress: float
    is_completed: bool
    created_at: datetime
    
    class Config:
        from_attributes = True
