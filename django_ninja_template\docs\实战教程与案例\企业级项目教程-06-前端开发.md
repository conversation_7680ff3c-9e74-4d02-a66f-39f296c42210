# 🎨 企业级项目教程 06 - 前端界面开发

本教程将指导您使用 Vue.js 3 构建现代化的企业级前端界面，包括组件化开发、状态管理、路由配置、与后端 API 交互等核心内容。

## 🎯 本教程目标

完成本教程后，您将：
- ✅ 掌握 Vue.js 3 组合式 API 开发
- ✅ 实现组件化和模块化开发
- ✅ 构建响应式用户界面
- ✅ 实现状态管理和数据流控制
- ✅ 掌握前后端数据交互
- ✅ 了解前端性能优化技巧

## 📋 前置要求

- 完成 [API 接口开发](企业级项目教程-05-API开发.md)
- 了解 JavaScript ES6+ 语法
- 理解 Vue.js 基础概念

## 🏗️ 前端架构设计

### 技术栈选择
```
前端技术栈
├── 🖼️ 框架层 (Framework)
│   ├── Vue.js 3 - 渐进式框架
│   ├── TypeScript - 类型安全
│   ├── Vite - 构建工具
│   └── Pinia - 状态管理
├── 🎨 UI 层 (UI Components)
│   ├── Element Plus - UI 组件库
│   ├── Tailwind CSS - 原子化 CSS
│   ├── Sass/SCSS - CSS 预处理器
│   └── 自定义组件
├── 🔗 网络层 (Network)
│   ├── Axios - HTTP 客户端
│   ├── API 封装
│   ├── 请求拦截器
│   └── 响应处理
├── 🧭 路由层 (Routing)
│   ├── Vue Router 4
│   ├── 路由守卫
│   ├── 动态路由
│   └── 嵌套路由
└── 🛠️ 工具层 (Utils)
    ├── 工具函数
    ├── 常量定义
    ├── 类型定义
    └── 配置文件
```

## 🚀 第一步：项目初始化

### 1.1 创建 Vue 项目

```bash
# 使用 Vite 创建 Vue 项目
npm create vue@latest frontend

# 选择配置选项
✔ Add TypeScript? … Yes
✔ Add JSX Support? … No
✔ Add Vue Router for Single Page Application development? … Yes
✔ Add Pinia for state management? … Yes
✔ Add Vitest for Unit Testing? … Yes
✔ Add an End-to-End Testing Solution? … Playwright
✔ Add ESLint for code quality? … Yes
✔ Add Prettier for code formatting? … Yes

# 进入项目目录
cd frontend

# 安装依赖
npm install

# 安装额外依赖
npm install element-plus @element-plus/icons-vue axios dayjs
npm install -D tailwindcss postcss autoprefixer @types/node
```

### 1.2 配置开发环境

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@api': resolve(__dirname, 'src/api'),
      '@types': resolve(__dirname, 'src/types')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: '../static/frontend',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus'],
          utils: ['axios', 'dayjs']
        }
      }
    }
  }
})
```

```json
// package.json 脚本配置
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "test:unit": "vitest",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "format": "prettier --write src/"
  }
}
```

### 1.3 项目目录结构

```
frontend/
├── 📁 public/                 # 静态资源
├── 📁 src/
│   ├── 📁 api/                # API 接口
│   ├── 📁 components/         # 通用组件
│   │   ├── 📁 common/         # 基础组件
│   │   ├── 📁 layout/         # 布局组件
│   │   └── 📁 business/       # 业务组件
│   ├── 📁 views/              # 页面组件
│   │   ├── 📁 auth/           # 认证页面
│   │   ├── 📁 cms/            # CMS 页面
│   │   └── 📁 dashboard/      # 仪表板
│   ├── 📁 stores/             # 状态管理
│   ├── 📁 router/             # 路由配置
│   ├── 📁 utils/              # 工具函数
│   ├── 📁 types/              # 类型定义
│   ├── 📁 styles/             # 样式文件
│   ├── 📁 assets/             # 资源文件
│   ├── 📄 App.vue             # 根组件
│   └── 📄 main.ts             # 入口文件
├── 📄 index.html              # HTML 模板
├── 📄 vite.config.ts          # Vite 配置
├── 📄 tsconfig.json           # TypeScript 配置
└── 📄 tailwind.config.js      # Tailwind 配置
```

## 🔗 第二步：API 接口封装

### 2.1 HTTP 客户端配置

```typescript
// src/utils/request.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const authStore = useAuthStore()
    
    // 添加认证令牌
    if (authStore.token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${authStore.token}`
      }
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, config } = response
    
    // 计算请求耗时
    if (config.metadata?.startTime) {
      const duration = new Date().getTime() - config.metadata.startTime.getTime()
      console.log(`API ${config.url} took ${duration}ms`)
    }
    
    // 处理业务错误
    if (data.success === false) {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message))
    }
    
    return data
  },
  (error) => {
    const { response } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          const authStore = useAuthStore()
          authStore.logout()
          router.push('/login')
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(response.data?.message || '请求失败')
      }
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

export default service
```

### 2.2 API 接口定义

```typescript
// src/types/api.ts
export interface PaginationParams {
  page?: number
  page_size?: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}

export interface APIResponse<T = any> {
  success: boolean
  message: string
  data?: T
  timestamp: string
}

// 文章相关类型
export interface Article {
  id: number
  title: string
  slug: string
  content: string
  summary?: string
  status: 'draft' | 'published' | 'archived' | 'deleted'
  is_featured: boolean
  is_top: boolean
  view_count: number
  like_count: number
  comment_count: number
  reading_time: number
  published_at?: string
  created_at: string
  updated_at: string
  author: Author
  category?: Category
  tags: Tag[]
  featured_image?: string
}

export interface Author {
  id: number
  username: string
  first_name: string
  last_name: string
  avatar?: string
}

export interface Category {
  id: number
  name: string
  slug: string
  description?: string
  parent_id?: number
  article_count: number
  children: Category[]
}

export interface Tag {
  id: number
  name: string
  slug: string
  color: string
  article_count: number
}

export interface Comment {
  id: number
  content: string
  author: Author
  created_at: string
  like_count: number
  replies: Comment[]
}

// 请求参数类型
export interface ArticleListParams extends PaginationParams {
  category_id?: number
  tag_names?: string
  status?: string
  featured?: boolean
  search?: string
  ordering?: string
}

export interface ArticleCreateData {
  title: string
  slug?: string
  content: string
  summary?: string
  category_id?: number
  tag_names: string[]
  status?: string
  is_featured?: boolean
  is_top?: boolean
  seo_title?: string
  seo_description?: string
  seo_keywords?: string
  featured_image_id?: string
}

export interface CommentCreateData {
  content: string
  parent_id?: number
}
```

```typescript
// src/api/cms.ts
import request from '@/utils/request'
import type {
  Article,
  Category,
  Tag,
  Comment,
  ArticleListParams,
  ArticleCreateData,
  CommentCreateData,
  PaginatedResponse
} from '@/types/api'

export class CMSAPI {
  // 文章相关 API
  static async getArticles(params: ArticleListParams = {}): Promise<PaginatedResponse<Article>> {
    return request.get('/cms/articles', { params })
  }
  
  static async getArticle(id: number): Promise<Article> {
    return request.get(`/cms/articles/${id}`)
  }
  
  static async createArticle(data: ArticleCreateData): Promise<Article> {
    return request.post('/cms/articles', data)
  }
  
  static async updateArticle(id: number, data: Partial<ArticleCreateData>): Promise<Article> {
    return request.put(`/cms/articles/${id}`, data)
  }
  
  static async deleteArticle(id: number): Promise<void> {
    return request.delete(`/cms/articles/${id}`)
  }
  
  static async publishArticle(id: number): Promise<void> {
    return request.post(`/cms/articles/${id}/publish`)
  }
  
  // 分类相关 API
  static async getCategories(): Promise<Category[]> {
    return request.get('/cms/categories')
  }
  
  static async getCategory(id: number): Promise<Category> {
    return request.get(`/cms/categories/${id}`)
  }
  
  static async getCategoryArticles(id: number, params: ArticleListParams = {}): Promise<PaginatedResponse<Article>> {
    return request.get(`/cms/categories/${id}/articles`, { params })
  }
  
  // 标签相关 API
  static async getTags(popular = false): Promise<Tag[]> {
    return request.get('/cms/tags', { params: { popular } })
  }
  
  static async getTagArticles(id: number, params: ArticleListParams = {}): Promise<PaginatedResponse<Article>> {
    return request.get(`/cms/tags/${id}/articles`, { params })
  }
  
  // 评论相关 API
  static async getArticleComments(articleId: number, params: ArticleListParams = {}): Promise<PaginatedResponse<Comment>> {
    return request.get(`/cms/articles/${articleId}/comments`, { params })
  }
  
  static async createComment(articleId: number, data: CommentCreateData): Promise<Comment> {
    return request.post(`/cms/articles/${articleId}/comments`, data)
  }
  
  // 搜索 API
  static async searchArticles(query: string, params: Omit<ArticleListParams, 'search'> = {}): Promise<PaginatedResponse<Article>> {
    return request.get('/cms/search', { params: { q: query, ...params } })
  }
}
```

## 🗂️ 第三步：状态管理

### 3.1 认证状态管理

```typescript
// src/stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userDisplayName = computed(() => {
    if (!user.value) return ''
    return user.value.first_name && user.value.last_name
      ? `${user.value.first_name} ${user.value.last_name}`
      : user.value.username
  })
  
  // 方法
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }
  
  const setUser = (newUser: User) => {
    user.value = newUser
  }
  
  const login = async (email: string, password: string) => {
    isLoading.value = true
    try {
      const response = await AuthAPI.login({ email, password })
      setToken(response.access_token)
      setUser(response.user)
      return response
    } finally {
      isLoading.value = false
    }
  }
  
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
  }
  
  const fetchUserInfo = async () => {
    if (!token.value) return
    
    try {
      const userInfo = await AuthAPI.getUserInfo()
      setUser(userInfo)
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      logout()
    }
  }
  
  return {
    // 状态
    token,
    user,
    isLoading,
    // 计算属性
    isAuthenticated,
    userDisplayName,
    // 方法
    setToken,
    setUser,
    login,
    logout,
    fetchUserInfo
  }
})

## 🧭 第四步：路由配置

### 4.1 路由结构设计

```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { title: '登录', guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { title: '注册', guest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/Index.vue'),
    meta: { title: '仪表板', requiresAuth: true },
    children: [
      {
        path: '',
        name: 'DashboardHome',
        component: () => import('@/views/dashboard/Home.vue'),
        meta: { title: '概览' }
      },
      {
        path: 'articles',
        name: 'ArticleManagement',
        component: () => import('@/views/cms/ArticleList.vue'),
        meta: { title: '文章管理' }
      },
      {
        path: 'articles/create',
        name: 'ArticleCreate',
        component: () => import('@/views/cms/ArticleEdit.vue'),
        meta: { title: '创建文章' }
      },
      {
        path: 'articles/:id/edit',
        name: 'ArticleEdit',
        component: () => import('@/views/cms/ArticleEdit.vue'),
        meta: { title: '编辑文章' },
        props: true
      },
      {
        path: 'categories',
        name: 'CategoryManagement',
        component: () => import('@/views/cms/CategoryList.vue'),
        meta: { title: '分类管理' }
      },
      {
        path: 'tags',
        name: 'TagManagement',
        component: () => import('@/views/cms/TagList.vue'),
        meta: { title: '标签管理' }
      }
    ]
  },
  {
    path: '/articles',
    name: 'ArticleList',
    component: () => import('@/views/cms/PublicArticleList.vue'),
    meta: { title: '文章列表' }
  },
  {
    path: '/articles/:slug',
    name: 'ArticleDetail',
    component: () => import('@/views/cms/ArticleDetail.vue'),
    meta: { title: '文章详情' },
    props: true
  },
  {
    path: '/categories/:id',
    name: 'CategoryArticles',
    component: () => import('@/views/cms/CategoryArticles.vue'),
    meta: { title: '分类文章' },
    props: true
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/cms/SearchResults.vue'),
    meta: { title: '搜索结果' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - Django Ninja CMS` : 'Django Ninja CMS'

  // 检查认证状态
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'Login', query: { redirect: to.fullPath } })
    return
  }

  // 已登录用户访问登录页面时重定向
  if (to.meta.guest && authStore.isAuthenticated) {
    next({ name: 'Dashboard' })
    return
  }

  // 获取用户信息
  if (authStore.token && !authStore.user) {
    try {
      await authStore.fetchUserInfo()
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      authStore.logout()
      if (to.meta.requiresAuth) {
        next({ name: 'Login' })
        return
      }
    }
  }

  next()
})

export default router
```

## 🎨 第五步：组件开发

### 5.1 布局组件

```vue
<!-- src/components/layout/AppLayout.vue -->
<template>
  <div class="app-layout">
    <!-- 顶部导航 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container mx-auto px-4 py-8">
        <router-view />
      </div>
    </main>

    <!-- 底部 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
</script>

<style scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}
</style>
```

```vue
<!-- src/components/layout/AppHeader.vue -->
<template>
  <header class="app-header bg-white shadow-sm border-b">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <img src="/logo.svg" alt="Logo" class="h-8 w-8" />
            <span class="text-xl font-bold text-gray-900">Django Ninja CMS</span>
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <nav class="hidden md:flex space-x-8">
          <router-link
            to="/"
            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            active-class="text-blue-600"
          >
            首页
          </router-link>
          <router-link
            to="/articles"
            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            active-class="text-blue-600"
          >
            文章
          </router-link>
          <router-link
            to="/categories"
            class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            active-class="text-blue-600"
          >
            分类
          </router-link>
        </nav>

        <!-- 搜索框 -->
        <div class="flex-1 max-w-lg mx-8">
          <SearchBox />
        </div>

        <!-- 用户菜单 -->
        <div class="flex items-center space-x-4">
          <template v-if="authStore.isAuthenticated">
            <el-dropdown @command="handleUserMenuCommand">
              <div class="flex items-center space-x-2 cursor-pointer">
                <el-avatar :size="32" :src="authStore.user?.avatar">
                  {{ authStore.userDisplayName.charAt(0) }}
                </el-avatar>
                <span class="text-sm font-medium text-gray-700">
                  {{ authStore.userDisplayName }}
                </span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="dashboard">
                    <el-icon><User /></el-icon>
                    仪表板
                  </el-dropdown-item>
                  <el-dropdown-item command="profile">
                    <el-icon><Setting /></el-icon>
                    个人设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <router-link
              to="/login"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            >
              登录
            </router-link>
            <router-link
              to="/register"
              class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
            >
              注册
            </router-link>
          </template>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <el-button @click="mobileMenuOpen = !mobileMenuOpen" text>
            <el-icon><Menu /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <div v-show="mobileMenuOpen" class="md:hidden border-t border-gray-200 py-4">
        <div class="space-y-2">
          <router-link
            to="/"
            class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900"
            @click="mobileMenuOpen = false"
          >
            首页
          </router-link>
          <router-link
            to="/articles"
            class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900"
            @click="mobileMenuOpen = false"
          >
            文章
          </router-link>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { ArrowDown, User, Setting, SwitchButton, Menu } from '@element-plus/icons-vue'
import SearchBox from '@/components/common/SearchBox.vue'

const router = useRouter()
const authStore = useAuthStore()
const mobileMenuOpen = ref(false)

const handleUserMenuCommand = (command: string) => {
  switch (command) {
    case 'dashboard':
      router.push('/dashboard')
      break
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      authStore.logout()
      ElMessage.success('已退出登录')
      router.push('/')
      break
  }
}
</script>
```

### 5.2 业务组件

```vue
<!-- src/components/business/ArticleCard.vue -->
<template>
  <div class="article-card bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
    <!-- 特色图片 -->
    <div v-if="article.featured_image" class="aspect-w-16 aspect-h-9">
      <img
        :src="article.featured_image"
        :alt="article.title"
        class="w-full h-48 object-cover rounded-t-lg"
      />
    </div>

    <div class="p-6">
      <!-- 标签 -->
      <div v-if="article.tags.length > 0" class="flex flex-wrap gap-2 mb-3">
        <el-tag
          v-for="tag in article.tags"
          :key="tag.id"
          :color="tag.color"
          size="small"
          class="cursor-pointer"
          @click="$emit('tag-click', tag)"
        >
          {{ tag.name }}
        </el-tag>
      </div>

      <!-- 标题 -->
      <h3 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
        <router-link
          :to="{ name: 'ArticleDetail', params: { slug: article.slug } }"
          class="hover:text-blue-600 transition-colors"
        >
          {{ article.title }}
        </router-link>
      </h3>

      <!-- 摘要 -->
      <p v-if="article.summary" class="text-gray-600 mb-4 line-clamp-3">
        {{ article.summary }}
      </p>

      <!-- 元信息 -->
      <div class="flex items-center justify-between text-sm text-gray-500">
        <div class="flex items-center space-x-4">
          <!-- 作者 -->
          <div class="flex items-center space-x-2">
            <el-avatar :size="24" :src="article.author.avatar">
              {{ article.author.username.charAt(0) }}
            </el-avatar>
            <span>{{ article.author.username }}</span>
          </div>

          <!-- 发布时间 -->
          <span>{{ formatDate(article.published_at || article.created_at) }}</span>

          <!-- 阅读时间 -->
          <span>{{ article.reading_time }} 分钟阅读</span>
        </div>

        <!-- 统计信息 -->
        <div class="flex items-center space-x-4">
          <span class="flex items-center space-x-1">
            <el-icon><View /></el-icon>
            <span>{{ article.view_count }}</span>
          </span>
          <span class="flex items-center space-x-1">
            <el-icon><ChatDotRound /></el-icon>
            <span>{{ article.comment_count }}</span>
          </span>
          <span class="flex items-center space-x-1">
            <el-icon><Star /></el-icon>
            <span>{{ article.like_count }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { View, ChatDotRound, Star } from '@element-plus/icons-vue'
import type { Article, Tag } from '@/types/api'
import { formatDate } from '@/utils/date'

interface Props {
  article: Article
}

interface Emits {
  (e: 'tag-click', tag: Tag): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
```

```vue
<!-- src/components/common/SearchBox.vue -->
<template>
  <div class="search-box relative">
    <el-input
      v-model="searchQuery"
      placeholder="搜索文章..."
      class="search-input"
      @keyup.enter="handleSearch"
      @input="handleInput"
    >
      <template #prefix>
        <el-icon><Search /></el-icon>
      </template>
      <template #suffix>
        <el-button
          v-if="searchQuery"
          text
          @click="clearSearch"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </template>
    </el-input>

    <!-- 搜索建议 -->
    <div
      v-if="showSuggestions && suggestions.length > 0"
      class="suggestions absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-50 mt-1"
    >
      <div
        v-for="(suggestion, index) in suggestions"
        :key="index"
        class="suggestion-item px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
        @click="selectSuggestion(suggestion)"
      >
        <div class="flex items-center space-x-2">
          <el-icon><Search /></el-icon>
          <span>{{ suggestion }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Close } from '@element-plus/icons-vue'
import { debounce } from '@/utils/debounce'

const router = useRouter()
const searchQuery = ref('')
const suggestions = ref<string[]>([])
const showSuggestions = ref(false)

// 模拟搜索建议数据
const mockSuggestions = [
  'Vue.js 教程',
  'Django 开发',
  'TypeScript 入门',
  'API 设计',
  '前端优化'
]

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      name: 'Search',
      query: { q: searchQuery.value.trim() }
    })
    showSuggestions.value = false
  }
}

const handleInput = debounce((value: string) => {
  if (value.trim()) {
    // 模拟搜索建议
    suggestions.value = mockSuggestions.filter(item =>
      item.toLowerCase().includes(value.toLowerCase())
    )
    showSuggestions.value = suggestions.value.length > 0
  } else {
    showSuggestions.value = false
  }
}, 300)

const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  handleSearch()
}

const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
}

// 点击外部关闭建议
watch(showSuggestions, (show) => {
  if (show) {
    const handleClickOutside = (event: Event) => {
      const target = event.target as Element
      if (!target.closest('.search-box')) {
        showSuggestions.value = false
        document.removeEventListener('click', handleClickOutside)
      }
    }
    document.addEventListener('click', handleClickOutside)
  }
})
</script>

<style scoped>
.search-input {
  width: 100%;
}

.suggestions {
  max-height: 300px;
  overflow-y: auto;
}
</style>
```

## ✅ 完成检查

完成本教程后，您应该能够：

- [ ] 搭建完整的 Vue.js 3 开发环境
- [ ] 实现组件化和模块化开发
- [ ] 构建响应式用户界面
- [ ] 实现状态管理和数据流控制
- [ ] 掌握前后端数据交互
- [ ] 配置路由和导航
- [ ] 开发可复用的业务组件

## 📖 下一步

恭喜！您已经构建了一个现代化的企业级前端界面。

**接下来学习**：
- [系统集成测试](企业级项目教程-07-测试部署.md) - 完整的测试和部署流程

**相关文档**：
- [开发指南](开发指南.md) - 前端开发最佳实践
- [项目结构说明](项目结构说明.md) - 前端项目结构详解

---

**🎉 前端界面开发完成！** 您现在拥有了现代化的企业级前端界面，支持响应式设计、组件化开发和高效的用户体验。
```

### 3.2 CMS 状态管理

```typescript
// src/stores/cms.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { CMSAPI } from '@/api/cms'
import type { Article, Category, Tag, ArticleListParams } from '@/types/api'

export const useCMSStore = defineStore('cms', () => {
  // 文章状态
  const articles = ref<Article[]>([])
  const currentArticle = ref<Article | null>(null)
  const articlesLoading = ref(false)
  const articlesPagination = ref({
    total: 0,
    page: 1,
    page_size: 20,
    total_pages: 0,
    has_next: false,
    has_prev: false
  })
  
  // 分类和标签状态
  const categories = ref<Category[]>([])
  const tags = ref<Tag[]>([])
  const categoriesLoading = ref(false)
  const tagsLoading = ref(false)
  
  // 计算属性
  const publishedArticles = computed(() => 
    articles.value.filter(article => article.status === 'published')
  )
  
  const featuredArticles = computed(() =>
    publishedArticles.value.filter(article => article.is_featured)
  )
  
  const topArticles = computed(() =>
    publishedArticles.value.filter(article => article.is_top)
  )
  
  // 文章相关方法
  const fetchArticles = async (params: ArticleListParams = {}) => {
    articlesLoading.value = true
    try {
      const response = await CMSAPI.getArticles(params)
      articles.value = response.items
      articlesPagination.value = {
        total: response.total,
        page: response.page,
        page_size: response.page_size,
        total_pages: response.total_pages,
        has_next: response.has_next,
        has_prev: response.has_prev
      }
    } finally {
      articlesLoading.value = false
    }
  }
  
  const fetchArticle = async (id: number) => {
    try {
      currentArticle.value = await CMSAPI.getArticle(id)
      return currentArticle.value
    } catch (error) {
      currentArticle.value = null
      throw error
    }
  }
  
  const createArticle = async (data: ArticleCreateData) => {
    const article = await CMSAPI.createArticle(data)
    articles.value.unshift(article)
    return article
  }
  
  const updateArticle = async (id: number, data: Partial<ArticleCreateData>) => {
    const updatedArticle = await CMSAPI.updateArticle(id, data)
    const index = articles.value.findIndex(article => article.id === id)
    if (index !== -1) {
      articles.value[index] = updatedArticle
    }
    if (currentArticle.value?.id === id) {
      currentArticle.value = updatedArticle
    }
    return updatedArticle
  }
  
  const deleteArticle = async (id: number) => {
    await CMSAPI.deleteArticle(id)
    articles.value = articles.value.filter(article => article.id !== id)
    if (currentArticle.value?.id === id) {
      currentArticle.value = null
    }
  }
  
  // 分类相关方法
  const fetchCategories = async () => {
    categoriesLoading.value = true
    try {
      categories.value = await CMSAPI.getCategories()
    } finally {
      categoriesLoading.value = false
    }
  }
  
  // 标签相关方法
  const fetchTags = async (popular = false) => {
    tagsLoading.value = true
    try {
      tags.value = await CMSAPI.getTags(popular)
    } finally {
      tagsLoading.value = false
    }
  }
  
  // 搜索方法
  const searchArticles = async (query: string, params: Omit<ArticleListParams, 'search'> = {}) => {
    articlesLoading.value = true
    try {
      const response = await CMSAPI.searchArticles(query, params)
      articles.value = response.items
      articlesPagination.value = {
        total: response.total,
        page: response.page,
        page_size: response.page_size,
        total_pages: response.total_pages,
        has_next: response.has_next,
        has_prev: response.has_prev
      }
    } finally {
      articlesLoading.value = false
    }
  }
  
  return {
    // 状态
    articles,
    currentArticle,
    articlesLoading,
    articlesPagination,
    categories,
    tags,
    categoriesLoading,
    tagsLoading,
    // 计算属性
    publishedArticles,
    featuredArticles,
    topArticles,
    // 方法
    fetchArticles,
    fetchArticle,
    createArticle,
    updateArticle,
    deleteArticle,
    fetchCategories,
    fetchTags,
    searchArticles
  }
})
```
