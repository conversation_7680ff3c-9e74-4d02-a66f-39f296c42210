"""
Core Celery tasks.
"""
import logging
from datetime import timed<PERSON>ta
from django.utils import timezone
from celery import shared_task
from .models import AuditLog

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def cleanup_old_logs(self):
    """
    Clean up old audit logs (older than 90 days).
    """
    try:
        cutoff_date = timezone.now() - timedelta(days=90)
        deleted_count, _ = AuditLog.objects.filter(created_at__lt=cutoff_date).delete()
        
        logger.info(f"Cleaned up {deleted_count} old audit logs")
        return f"Cleaned up {deleted_count} old audit logs"
        
    except Exception as exc:
        logger.error(f"Error cleaning up old logs: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True)
def create_audit_log(self, user_id, action, resource_type, resource_id=None, details=None, ip_address=None, user_agent=None):
    """
    Create an audit log entry asynchronously.
    """
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user = None
        if user_id:
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                pass
        
        AuditLog.objects.create(
            user=user,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id or '',
            details=details or {},
            ip_address=ip_address,
            user_agent=user_agent or ''
        )
        
        logger.info(f"Created audit log: {action} on {resource_type}")
        return "Audit log created successfully"
        
    except Exception as exc:
        logger.error(f"Error creating audit log: {exc}")
        raise


@shared_task(bind=True)
def send_notification_email(self, user_id, subject, message, email_template=None):
    """
    Send notification email to user.
    """
    try:
        from django.contrib.auth import get_user_model
        from django.core.mail import send_mail
        from django.conf import settings
        
        User = get_user_model()
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return "User not found"
        
        # Send email
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )
        
        logger.info(f"Sent notification email to {user.email}")
        return f"Email sent to {user.email}"
        
    except Exception as exc:
        logger.error(f"Error sending notification email: {exc}")
        raise self.retry(exc=exc, countdown=60, max_retries=3)


@shared_task(bind=True)
def process_bulk_operation(self, operation_type, data):
    """
    Process bulk operations asynchronously.
    """
    try:
        logger.info(f"Starting bulk operation: {operation_type}")
        
        # This is a placeholder for bulk operations
        # You can implement specific bulk operations based on operation_type
        
        if operation_type == "bulk_user_update":
            # Example: bulk update users
            processed_count = len(data.get('user_ids', []))
            
        elif operation_type == "bulk_data_export":
            # Example: bulk data export
            processed_count = 1
            
        else:
            raise ValueError(f"Unknown operation type: {operation_type}")
        
        logger.info(f"Completed bulk operation: {operation_type}, processed {processed_count} items")
        return f"Processed {processed_count} items"
        
    except Exception as exc:
        logger.error(f"Error in bulk operation {operation_type}: {exc}")
        raise self.retry(exc=exc, countdown=60, max_retries=3)


@shared_task(bind=True)
def health_check_task(self):
    """
    Health check task to verify Celery is working.
    """
    try:
        from django.db import connection
        
        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        logger.info("Celery health check passed")
        return "Health check passed"
        
    except Exception as exc:
        logger.error(f"Celery health check failed: {exc}")
        raise


@shared_task(bind=True)
def cache_warmup_task(self):
    """
    Warm up cache with frequently accessed data.
    """
    try:
        from .services import CacheService
        from apps.permissions.models import Permission, Role
        
        # Cache all permissions
        permissions = list(Permission.objects.all())
        for permission in permissions:
            CacheService.cache_model_instance(permission, timeout=3600)
        
        # Cache all roles
        roles = list(Role.objects.filter(is_active=True))
        for role in roles:
            CacheService.cache_model_instance(role, timeout=3600)
        
        logger.info(f"Cache warmed up: {len(permissions)} permissions, {len(roles)} roles")
        return f"Cache warmed up: {len(permissions)} permissions, {len(roles)} roles"
        
    except Exception as exc:
        logger.error(f"Error warming up cache: {exc}")
        raise self.retry(exc=exc, countdown=60)
