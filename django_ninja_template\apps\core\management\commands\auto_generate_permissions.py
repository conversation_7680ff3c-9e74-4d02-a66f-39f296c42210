"""
自动生成权限管理命令
"""
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from apps.core.permissions.auto_permissions import PermissionAutoGenerator, auto_generate_app_permissions
import json
from pathlib import Path

User = get_user_model()


class Command(BaseCommand):
    help = '自动生成应用权限和角色'
    
    def add_arguments(self, parser):
        parser.add_argument('app_name', type=str, help='应用名称')
        parser.add_argument('--create-roles', action='store_true', help='同时创建标准角色')
        parser.add_argument('--models', type=str, help='指定模型名称列表 (用逗号分隔)')
        parser.add_argument('--config', type=str, help='自定义权限配置文件路径')
        parser.add_argument('--export', type=str, help='导出权限配置到文件')
        parser.add_argument('--import', type=str, help='从文件导入权限配置', dest='import_file')
        parser.add_argument('--cleanup', action='store_true', help='清理未使用的权限')
        parser.add_argument('--reset', action='store_true', help='重置权限（删除后重新创建）')
        parser.add_argument('--assign-user', type=str, help='为用户分配管理员角色 (用户邮箱)')
    
    def handle(self, *args, **options):
        app_name = options['app_name']
        generator = PermissionAutoGenerator()
        
        # 导出权限配置
        if options['export']:
            self.export_permissions(generator, app_name, options['export'])
            return
        
        # 导入权限配置
        if options['import_file']:
            self.import_permissions(generator, options['import_file'])
            return
        
        # 清理未使用的权限
        if options['cleanup']:
            count = generator.cleanup_unused_permissions(app_name)
            self.stdout.write(self.style.SUCCESS(f'清理了 {count} 个未使用的权限'))
            return
        
        # 重置权限
        if options['reset']:
            self.reset_permissions(generator, app_name)
        
        # 解析模型列表
        models_list = None
        if options['models']:
            models_list = [model.strip() for model in options['models'].split(',')]
        
        # 处理自定义配置
        custom_permissions = []
        custom_roles = []
        if options['config']:
            custom_permissions, custom_roles = self.load_custom_config(options['config'])
        
        # 生成标准权限
        self.stdout.write(f'正在为应用 {app_name} 生成权限...')
        permissions = generator.scan_and_generate_permissions(app_name, models_list)
        self.stdout.write(f'生成了 {len(permissions)} 个权限')
        
        # 创建自定义权限
        if custom_permissions:
            custom_perms = generator.create_custom_permissions(custom_permissions)
            self.stdout.write(f'创建了 {len(custom_perms)} 个自定义权限')
            permissions.extend(custom_perms)
        
        # 创建角色
        roles = []
        if options['create_roles']:
            roles = generator.create_standard_roles(app_name, permissions)
            self.stdout.write(f'创建了 {len(roles)} 个标准角色')
        
        # 创建自定义角色
        if custom_roles:
            custom_role_objs = generator.create_custom_roles(custom_roles)
            self.stdout.write(f'创建了 {len(custom_role_objs)} 个自定义角色')
            roles.extend(custom_role_objs)
        
        # 为用户分配角色
        if options['assign_user']:
            self.assign_user_role(generator, options['assign_user'], app_name)
        
        # 显示总结
        self.show_summary(app_name, permissions, roles)
        
        self.stdout.write(self.style.SUCCESS(f'应用 {app_name} 的权限配置完成！'))
    
    def export_permissions(self, generator: PermissionAutoGenerator, app_name: str, export_path: str):
        """导出权限配置"""
        config = generator.export_permissions_config(app_name)
        
        export_file = Path(export_path)
        export_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(export_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        self.stdout.write(self.style.SUCCESS(f'权限配置已导出到: {export_path}'))
        self.stdout.write(f'导出了 {len(config["permissions"])} 个权限和 {len(config["roles"])} 个角色')
    
    def import_permissions(self, generator: PermissionAutoGenerator, import_path: str):
        """导入权限配置"""
        import_file = Path(import_path)
        
        if not import_file.exists():
            raise CommandError(f'配置文件不存在: {import_path}')
        
        with open(import_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        stats = generator.import_permissions_config(config)
        
        self.stdout.write(self.style.SUCCESS('权限配置导入完成！'))
        self.stdout.write(f'导入了 {stats["permissions"]} 个权限和 {stats["roles"]} 个角色')
    
    def reset_permissions(self, generator: PermissionAutoGenerator, app_name: str):
        """重置权限"""
        from apps.permissions.models import Permission, Role
        
        # 删除应用相关的权限和角色
        app_permissions = Permission.objects.filter(codename__startswith=f'{app_name}.')
        app_roles = Role.objects.filter(name__icontains=app_name.replace('_', ' ').title())
        
        perm_count = app_permissions.count()
        role_count = app_roles.count()
        
        app_permissions.delete()
        app_roles.delete()
        
        self.stdout.write(f'删除了 {perm_count} 个权限和 {role_count} 个角色')
    
    def load_custom_config(self, config_path: str) -> tuple:
        """加载自定义配置"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            # 尝试在应用目录中查找
            app_config_file = Path(f'apps/{config_path}')
            if app_config_file.exists():
                config_file = app_config_file
            else:
                raise CommandError(f'配置文件不存在: {config_path}')
        
        if config_file.suffix == '.json':
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            # 尝试导入 Python 模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("config", config_file)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            config = {
                'custom_permissions': getattr(config_module, 'CUSTOM_PERMISSIONS', []),
                'custom_roles': getattr(config_module, 'CUSTOM_ROLES', []),
            }
        
        return config.get('custom_permissions', []), config.get('custom_roles', [])
    
    def assign_user_role(self, generator: PermissionAutoGenerator, user_email: str, app_name: str):
        """为用户分配角色"""
        try:
            user = User.objects.get(email=user_email)
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'用户不存在: {user_email}'))
            return
        
        app_display_name = app_name.replace('_', ' ').title()
        admin_role_name = f'{app_display_name} 管理员'
        
        success = generator.assign_role_to_user(user, admin_role_name)
        
        if success:
            self.stdout.write(self.style.SUCCESS(f'为用户 {user_email} 分配了角色: {admin_role_name}'))
        else:
            self.stdout.write(self.style.WARNING(f'用户 {user_email} 已拥有角色: {admin_role_name}'))
    
    def show_summary(self, app_name: str, permissions: list, roles: list):
        """显示总结信息"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f'应用: {app_name}')
        self.stdout.write('='*50)
        
        if permissions:
            self.stdout.write('\n权限列表:')
            for perm in permissions:
                self.stdout.write(f'  ✓ {perm.name} ({perm.codename})')
        
        if roles:
            self.stdout.write('\n角色列表:')
            for role in roles:
                perm_count = role.permissions.count()
                self.stdout.write(f'  ✓ {role.name} ({perm_count} 个权限)')
        
        self.stdout.write('\n下一步建议:')
        self.stdout.write(f'  1. 检查生成的权限和角色是否符合需求')
        self.stdout.write(f'  2. 为用户分配适当的角色')
        self.stdout.write(f'  3. 在 API 中使用 @require_permission 装饰器')
        self.stdout.write(f'  4. 测试权限控制是否正常工作')
        
        # 显示使用示例
        self.stdout.write('\n使用示例:')
        self.stdout.write(f'  # 为用户分配角色')
        self.stdout.write(f'  python manage.py auto_generate_permissions {app_name} --assign-user <EMAIL>')
        self.stdout.write(f'  ')
        self.stdout.write(f'  # 导出权限配置')
        self.stdout.write(f'  python manage.py auto_generate_permissions {app_name} --export {app_name}_permissions.json')
        self.stdout.write(f'  ')
        self.stdout.write(f'  # 清理未使用的权限')
        self.stdout.write(f'  python manage.py auto_generate_permissions {app_name} --cleanup')
